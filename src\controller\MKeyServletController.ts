import * as express from 'express';
import { Request, Response } from 'express';
import { Controller, StringRecord } from '../types';
import LoggerCenter from '../logger/LoggerCenter';
import path from 'path';
import { ProcessInfoType, sqExceptionResult, systemLine } from '../serviceQuery';
import ServiceCenter from '../service/ServiceCenter';
import { getUserProfile, toParameters } from './web-utils';
import { SYSTEM } from '../service/constants';
import { KeyProcessor2 } from '../service/lib/mobile/KeyProcessor2';
import pako from 'pako';

const logger = LoggerCenter.getLogger(path.basename(__filename));
const CONTROLLER_NAME = 'MKeyServletController';

export default class MKeyServletController implements Controller {
  public readonly name = CONTROLLER_NAME;
  public readonly paths = ['/api/mobile/getkeys2'];
  public readonly router = express.Router();

  constructor() {
    this.router.post('/*', processHttpRequest);
    this.router.get('/*', processHttpRequest);
  }
}

async function processHttpRequest(req: Request, res: Response) {
  try {
    const processInfo: ProcessInfoType = { lines: [] };
    processInfo.lines.push(systemLine('start MKeyServlet.get '));
    const sq = ServiceCenter.getInstance().getSq();
    const userProfile = await getUserProfile(sq, req);
    if (!userProfile) {
      res.json(sqExceptionResult('Could not create user profile!'));
      return;
    }
    const { userId } = userProfile;

    // const rolesWithSystem = [...roles, SYSTEM];
    const parameters = toParameters(req, userId);
    const mVersion = parameters['mVersion'] || 'noversion';
    const compression = parameters['compression'] || '';
    logger.info(`IESPKeyServlet Parameters : ${parameters}`);
    // useBase64: not used by iPad

    const name = parameters['name'];

    logger.info(`origin=MKeyServlet; userId:${userId}; name=${name}; mVersion=${mVersion}`);
    await sq.run({ serviceId: 'm.setMVersion', parameters: { mVersion }, userId: SYSTEM, roles: [SYSTEM] });

    const kp: KeyProcessor2 = new KeyProcessor2(userProfile);
    kp.setName(name);

    kp.setSourcesOnly(parameters['sourcesOnly'] === 'true');
    // useCached: not needed anymore
    // dumpDir: not needed anymore
    // outputMode: not needed anymore

    const outParameters: StringRecord = {};
    const keyDocument = await kp.processKeydocumentAsPagingResult(parameters, outParameters);

    const output = parameters['output'];
    //
    // output == sha1
    //

    // sha1: never requested by iPad

    const fileTidListOnly = 'fileTidListOnly' === output;
    if (fileTidListOnly) {
      res.json({ fileTidList: outParameters['fileTidList'] });
      return;
    }
    if (compression === 'gzip') {
      const s = JSON.stringify(keyDocument);
      const compressedData = pako.gzip(s);
      res.set('Content-Type', 'application/octet-stream'); // Important header
      // variante code decompressing: res.send(compressedData);
      res.set('Content-Encoding', 'gzip'); // If applicable (e.g., pako compression)
      res.send(Buffer.from(compressedData));
    } else {
      res.json(keyDocument);
    }
  } catch (e) {
    logger.error(`${CONTROLLER_NAME} ${e}`);
    res.sendStatus(500);
  }
}
