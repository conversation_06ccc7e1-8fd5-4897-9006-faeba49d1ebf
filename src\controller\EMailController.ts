import * as express from 'express';
import { Request, Response } from 'express';
import { Controller } from '../types';
import { send } from '../service/lib/EMailService';
import LoggerCenter from '../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));
const CONTROLLER_NAME = 'email';

export default class EMailController implements Controller {
  public readonly name = CONTROLLER_NAME;
  public readonly paths = [`/api/${CONTROLLER_NAME}`];
  public readonly router = express.Router();

  constructor() {
    this.router.get('/*', processHttpRequest);
  }
}

async function processHttpRequest(req: Request, res: Response) {
  // console.info('processHttpRequest: EMailController');
  try {
    const result = await send(
      ['<EMAIL>', '<EMAIL>'],
      '<EMAIL>', // Change to your verified sender
      'Sending with SendGrid is Fun',
      'This email is from SendGrid with TypeScript.'
    );
    res.json(result);
  } catch (e) {
    logger.error(`${CONTROLLER_NAME} ${e}`);
    res.sendStatus(500);
  }
}
