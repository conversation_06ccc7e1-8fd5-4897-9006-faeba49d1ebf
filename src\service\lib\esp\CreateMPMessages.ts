import { SqFunctionArg, Sq<PERSON><PERSON><PERSON>unction, SqResult, toList } from '../../../serviceQuery';
import { $REQUESTID, SYSTEM } from '../../constants';
import ServiceCenter from '../../ServiceCenter';
import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));
type MpMessage = {
  //        public String messageTid;
  logTimestamp: string;
  userId: string;
  messageType: string;
  message: string;
};
export const CreateMPMessages: SqNodeFunction = async({ request }: SqFunctionArg): Promise<SqResult> => {
  logger.debug('start CreateMPMessages');
  const rid = request.parameters[$REQUESTID];
  const sq = ServiceCenter.getInstance().getSq();
  if (!rid) {
    logger.error(`${$REQUESTID} not available, no messages produced!`);
  } else {
    const res = await sq.run({
      serviceId: 'MP_MESSAGE.get',
      roles: request.roles,
      parameters: { ...request.parameters, messageTid: rid }
    });
    const list = toList<MpMessage>(res);
    const writeLog = list.filter(({ messageType }) => messageType === 'E').length === 0;
    if (writeLog) {
      await sq.run({
        serviceId: 'MP_TYPE.log',
        userId: SYSTEM,
        roles: [SYSTEM],
        parameters: { ...request.parameters }
      });
    }
  }
  return {};
};
