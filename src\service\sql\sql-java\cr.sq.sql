-- KEY 		= cr_read_access

select u.user_id,
       u.last_name,
       u.first_name,
       case when ac.access_right_tid_min = 4 then 'read' end                                       r1,
       case when ac.access_right_tid = 6 then 'admin' end                                          r2,
       case when ac.ACCESS_RIGHT_TID = 0 then '0' else '1' end                                     access_ok,
       case when ((ac.access_right_tid = 0) or (ac.user_id = ESP.user_code())) then '0' else '1' end Enabled,
       case when ro.user_id is null then 'false' else 'true' end                                   readonly
from (select type_tid, user_id, max(access_right_tid) access_right_tid, min(access_right_tid_min) access_right_tid_min
      from esp.v_mp_access_type
      where type_tid = :in_meeting_type_tid
      group by type_tid, user_id) ac
         inner join ESP.V_SR_USER u on u.user_id = ac.user_id
         inner join esp.t_mp_meeting m on m.type_tid = ac.type_tid and m.meeting_tid = :in_meeting_tid
         left join esp.t_cr_readonly ro on ro.meeting_tid = m.meeting_tid and ro.user_id = ac.user_id
;




-- KEY 		= cr_get_excel_data

select mt.type_tid                     meeting_type,
       tp.name                         meeting_type_name,
       mt.meeting_tid                  meeting_id,
       mt.name                         meeting_name,
       vt.user_id                      vote_user_id,
       vt.first_name                   vote_user_first,
       vt.last_name                    vote_user_last,
       vt.answer,
       vt.cmt,
       vt.edited                       edit_time_date,
       vt.edited_by                    edit_user,
       vt.edited_first_name            edit_user_first,
       vt.edited_last_name             edit_user_last,
       (SELECT STRING_AGG(filename, ', ' ORDER BY ord) AS description
        FROM esp.t_mp_file_order fo
        where fo.agenda_tid = vt.agenda_tid
        GROUP BY agenda_tid)           documents,
       (SELECT STRING_AGG(fi.filename, ', ' ORDER BY answer_tid) AS description
        FROM esp.t_cr_answer_evidence ev
                 inner join esp.t_file fi on fi.file_tid = ev.file_tid
        where ev.question_tid = vt.question_tid
          and ev.user_id = vt.user_id
        GROUP BY question_tid)         evidences,
       '?meetingId=' || vt.meeting_tid meeting_link
from esp.v_cr_vote vt
         inner join esp.t_mp_meeting mt on mt.meeting_tid = vt.meeting_tid
         inner join esp.v_mp_access_meeting_all acc
                    on acc.meeting_tid = mt.meeting_tid
                        and acc.user_id = esp.user_code()
         inner join esp.t_mp_type tp on tp.type_tid = mt.type_tid
         left join ESP.T_CR_READONLY ro on ro.meeting_tid = mt.meeting_tid and ro.user_id = vt.user_id
where vt.meeting_tid = :in_meeting_tid
  and ro.user_id is null
  and acc.access_right_tid = 6
order by vt.edited
;


-- KEY = cr_question_delete

call ESP.sp_cr_question_delete(:in_question_tid)
;


-- KEY = mp_delete_meeting

call ESP.sp_mp_delete_meeting(:in_meeting_tid)
;


-- KEY 		= meeting_list_current_by_type
set-if-empty:timeformat=YYYYMMDDHH24MI
;
select v.mtgroup_tid,
       v.mtgroup_name,
       v.type_tid,
       v.type_name,
       v.meeting_tid,
       v.meeting_name,
       v.status_tid,
       v.location_city,
       v.location_address,
       v.location_room,
       to_char(v.time_start, :timeformat) as time_start,
       to_char(v.time_end, :timeformat)   as time_end,
       v.access_right_tid,
       v.agenda_count,
       v.unread,
       v.cmt_on,
       v.item_type,
       v.answer,
       v.response,
       v.invisible_count_agenda,
       v.response_count,
       v.mtgroup_ord,
       v.type_ord
from esp.v_mp_meeting_list_current v
where v.meeting_name is not null
--order by mtgroup_ord, type_ord, type_name, time_start, meeting_tid
;


-- KEY 		= meeting_list_published_by_type
set-if-empty:timeformat=YYYYMMDDHH24MI
;
select mtgroup_tid,
       mtgroup_name,
       type_tid,
       type_name,
       v.meeting_tid,
       meeting_name,
       status_tid,
       location_city,
       location_address,
       location_room,
       to_char(time_start, :timeformat) as time_start,
       to_char(time_end, :timeformat)   as time_end,
       access_right_tid,
       agenda_count,
       unread,
       cmt_on,
       item_type,
       answer,
       response,
       readonly,
       mtgroup_ord,
       type_ord
from esp.v_mp_meeting_list_all v
where status_tid = 10
  and status_tid is not null
--order by mtgroup_ord, type_ord, time_start, meeting_tid
;




-- KEY 		= zz-meeting_list_archived_by_type
set-if-empty:timeformat=YYYYMMDDHH24MI
;
select mtgroup_tid,
       mtgroup_name,
       type_tid,
       type_name,
       meeting_tid,
       meeting_name,
       status_tid,
       location_city,
       location_address,
       location_room,
       to_char(time_start, :timeformat) as time_start,
       to_char(time_end, :timeformat)   as time_end,
       access_right_tid,
       agenda_count,
       null                                  as unread,
       cmt_on,
       item_type,
       'NA'                                  as answer,
       response,
       mtgroup_ord,
       type_ord,
       unread
from esp.v_mp_meeting_list_all
where status_tid = 11
  and status_tid is not null
--order by mtgroup_ord, type_ord, type_name, time_start desc, meeting_tid desc
;


-- KEY 		= meeting_list_archived_by_type_admin
set-if-empty:timeformat=YYYYMMDDHH24MI
;
select mtgroup_tid,
       mtgroup_name,
       type_tid,
       type_name,
       v.meeting_tid,
       meeting_name,
       status_tid,
       location_city,
       location_address,
       location_room,
       to_char(time_start, :timeformat) as time_start,
       to_char(time_end, :timeformat)   as time_end,
       access_right_tid,
       agenda_count,
       unread,
       cmt_on,
       v.item_type,
       answer,
       ''                                   as  response,
       mtgroup_ord,
       type_ord
from esp.v_mp_meeting_list_all v
         left join
     (select a.*,
             dense_rank()
                     over (partition by meeting_tid order by ord) as drank
      from esp.t_mp_agenda a) ag on ag.meeting_tid = v.meeting_tid and ag.drank = 1
         left outer join esp.t_cr_mp_link li on li.meeting_item_tid = ag.agenda_tid
         LEFT OUTER JOIN (SELECT question_tid, COUNT(*) COUNT
                          FROM esp.v_cr_answer
                          GROUP BY question_tid) answer_count
                         ON answer_count.question_tid = li.question_tid
where status_tid = 11
  and access_right_tid = 6
  and status_tid is not null
--order by mtgroup_ord, type_ord, type_name, time_start desc, meeting_tid desc
;


-- KEY 		= meeting_list_published_by_date
set-if-empty:timeformat=YYYYMMDDHH24MI
;
select mtgroup_tid,
       mtgroup_name,
       type_tid,
       type_name,
       v.meeting_tid,
       meeting_name,
       status_tid,
       location_city,
       location_address,
       location_room,
       to_char(time_start, :timeformat) as time_start,
       to_char(time_end, :timeformat)   as time_end,
       access_right_tid,
       agenda_count,
       unread,
       cmt_on,
       item_type,
       answer,
       response,
       readonly
from esp.v_mp_meeting_list_all v
where status_tid = 10
  and status_tid is not null
--order by time_start, mtgroup_ord, type_ord, meeting_tid
;


-- KEY 		= meeting_list_archived_by_date
set-if-empty:timeformat=YYYYMMDDHH24MI
;
select mtgroup_tid,
       mtgroup_name,
       type_tid,
       type_name,
       meeting_tid,
       meeting_name,
       status_tid,
       location_city,
       location_address,
       location_room,
       to_char(time_start, :timeformat) as time_start,
       to_char(time_end, :timeformat)   as time_end,
       access_right_tid,
       agenda_count,
       null                                  as unread,
       cmt_on,
       item_type,
       'NA'                                  as answer,
       response
from esp.v_mp_meeting_list_all
where (status_tid = 11 and status_tid is not null)
--order by time_start desc, mtgroup_ord, type_ord, meeting_tid desc
;







-- KEY = cr_get_lastinserted

select max(meeting_tid) as meeting_tid
from esp.t_mp_meeting;


-- KEY = cr_insert_cr

call ESP.sp_cr_insert_cr(:in_cr_name, :in_parent, :in_time)
;
include:cr_get_lastinserted
;



-- KEY         = cr_question_edit

call esp.sp_cr_question_edit(:in_question_tid, :in_nme, :in_details, :in_due,
                             :in_recipient, :in_opt, :in_opttype, :in_votes_visible)
;



-- KEY         = cr_get_question_simple

select mpa.agenda_tid,
       mpa.name                                                                                     agenda_name,
       mpt.type_tid,
       mpt.name                                                                                     type_name,
       mpm.meeting_tid,
       mpm.name                                                                                     meeting_name,
       q.question_tid,
       pkg_crypto.decrypt_varchar(q.nme_enc)                                                    nme,
       q.due,
       q.options,
       q.option_type,
       q.question,
       case
           when q.details_enc is null then null
           else
               pkg_crypto.decrypt_cmnt(q.details_enc) end                                       details,
       q.votes_visible,
       esp.user_code()                                                                                current_user,
       CASE WHEN ro.user_id is not null then 'NA' when cra.user_id is null THEN 'N' ELSE 'Y' END as answer,
       ''                                                                                        as recipients,
       mpm.status_tid,
       vob.votesonbehalf,
       case
           when votes.votesgiven is not null then 'N'
           else 'Y'
           end                                                                                      editable,
       q.votes_visible,
       case when ro.user_id is null then 'false' else 'true' end                                    readonly,
       tma.access_right_tid,
       tma.access_right_tid_min
from esp.t_cr_question q
         inner join esp.t_cr_mp_link l on l.question_tid = q.question_tid
         inner join esp.t_mp_agenda mpa on mpa.agenda_tid = l.meeting_item_tid
         inner join esp.t_mp_meeting mpm on mpm.meeting_tid = mpa.meeting_tid
         inner join esp.v_mp_access_meeting tma on tma.meeting_tid = mpm.meeting_tid
    and tma.user_id = esp.user_code()
    and tma.access_right_tid in (4, 6)
         left join esp.t_cr_readonly ro on ro.meeting_tid = mpm.meeting_tid
    and ro.user_id = esp.user_code()
         inner join esp.t_mp_type mpt on mpt.type_tid = mpm.type_tid
         left outer join esp.v_cr_answer cra on cra.question_tid = q.question_tid
    and cra.user_id = esp.user_code() and cra.answer <> 'Comment without voting'
         left outer join esp.v_cr_voteonbehalf vob on vob.question_tid = l.question_tid
         left outer join
     (select question_tid, count(*) votesgiven
      from esp.t_cr_answer
      group by question_tid) votes on votes.question_tid = q.question_tid
where mpm.meeting_tid = :in_meeting_tid
;



-- KEY         = cr_answer_edit

call esp.sp_cr_answer_edit(:in_question_tid, :in_user_id, :in_reply, :in_cmt, :in_evidence)
;



--
-- KEY         = zz-cr_results_byagenda_agg
--

select dta.question_tid, dta.agenda_tid, dta.meeting_tid, dta.user_id, dta.first_name, dta.last_name,
       dta.answer, dta.cmt, dta.edited, dta.edited_by, dta.edited_first_name, dta.edited_last_name,
       STRING_AGG(cast(dta.file_tid as text), ',' order by last_name, first_name) as file_tid
from
    (
    select v.* , ae.file_tid
    from esp.v_cr_votes_full v
    left outer join esp.t_cr_answer_evidence ae on (AE.QUESTION_TID = v.question_Tid and ae.user_id = v.user_id and ae.answer_tid = v.answer_tid)
    where v.agenda_tid =
    (
    select agenda_tid from esp.t_mp_agenda where meeting_tid = (
    select meeting_tid from esp.t_mp_agenda where agenda_tid = :in_agenda_tid
    ) and item_type = 'CR'
    )
    ) dta
group by dta.question_tid, dta.agenda_tid, dta.meeting_tid, dta.user_id, dta.first_name, dta.last_name,
    dta.answer, dta.cmt, dta.edited, dta.edited_by, dta.edited_first_name, dta.edited_last_name
order by edited desc

--
-- KEY         = cr_results_byagenda_agg_all
--

select * from esp.v_cr_result
;


-- KEY          = meeting_detail_allcr

select * from esp.v_iesp_cr_detail
;


-- KEY         = cr_answer_evidence

select ae.question_tid, ae.user_id, ae.file_tid, f.filename, ae.edited, ae.edited_by
from esp.t_cr_answer_evidence ae
         inner join esp.t_file f on f.file_tid = ae.file_tid
where ae.question_tid = :in_question_tid
  and ae.user_id = :in_user_id
;



--
-- KEY         = meeting.documents.setSupplFlag
--

call ESP.sp_mp_suppl_flag_update(:itemTid, :filename, 'Y')
;

--
-- KEY         = meeting.documents.removeSupplFlag
--

call ESP.sp_mp_suppl_flag_update(:itemTid, :filename, 'N')
;

-- KEY         = cr_documents_all

select * from esp.v_iesp_cr_document
;


-- KEY         = cr_agenda_list

select * from esp.v_iesp_cr_agenda
;
