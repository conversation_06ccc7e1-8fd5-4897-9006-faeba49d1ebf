import MailService from '@sendgrid/mail';
import { SUCCESS, FAILURE } from '../constants';
import { AttachmentData } from '@sendgrid/helpers/classes/attachment';
import { DbFile } from './esp/types';
import { getDbFileContent } from './upload-file-utils';
import <PERSON><PERSON><PERSON><PERSON> from '../../logger/LoggerCenter';
import path from 'path';

MailService.setApiKey(process.env.SENDGRID_API_SECRET);
const logger = LoggerCenter.getLogger(path.basename(__filename));

export const send = async (
  recipients: Array<string>,
  from: string,
  subject: string,
  text: string,
  attachments?: AttachmentData[],
  html?: string
) => {
  const msg = html
    ? {
        to: recipients,
        from,
        subject,
        html,
        attachments
      }
    : {
        to: recipients,
        from,
        subject,
        text,
        attachments
      };
  return await MailService.send(msg)
    .then(() => {
      return SUCCESS;
    })
    .catch((error) => {
      console.error(error);
      return FAILURE;
    });
};

export const sendWithDBFile = async (
  recipients: Array<string>,
  from: string,
  subject: string,
  text?: string,
  attachmentsDBFiles?: DbFile[],
  html?: string
) => {
  const attachments = new Array<AttachmentData>();
  for (const file of attachmentsDBFiles) {
    const fileContent = await getDbFileContent(file.fileTid.toString());
    attachments.push({
      content: fileContent.toString('base64'),
      filename: `=?UTF-8?B?${Buffer.from(file.filename).toString('base64')}?=`,
      disposition: 'attachment'
    });
  }

  const msg = html
    ? {
        to: recipients,
        from,
        subject,
        html,
        attachments
      }
    : {
        to: recipients,
        from,
        subject,
        text,
        attachments
      };

  return await MailService.send(msg)
    .then(() => {
      return SUCCESS;
    })
    .catch((error) => {
      logger.error(`send mail msg: ${msg.to} ${msg.from} ${msg.subject} \n error sending email: ${error}`);
      return FAILURE;
    });
};
