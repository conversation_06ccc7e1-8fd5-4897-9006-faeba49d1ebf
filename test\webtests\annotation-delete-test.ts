/* eslint-disable no-console,space-before-function-paren */
import { test } from 'mocha';
import { localUrlNode } from '../common';
import axios from 'axios';
import { expect } from 'chai';

const deleteAnnotationUrl = `${localUrlNode}serviceQuery/AnnotationDelete
`;

const DOCUMENT_FILE_TID = 141941373;
// -- 141941574

const testname = '(webtests) annotation-delete-test';
test(testname, async () => {
  return Promise.all([doTest()]);
});

async function doTest() {
  const headers = {
    'Content-Type': 'multipart/form-data'
  };
  const formData = new FormData();
  formData.append('documentFileTidList', DOCUMENT_FILE_TID.toString());

  const start = Date.now();
  const response = await axios.post(deleteAnnotationUrl, formData, { headers });
  console.log(`${testname} successful, time used: ${Date.now() - start}ms`);
  console.debug(`try to delete ${DOCUMENT_FILE_TID} with ${deleteAnnotationUrl}`);
  expect(!!response.data).true;
  expect(!!response.data.name).true;
  expect(response.data.name).equals('AnnotationDelete');
}
