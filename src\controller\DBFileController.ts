import * as express from 'express';
import { Request, Response } from 'express';
import { isSqExceptionResult, sqExceptionResult, toFirst } from '../serviceQuery';
import ServiceCenter from '../service/ServiceCenter';
import { getDbFileContent } from '../service/lib/upload-file-utils';
import xss from 'xss';
import { sendFileBuffer } from '../utils';
import { getUserProfile } from './web-utils';
import { Controller } from '../types';
import { dbFileProcessServiceId } from '../service/constants';
import LoggerCenter from '../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));

const CONTROLLER_NAME = 'dbfile';

export default class DBFileController implements Controller {
  public readonly name = CONTROLLER_NAME;
  public readonly paths = [`/api/${CONTROLLER_NAME}`];
  public readonly router = express.Router();

  constructor() {
    this.router.post('/*', processHttpRequest);
    this.router.get('/*', processHttpRequest);
  }
}

async function processHttpRequest(req: Request, res: Response) {
  try {
    const sq = ServiceCenter.getInstance().getSq();
    const urlParts = req.path.split('/');
    const userProfile = await getUserProfile(sq, req);
    if (!userProfile) {
      res.json(sqExceptionResult('Could not create user profile!'));
      return;
    }
    const { userId, roles } = userProfile;
    const filename = xss(urlParts[urlParts.length - 1]);

    const res0 = await sq.run({
      serviceId: dbFileProcessServiceId,
      parameters: { filename, $USERID: userId },
      userId,
      roles
    });
    const dbFile = toFirst(res0);
    if (!dbFile) {
      res.json(sqExceptionResult(`NO ACCESS TO ${filename}`));
    } else {
      const content = await getDbFileContent(dbFile.fileTid);
      if (isSqExceptionResult(content)) {
        res.json(content);
      } else {
        sendFileBuffer(filename, content, res);
      }
    }
  } catch (e) {
    logger.error(`${CONTROLLER_NAME} ${e}`);
    res.sendStatus(500);
  }
}
