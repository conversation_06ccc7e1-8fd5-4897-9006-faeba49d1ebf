import 'mocha';
import { Pool } from 'pg';
import { expect } from 'chai';
import * as dotenv from 'dotenv';

dotenv.config();
describe('test-dbconnection', async () => {
  it('test-esp-connection', async () => {
    const user = process.env.DB_TRX_USER;
    const password = process.env.DB_TRX_PASSWORD;
    const host = process.env.DB_HOST;
    const database = process.env.DB_DATABASE;
    const port = +(process.env.DB_PORT || '5432');
    const ssl = true;
    const pool = new Pool({ user, host, database, password, port, ssl });
    const client = await pool.connect();
    expect(!!client).true;
    client.release(true);
  });
});
