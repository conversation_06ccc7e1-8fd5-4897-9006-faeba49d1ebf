import { expect } from 'chai';
import Mustache from 'mustache';

describe('mustache-tests', () => {
  it(`hello-world`, async () => {
    const result = renderHello();
    expect(result).equals('<PERSON>o Luke');
  });
  it(`loop`, async () => {
    const names = ['heidi', 'peter'];
    const result = Mustache.render('{{#names}}Hallo <b>{{.}}</b> {{/names}}', { names });
    expect(result).equals('<PERSON>o <b>heidi</b> <PERSON>o <b>peter</b> ');
  });
  it(`conditional`, async () => {
    const condi = Math.random() > 0.5;
    const result = Mustache.render('{{#condi}}istrue{{/condi}}', { condi });
    const expected = condi ? 'istrue' : '';
    expect(result).equals(expected);
  });
});

function renderHello() {
  const template = 'Hallo {{name}}';
  return Mustache.render(template, { name: '<PERSON>' });
}
