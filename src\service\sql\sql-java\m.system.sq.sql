
--
-- KEY           = m.appProperties
-- ACCESS_GROUPS = SYSTEM
--

select NAME, VALUE from ESP.T_APP_PROPERTIES where NAME like 'm.%'
;


--
-- KEY           = m.userPrefs
-- ACCESS_GROUPS = SYSTEM
--

select PREF_NAME as NAME, PREF_VALUE as "VALUE" from ESP.T_USER_PREF where USER_ID = :$USERID and PREF_NAME like 'm.%'
;


--           
-- KEY         = m.setMVersion
--

select * from ESP.T_APP_PROPERTIES where NAME = 'mVersion'
;


--
-- KEY           = m.tabs
-- ACCESS_GROUPS = SYSTEM
--

select * from ESP.V_M_TABS
;


--           
-- KEY         = m.getkeys.getServices
--

select 
SERVICES 
from 
ESP.T_CYCLE_DEFINITION_USER
where
USER_ID = 
( 
select coalesce( max(USER_ID ), '_DEFAULT_') 
from ESP.T_CYCLE_DEFINITION_USER where USER_ID = :userId and NAME = :name
)
and NAME = :name


--
-- KEY           = m.getkeys.getSourceDocuments
-- ACCESS_GROUPS = SYSTEM
--

SELECT 
f1.FILE_TID,
to_char(creation_timestamp, 'yyyy-mm-dd hh24:mi') as creation_timestamp,
f1.OWNER,
f1.DIRECTORY,
f1.FILENAME, f1.TAGS,
LENGTH (f1.DOCUMENT) as "LENGTH"
FROM ESP.T_FILE f1
WHERE f1.FILE_TID IN 
(
  SELECT MAX (innerquery.FILE_TID)
  FROM ESP.T_FILE innerquery where innerquery.DIRECTORY like :directory 
  and
  not (innerquery.DIRECTORY like :exclusionDirectory)
  GROUP BY DIRECTORY, FILENAME
) 
ORDER BY f1.DIRECTORY, F1.FILENAME
;


--
-- KEY           = MUpdateService
-- ACCESS_GROUPS = SYSTEM,ESP_ADMIN  
--  

java:com.swissre.serviceMobile.lib.MUpdateService
;




--
-- crashlog
--


--
-- KEY           = Crashlog
--

set:crashlogDir=/var/m.crashlog/
;
java:com.swissre.serviceMobile.lib.Crashlog



--
-- KEY           = m.crashlog.insert
-- ACCESS_GROUPS = SYSTEM
--

create-tid-for:logTid
;
insert into ESP.T_LOG 
(LOG_TID, ERR_SEVERITY, ERR_SP_NAME, ERR_ORIGIN, SESSION_ID, ERR_MESSAGE, ERR_TIMESTAMP, VERSION) 
values
(:logTid, 'E', 'm.crashlog', :userId, -32187, :errMessage, current_timestamp, :version)



--
-- KEY           = m.crashlog.get
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
--

select LOG_TID, TO_CHAR(ERR_TIMESTAMP, 'YYYY-MM-DD HH24:MI') as "ERR_TIMESTAMP", 
      ERR_ORIGIN, VERSION, ERR_MESSAGE from ESP.T_LOG 
      where LOG_TID > -10 and LOG_TID  = :logTid



--
-- KEY           = m.crashlog.select
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
--


set-if-empty:errTimestamp=%
;
set-if-empty:errOrigin=%
;
set-if-empty:errMessage=%
;
select 
LOG_TID, 
TO_CHAR(ERR_TIMESTAMP, 'YYYY-MM-DD HH24:MI') as "ERR_TIMESTAMP", 
ERR_ORIGIN,
VERSION,
ERR_MESSAGE
from 
ESP.T_LOG
where 
SESSION_ID = -32187
and
TO_CHAR(ERR_TIMESTAMP, 'YYYY-MM-DD HH24:MI') like :errTimestamp
and
ERR_ORIGIN like :errOrigin
and
ERR_MESSAGE like :errMessage
order by 
LOG_TID desc



--
-- KEY = m.loginReport
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
--

set-if-empty:userId=%
;
set-if-empty:ts=%
;
set-if-empty:device=%
;
set-if-empty:lastName=%
;
select t.USER_ID, t.TS, t.DEVICE, vu1.FIRST_NAME, vu1.LAST_NAME
from (
         select USER_ID, max(TO_CHAR(LOG_INSERTED, 'YYYY-MM-DD MM:SS')) as TS, 'Browser' as DEVICE
         from ESP.T_USAGE_LOG
         GROUP BY USER_ID
     ) t
         left join
     ESP.V_SR_USER_ALL vu1 on vu1.USER_ID = t.USER_ID
where upper(t.USER_ID) like upper(:userId)
  and t.DEVICE like :device
  and t.TS like :ts
  and upper(vu1.LAST_NAME) like upper(:lastName)
ORDER BY t.TS desc



--
-- KEY = m.Getkeys2
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
--

java:com.swissre.serviceMobile.lib.Getkeys2


--
-- KEY = m.Start2
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
--

java:com.swissre.serviceMobile.lib.Start2
