trigger: none

pool: SR-Ubuntu-Default

variables:
  - template: ./dev-variables.yml

stages:
  - stage: TRAIN
    jobs:
      - job: Build_Deploy
        steps:
          - template: ./steps.yml
            parameters:
              kubeloginVersion: $(kubeloginVersion)
              apmId: $(apmId)
              clusterEnv: $(clusterEnv)
              stage: $(stage)
              imageTag: $(imageTag)
              serviceConnection: $(serviceConnection)
              npmBuildScript: build
              deploymentEnv: $(deploymentEnv)
