import { DrawOperation } from '../types'; // const cp = (n: number): number => n / 255;

// const cp = (n: number): number => n / 255;
const numbersToRgba = (r: number, g: number, b: number, a = 1): string => `rgba(${r},${g},${b},${a})`;
export const PINK = 'rgba(255, 175, 175, 1)';
export const getColor = (operation: DrawOperation, alpha?: number): string => {
  const colorNumbers = getColorNumbers(operation);
  if (!colorNumbers) {
    return PINK;
  }
  const { r, g, b, a } = colorNumbers;
  return numbersToRgba(r, g, b, alpha * (a / 255));
};

export const getColorNumbers = (
  operation: DrawOperation
): {
  a: number;
  r: number;
  g: number;
  b: number;
} => {
  const strColor = operation.objectMeta?.color;
  if (!strColor) {
    return { r: 255, g: 175, b: 175, a: 255 };
  }
  if (strColor?.length != 9 && !strColor.startsWith('#')) {
    return { r: 255, g: 175, b: 175, a: 255 };
  }
  const strPurifiedColor = strColor.substring(1);
  const strR = strPurifiedColor.substring(0, 2);
  const strG = strPurifiedColor.substring(2, 2 + 2);
  const strB = strPurifiedColor.substring(4, 4 + 2);
  const strAlpha = strPurifiedColor.substring(6);
  const r = Number.parseInt(strR, 16);
  const g = Number.parseInt(strG, 16);
  const b = Number.parseInt(strB, 16);
  const a = Number.parseInt(strAlpha, 16);
  return { r, g, b, a };
};
