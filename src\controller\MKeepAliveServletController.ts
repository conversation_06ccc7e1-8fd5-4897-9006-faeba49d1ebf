import * as express from 'express';
import { Request, Response } from 'express';
import { Controller } from '../types';
import <PERSON>gger<PERSON>enter from '../logger/LoggerCenter';
import path from 'path';
import { getUserProfile } from './web-utils';
import { sqExceptionResult } from '../serviceQuery';
import ServiceCenter from '../service/ServiceCenter';

const logger = LoggerCenter.getLogger(path.basename(__filename));
const CONTROLLER_NAME = 'MKeepAliveServletController';

export default class MKeepAliveServletController implements Controller {
  public readonly name = CONTROLLER_NAME;
  public readonly paths = ['/api/mobile/keepalive'];
  public readonly router = express.Router();

  constructor() {
    this.router.post('/*', processHttpRequest);
    this.router.get('/*', processHttpRequest);
  }
}

async function processHttpRequest(req: Request, res: Response) {
  try {
    const sq = ServiceCenter.getInstance().getSq();
    const userProfile = await getUserProfile(sq, req);
    if (!userProfile) {
      res.json(sqExceptionResult('Could not create user profile!'));
      return;
    }
    logger.info(`User : ${userProfile.userId}`);
    res.send('OK');
  } catch (e) {
    logger.error(`${CONTROLLER_NAME} ${e}`);
    res.sendStatus(500);
  }
}
