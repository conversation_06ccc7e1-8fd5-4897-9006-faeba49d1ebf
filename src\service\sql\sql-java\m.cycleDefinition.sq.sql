


--
-- KEY           = m.cycleDefinition.insert
-- ACCESS_GROUPS = ESP_ADMIN
--

set-if-empty:userId=_DEFAULT_
;
set-if-empty:delay=-1
;
set-if-empty:interval=-1
;
set-if-empty:priority=-1
;
insert into ESP.T_CYCLE_DEFINITION_USER 
(NAME, KEYS_URL, WAIT_MSG, KEYS_LOG_URL, DOCU_LOG_URL, DELAY, INTERVAL, PRIORITY, USER_ID
, SERVICES, SERVER, BACKEND_URL
)
VALUES 
(:name, :keysUrl, :waitMsg, :keysLogUrl, :docuLogUrl, :delay, :interval, :priority, :userId
,:services, :server, :backendUrl)
;



--
-- KEY = m.cycleDefinition.select
--
--

set-if-empty:name=%
;
set-if-empty:userId=%
;
set-if-empty:server=%
;
set-if-empty:services=%
;
set-if-empty:backendUrl=%
;
select NAME,
       KEY<PERSON>_URL,
       WAIT_MSG,
       KEY<PERSON>_LOG_URL,
       DOCU_LOG_URL,
       DELAY,
       INTERVAL,
       PRIORITY,
       USER_ID,
       CONTENT,
       SERVICES,
       SERVER,
       BACKEND_URL
from ESP.T_CYCLE_DEFINITION_USER
where (NAME like :name or (:name = '%' and NAME is null))
  and (USER_ID like :userId or (:userId = '%' and USER_ID is null))
  and (SERVER like :server or (:server = '%' and SERVER is null))
  and (SERVICES like :services or (:services = '%' and SERVICES is null))
  and (BACKEND_URL like :backendUrl or (:backendUrl = '%' and BACKEND_URL is null))
order by NAME, USER_ID, KEYS_URL


--
-- KEY = m.cycleDefinition.get
--

select NAME, KEYS_URL, WAIT_MSG, KEYS_LOG_URL, DOCU_LOG_URL, DELAY, 
INTERVAL, PRIORITY, USER_ID, CONTENT, SERVICES, SERVER, BACKEND_URL from ESP.T_CYCLE_DEFINITION_USER
where NAME = :name and USER_ID = :userId
;



--
-- KEY           = m.cycleDefinition.update
-- ACCESS_GROUPS = ESP_ADMIN
--

update
    ESP.T_CYCLE_DEFINITION_USER
set NAME=:name,
    KEYS_URL=:keysUrl,
    WAIT_MSG=:waitMsg,
    KEYS_LOG_URL=:keysLogUrl,
    DOCU_LOG_URL=:docuLogUrl,
    DELAY=:delay,
    INTERVAL=:interval,
    PRIORITY=:priority,
    SERVICES=:services,
    SERVER=:server,
    BACKEND_URL = :backendUrl
where NAME = :name
  and USER_ID = :userId
;



--
-- KEY           = m.cycleDefinition.delete
-- ACCESS_GROUPS = ESP_ADMIN
--

delete from ESP.T_CYCLE_DEFINITION_USER 
where 
NAME = :name 
and 
USER_ID = :userId




