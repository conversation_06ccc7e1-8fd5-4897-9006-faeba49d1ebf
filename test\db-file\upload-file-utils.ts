import { getDbFileTag } from '../../src/service/lib/upload-file-utils';
import { test } from 'mocha';
import { expect } from 'chai';
import { DbFile } from '../../src/service/lib/esp/types';

test(`get DbFileTag`, () => {
  const dbFile: DbFile = { fileTid: '123', filename: 'hello', directory: '/', tags: '' };
  const res1 = getDbFileTag(dbFile, 'pathId');
  expect(res1).equals('');
  dbFile.tags = undefined;
  expect(getDbFileTag(dbFile, 'pathId')).equals('');
  dbFile.tags = 'pathId:221';
  expect(getDbFileTag(dbFile, 'pathId')).equals('221');
  dbFile.tags = 'xpathId:221';
  expect(getDbFileTag(dbFile, 'pathId')).equals('');
  dbFile.tags = 'hello:world,xpathId:221';
  expect(getDbFileTag(dbFile, 'pathId')).equals('');
  dbFile.tags = 'hello:world,pathId:221';
  expect(getDbFileTag(dbFile, 'pathId')).equals('221');
});
