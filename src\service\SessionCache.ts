import { toColumnList } from '../serviceQuery/utils';
import ServiceCenter from './ServiceCenter';
import { UserProfile } from './types';

const timeoutMs = 10000;

export class SessionCache {
  private static inst: SessionCache;

  static getInstance() {
    return !SessionCache.inst ? (SessionCache.inst = new SessionCache()) : SessionCache.inst;
  }

  private readonly sessions = new Map<string, UserProfile>();
  private readonly tokenMap = new Map<string, string>();
  private readonly emailMap = new Map<string, string>();

  public checkTimeout(token: string) {
    const userId = this.tokenMap.get(token);
    if (userId) {
      const e = this.sessions.get(userId);
      if (e && Date.now() - e.created > timeoutMs) {
        this.sessions.delete(userId);
        this.tokenMap.delete(userId);
      }
    }
  }

  public getUserId(token: string) {
    this.checkTimeout(token);
    return this.tokenMap.get(token);
  }

  // public addRoles(userId: string, roles: string[] = []) {
  //   const created = Date.now();
  //   this.sessions.set(userId, { userId, roles, created });
  // }

  public addToken(token: string, userId: string, email: string) {
    this.tokenMap.set(token, userId);
    this.emailMap.set(userId, email);
  }

  public getEmail(userId: string) {
    return this.emailMap.get(userId);
  }

  public async getRoles(userId: string): Promise<string[]> {
    this.checkTimeout(userId);
    let e = this.sessions.get(userId);
    if (!e) {
      const roles = await resolveRoles(userId);
      e = { userId, userEmail: '', roles, created: Date.now() };
      this.sessions.set(userId, e);
    }
    return e.roles;
  }
}

export async function resolveRoles(userId: string): Promise<string[]> {
  const sc = ServiceCenter.getInstance();
  const r = await sc.runSystemSq({
    serviceId: 'roleNames.select',
    parameters: { userId: userId, $USERID: userId }
  });
  // roleNames.push('USER_AUTH');
  // roleNames.push('USER_' + userId);
  return toColumnList(r, 'roleName');
}
