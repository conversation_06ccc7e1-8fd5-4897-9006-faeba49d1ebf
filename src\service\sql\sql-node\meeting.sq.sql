--
-- SERVICE_ID = meeting.get
--

select meeting_tid,
       tmm.name as meeting_name,
       ty.name  as type_name
from esp.t_mp_meeting tmm
         join esp.t_mp_type ty on tmm.type_tid = ty.type_tid
where tmm.meeting_tid = cast(:in_meeting_tid as numeric)
  and tmm.meeting_tid in (select meeting_tid
                          from esp.v_mp_access_meeting_all
                          where user_id = esp.user_code()
                            and access_right_tid_min >= 4);


--
-- SERVICE_ID = meeting_list_archived_by_type
--
select mtgroup_tid,
       mtgroup_name,
       type_tid,
       type_name,
       meeting_tid,
       meeting_name,
       status_tid,
       location_city,
       location_address,
       location_room,
       to_char(time_start, 'YYYYMMDDHH24MI') as time_start,
       to_char(time_end, 'YYYYMMDDHH24MI') as time_end,
       access_right_tid,
       agenda_count,
       unread,
       cmt_on,
       item_type,
       'NA' as answer,
       response,
       mtgroup_ord,
       type_ord
from esp.v_mp_meeting_list_all
where status_tid = 11
    and status_tid is not null;

-- SERVICE_ID      = meeting_insert_meeting

call esp.sp_mp_insert_meeting_only ( :in_meeting_name, :in_parent, :in_preceding_meeting, :in_location_room, :in_location_address, :in_location_city, :in_time_start, null)
;
include:cr_get_lastinserted
;


--
-- SERVICE_ID = isAdminFor
-- ROLES      = SYSTEM
--

select case when esp.is_admin_for('M', :tid) = 'N' then 0 else 1 end +
       case when esp.is_admin_for('CR', :tid) = 'N' then 0 else 1 end +
       case when esp.is_admin_for('T', :tid) = 'N' then 0 else 1 end +
       case when esp.is_admin_for('A', :tid) = 'N' then 0 else 1 end +
       case when esp.is_admin_for('Q', :tid) = 'N' then 0 else 1 end ADMIN
;

--
-- SERVICE_ID = type_read_access_admin
--

select * from esp.sp_mp_type_read_access_admin(:in_meeting_item_tid)
;


--
-- SERVICE_ID = get_meeting_status
-- ROLES      = SYSTEM
-- INFO: 10=published, 11=archived
--

select status_tid from esp.t_mp_meeting where meeting_tid = :meetingTid;
;


--
-- SERVICE_ID = meeting_read_access_admin
--
WITH UserList AS (
    SELECT at.user_id,
       MAX(u.last_name)  as                                        last_name,
       MAX(u.first_name) as                                        first_name,
       case (MIN(at.access_right_tid_min))
           when 4 then 'read'
           else '' end                                             R1,
       case (MAX(at.access_right_tid))
           when 6 then 'admin'
           else '' end                                             R2,
       case
           when max(det.access_right_tid) is null
               and min(detAll.update_date) is not null
               and min(detAll.update_date) < min(at.valid_from) then 0
           when min(det.access_right_tid) = 0 then 0
           else 1
           end                                                     access_ok,
       max(case when at.user_id = esp.user_code() then 0 else 1 end) enabled,
       MAX(u.email_address)                                   external_mail
FROM esp.t_mp_meeting m
         inner join esp.v_mp_access_type_all at
                    on at.type_tid = m.type_tid
         INNER JOIN esp.V_SR_USER u
                    on u.user_id = at.user_id
         LEFT JOIN esp.t_mp_access_detail det
                   ON det.meeting_item_tid = m.meeting_tid
                       AND det.user_id = at.user_id
         LEFT JOIN esp.t_mp_access_detail detAll
                   ON detAll.meeting_item_tid = m.meeting_tid
                       AND detAll.update_date < at.valid_from
                       AND detAll.access_right_tid = 0
WHERE m.meeting_tid = :in_meeting_item_tid
GROUP BY at.user_id, m.meeting_tid
)
SELECT * FROM UserList
WHERE EXISTS (
    SELECT 1 FROM UserList WHERE user_id = esp.user_code() and r2='admin'
)
ORDER BY last_name, first_name;