{"env": {"browser": false, "es6": true, "jest": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"quotes": ["error", "single", {"avoidEscape": true}], "space-before-function-paren": ["error", "never"], "no-extra-semi": "error", "semi": ["error", "always"], "no-spaced-func": "error", "block-spacing": "error", "comma-spacing": "error", "key-spacing": "error", "keyword-spacing": "error", "space-infix-ops": "error", "space-unary-ops": "error", "no-multi-spaces": "error", "no-multiple-empty-lines": "error", "no-trailing-spaces": "error", "no-irregular-whitespace": "error", "@typescript-eslint/no-explicit-any": "off", "no-console": ["error", {"allow": ["warn", "error"]}]}}