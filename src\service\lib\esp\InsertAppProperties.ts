import { ServiceFunction, SqResult } from '../../../serviceQuery';
import ServiceCenter from '../../ServiceCenter';

export const InsertAppProperties: ServiceFunction = async({ request }): Promise<SqResult> => {
  const dbDriver = ServiceCenter.getInstance().getSq().driver;
  const { parameters } = request;
  const r1 = await dbDriver.processSql('select name from esp.t_app_properties where name = :name', parameters);
  let rowsAffected = 0;
  if ((r1.table?.length || 0) === 0) {
    const r2 = await dbDriver.processSql(
      'INSERT INTO ESP.T_APP_PROPERTIES (NAME, VALUE) VALUES (:name, :value) ON CONFLICT (NAME) DO UPDATE SET VALUE = EXCLUDED.VALUE',
      parameters
    );
    rowsAffected = r2.rowsAffected || -1;
  }
  return { name: 'insertAppProperties', rowsAffected };
};
