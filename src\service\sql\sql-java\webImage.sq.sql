
--
-- KEY    = UploadImage
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
--


set:processServiceId=moveToImageDirectory
;
java:com.swissre.serviceQuery.lib.UploadFile
;

--
-- KEY    = UploadTmpFile
-- ACCESS_GROUPS = ESP_ADMIN
--

java:com.swissre.serviceQuery.lib.UploadFile


--
-- KEY           = moveToImageDirectory
-- ACCESS_GROUPS = SYSTEM
--


delete from ESP.T_FILE
where
DIRECTORY = '/web/images/'
and
FILENAME = :filename
;
update ESP.T_FILE 
set DIRECTORY = '/web/images/'
where
FILE_TID = :fileTid
;
select 'Web path for file ' || FILENAME || ' is ' || 'dbfile/'|| FILENAME from ESP.T_FILE where FILE_TID = :fileTid





--
-- KEY    = selectDownloadFile
--


select FILE_TID, FILENAME, DIRECTORY from ESP.T_FILE where FILENAME = :filename and DIRECTORY = '/web/images/'







--
-- KEY    = webImage.select
--

set-if-empty:fileTid=%
;
set-if-empty:filename=%
;
set-if-empty:owner=%
;
select 
FILE_TID, 
FILENAME, 
TO_CHAR(CREATION_TIMESTAMP, 'YYYY-MM-DD HH24:MI') AS CREATION_TIMESTAMP, 
OWNER, 
length(DOCUMENT) as DOCUMENT_SIZE 
from ESP.T_FILE where 
DIRECTORY = '/web/images/'
and
FILENAME like :filename
order by FILE_TID



--
-- KEY    = webImage.get
--

select 
FILE_TID, 
FILENAME, 
TO_CHAR(CREATION_TIMESTAMP, 'YYYY-MM-DD HH24:MI') AS CREATION_TIMESTAMP, 
OWNER, 
length(DOCUMENT) as DOCUMENT_SIZE 
from ESP.T_FILE where 
DIRECTORY = '/web/images/'
and
FILE_TID = :fileTid



--
-- KEY           = webImage.delete
-- ACCESS_GROUPS = ESP_ADMIN
--

delete from ESP.T_FILE where DIRECTORY = '/web/images/' and FILE_TID = :fileTid

