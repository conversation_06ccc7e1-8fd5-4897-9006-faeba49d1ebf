import ServiceCenter from '../ServiceCenter';
import { toMap } from '../../serviceQuery/utils';

type AppProperty = Record<string, string>;
const REFRESH_MINUTES = 1;

export default class AppPropertiesService {
  private static inst: AppPropertiesService;

  static getInstance() {
    return !AppPropertiesService.inst
      ? (AppPropertiesService.inst = new AppPropertiesService())
      : AppPropertiesService.inst;
  }

  private appPropertiesMap: AppProperty = {};
  private loaded = 0;

  private async checkRefresh() {
    if (Date.now() - this.loaded > 1000 * 60 * REFRESH_MINUTES) {
      return this.refresh();
    }
  }

  private async refresh() {
    // Register direct/overload services
    const sc = ServiceCenter.getInstance();
    const res0 = await sc.runSystemSq({ serviceId: 'selectAppProperties' });
    this.appPropertiesMap = toMap(res0, 'name', 'value');
    this.loaded = Date.now();
  }

  public async get(name: string, defaultValue = ''): Promise<string> {
    await this.checkRefresh();
    return this.appPropertiesMap[name] || defaultValue;
  }

  public async getAll(): Promise<AppProperty> {
    await this.checkRefresh();
    return this.appPropertiesMap;
  }

  public async saveProperty(name: string, value: string) {
    // Register direct/overload services
    const sc = ServiceCenter.getInstance();
    await sc.runSystemSq({ serviceId: 'saveAppProperties', parameters: { name, value } });
    await this.refresh();
    return this.getAll();
  }

  public async getArray(name: string): Promise<string[]> {
    await this.checkRefresh();
    const s = await this.get(name, '');
    return s
      .split(',')
      .map((e) => e.trim())
      .filter((e) => !!e);
  }
}
