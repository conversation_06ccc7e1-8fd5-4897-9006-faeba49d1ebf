export function makeTransparent(data: Uint8ClampedArray): Uint8ClampedArray {
  for (let i = 0; i < data.length; i += 4) {
    if (data[i] === 255 && data[i + 1] === 255 && data[i + 2] === 255) {
      data[i + 3] = 0;
    }
  }
  return data;
}

export function maskImage(data: Uint8ClampedArray, mask: Uint8ClampedArray) {
  for (let i = 0; i < data.length; i += 4) {
    if (mask[i + 3] > 0) {
      data[i + 3] = 0;
    }
  }
}
