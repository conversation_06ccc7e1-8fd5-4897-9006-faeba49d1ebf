
--
-- KEY           = viewFile.select
-- ACCESS_GROUPS = ESP_ADMIN
--  

set:$MAXROWS=100000
;
set:$MAXFETCHROWS=100500
;
set-if-empty:fileTid=0
;
set-if-empty:filename=%
;
set-if-empty:directory=%
;
set-if-empty:owner=%
;
select FILE_TID, FILENAME, TO_CHAR(CREATION_TIMESTAMP, 'YYYY-MM-DD HH24:MI') AS CREATION_TIMESTAMP, DIRECTORY, OWNER, length(DOCUMENT) as "SIZE" from ESP.T_FILE
where
not(DIRECTORY  like '/opt/%') 
and 
not(DIRECTORY  like '/var/%') 
and 
not (FILENAME  like '/opt/%') 
and
(FILE_TID = :fileTid or :fileTid = 0)
and
FILENAME like :filename
and 
DIRECTORY like :directory
order by 
CREATION_TIMESTAMP desc





--
-- KEY           = viewFile.get
-- ACCESS_GROUPS = ESP_ADMIN
--  

select FILE_TID, FILENAM<PERSON>, TO_CHAR(CREATION_TIMESTAMP, 'YYYY-MM-DD HH24:MI') AS CREATION_TIMESTAMP, DIRECTORY, OWNER, length(DOCUMENT) as "SIZE" from ESP.T_FILE
where
FILE_TID = :fileTid



