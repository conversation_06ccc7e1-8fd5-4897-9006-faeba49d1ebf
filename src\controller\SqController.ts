import * as express from 'express';
import { Request, Response } from 'express';
import {
  isSqExceptionResult,
  sqExceptionResult,
  SqRequest as SqRequest,
  SqResult as SqResult
} from '../serviceQuery/serviceQuery-common';
import ServiceCenter from '../service/ServiceCenter';
import { processUploadFiles } from '../service/lib/upload-file-utils';
import { getUserProfile, toParameters } from './web-utils';
import { Controller } from '../types';
import { canUploadFiles } from '../service/service-utils';
import LoggerCenter from '../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));

const CONTROLLER_NAME = 'serviceQuery';
export const UPLOADFILE_PREFIX = 'UPLOADFILE';

export default class SqController implements Controller {
  public name = CONTROLLER_NAME;
  public paths = [`/api/${CONTROLLER_NAME}`, '/api/mobile/restJson'];
  public router = express.Router();

  constructor() {
    this.router.post('/*', processHttpRequest);
    this.router.get('/*', processHttpRequest);
  }
}

async function processHttpRequest(req: Request, res: Response) {
  try {
    const url = req.path;
    const serviceId = url.replace(/\//gi, '.').replace(/^./gi, '');
    if (!serviceId) {
      res.json({ status: 'error', systemMessage: 'No service id provided!' });
      return;
    }
    await processService(serviceId, req, res);
  } catch (e) {
    logger.error(`${CONTROLLER_NAME} ${e}`);
    res.sendStatus(500);
  }
}

async function processService(serviceId: string, req: Request, res: Response): Promise<void> {
  if (serviceId === 'echo') {
    await processEcho(req, res);
  }
  let sqResult: SqResult | undefined;
  try {
    const sc = ServiceCenter.getInstance();
    const sq = sc.getSq();
    const userProfile = await getUserProfile(sq, req);
    if (!userProfile) {
      res.json(sqExceptionResult('Could not create user profile!'));
      return;
    }
    const { userId, roles } = userProfile;

    const parameters = toParameters(req, userId);
    //
    // Process file upload
    //
    if (req.files && canUploadFiles(userProfile.roles, serviceId)) {
      const res0 = await processUploadFiles(req.files, userId, parameters);
      if (isSqExceptionResult(res0)) {
        res.json(res0);
        return;
      }
      if ('uploadOnly' === serviceId) {
        res.json(res0);
        return;
      }
      res0.forEach((fileTid, index) => (parameters[`${UPLOADFILE_PREFIX}${index}`] = fileTid));
    }
    if (parameters.CLEARCACHE) {
      sc.clearServiceCache();
    }
    const sqRequest: SqRequest = { serviceId, parameters, userId, roles };
    const result = await sq.run(sqRequest);
    sqResult = result || { exception: 'Result was empty (null)' };
  } catch (e) {
    sqResult = sqExceptionResult(e);
  }
  res.json(sqResult);
}

async function processEcho(req: Request, res: Response): Promise<void> {
  let sqResult: SqResult;
  const parameters = toParameters(req, 'EchoUser');
  if ('crash' === parameters.command) {
    throw Error('crash test requested!');
  }

  try {
    const table: string[][] = [];
    Object.entries(parameters).forEach(([name, value]) => table.push([name, value, 'param']));
    sqResult = { name: 'EchoResult', table, header: ['name', 'value', 'type'] };
    if (req.headers) {
      Object.keys(req.headers).forEach((k) => {
        try {
          const v = req.headers[k].toString();
          table.push([k, v, 'header']);
        } catch (e) {
          throw new Error(e);
        }
      });
    }
  } catch (e) {
    sqResult = sqExceptionResult(e);
  }
  res.json(sqResult);
}
