import { AnnotationPoint, AnnotationPointN, DrawOperation, InstrumentType } from './types';

export function getOpacity(object: DrawOperation): number {
  const strColor = object.objectMeta.color;
  if (!strColor) {
    return 1;
  }
  const hex = strColor.substring(1);
  return parseInt(hex.substring(6, 8), 16) / 255;
}

export const getWidth = (operation: DrawOperation): number => operation.objectMeta?.width ?? 20;

const WIDTH_ERASER = 12.0;
const WIDTH_PENCIL = 8.0;

export function getStroke(object: DrawOperation) {
  const scale = getWidth(object);
  return scale * object.objectMeta.instrument === InstrumentType.ERASER ? WIDTH_ERASER : WIDTH_PENCIL;
}

export function translatePoly(poly: AnnotationPointN[], start: AnnotationPointN): AnnotationPointN[] {
  return poly.map((p) => [p[0] + start[0], p[1] + start[1]]);
}

export function rotateOrigin(a: number, [x, y]: AnnotationPointN): AnnotationPointN {
  return [x * Math.cos(a) - y * Math.sin(a), x * Math.sin(a) + y * Math.cos(a)];
}

export function getPoint(points: (AnnotationPoint | AnnotationPointN)[], index: number): AnnotationPointN {
  if (index < points.length) {
    const x = +points[index][0];
    const y = +points[index][1];
    return [x, y];
  }
  return [];
}

export function getPoints(points: (AnnotationPoint | AnnotationPointN)[]): AnnotationPointN[] {
  if (points?.length) {
    return points.map(([xs, ys]) => [+xs, +ys]);
  }
  return [];
}
