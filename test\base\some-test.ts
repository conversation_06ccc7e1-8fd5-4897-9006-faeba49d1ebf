import path from 'path';
import { expect } from 'chai';
import { base64_to_utf8 } from '../../src/service/base64-utils';

describe('same-tests', () => {
  it('path extname test', async () => {
    expect(path.extname('bla.pdf')).equals('.pdf');
    expect(path.extname('bla.exe')).equals('.exe');
    expect(path.extname('bla.PDF')).equals('.PDF');
    expect(path.extname('bla.pEf')).equals('.pEf');
    expect(path.extname('bla')).equals('');
  });

  it('array test: in and of reminder', async () => {
    for (const e in ['Hello']) {
      expect(e).equals('0');
    }
    for (const e of ['Hello']) {
      expect(e).equals('Hello');
    }
    for (const e in { hello: 'world' }) {
      expect(e).equals('hello');
    }
  });

  it('array test: should fail if arrays have different values', () => {
    const arr1 = [1, 2, 3];
    const arr2 = [1, 2, 4];

    expect(arr1).to.not.deep.equal(arr2);
  });

  it('Json serial', () => {
    const joes = "joe's";
    expect(joes).to.equal("joe's");
  });
  it('check path functions', () => {
    expect(path.basename('a/b/c/test.pdf')).to.equal('test.pdf');
    expect(path.basename('//a/b/c/test.pdf')).to.equal('test.pdf');
    expect(path.basename('/a/b/c/test.pdf')).to.equal('test.pdf');
    expect(path.basename('test.pdf')).to.equal('test.pdf');
    expect(path.basename('./test.pdf')).to.equal('test.pdf');

    expect(path.dirname('a/b/c/test.pdf')).to.equal('a/b/c');
    expect(path.dirname('//a/b/c/test.pdf')).to.equal('//a/b/c');
    expect(path.dirname('/a/b/c/test.pdf')).to.equal('/a/b/c');
    expect(path.dirname('test.pdf')).to.equal('.');
    expect(path.dirname('./test.pdf')).to.equal('.');

    expect(path.normalize('a/b/c/test.pdf')).to.equal('a\\b\\c\\test.pdf');
    expect(path.normalize('//a/b/c/test.pdf')).to.equal('\\\\a\\b\\c\\test.pdf');
    expect(path.normalize('/a/b/c/test.pdf')).to.equal('\\a\\b\\c\\test.pdf');
    expect(path.normalize('test.pdf')).to.equal('test.pdf');
    expect(path.normalize('./test.pdf')).to.equal('test.pdf');
  });

  it('decode base64', () => {
    const s = 'bla';
    const r = base64_to_utf8(s);
    expect(r).to.equal('');
  });
});
