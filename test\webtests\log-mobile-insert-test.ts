import { test } from 'mocha';
import * as dotenv from 'dotenv';
import { localUrlNode } from '../common';
import axios from 'axios';
import { toList } from '../../src/serviceQuery';

const cutDef: Record<string, string> = {
  iespVersion:
    'iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-iespVersion-test-',
  iosVersion: 'iespVersion-test',
  deviceType: 'iespVersion-test',
  logType: 'iespVersion-test',
  logTimestamp: 'iespVersion-test',
  logMessage: 'logMessage-test'
};

dotenv.config();
const serviceId = 'm.logMobile.insert';
const sqPart = `serviceQuery/${serviceId}`;
const localUrlNodeStart2 = `${localUrlNode}${sqPart}`;
test(`(webtests) meeting-detail-allcr-test`, async () => {
  console.log(`Start Node ${serviceId} url: ${localUrlNodeStart2}!`);
  const formData = new FormData();
  Object.entries(cutDef).forEach(([name, value]) => formData.append(name, value));

  const headers = {
    'Content-Type': 'multipart/form-data'
  };
  const resultNode = toList(await axios.post(localUrlNodeStart2, formData, { headers }));

  console.log(resultNode);
});
