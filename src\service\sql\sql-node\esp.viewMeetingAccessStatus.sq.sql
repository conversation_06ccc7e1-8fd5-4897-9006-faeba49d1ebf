--
-- SERVICE_ID = esp.viewMeetingAccessStatus.select
-- ACCESS_GROUPS    = ESP_ADMIN
--

set-if-empty:typeName=%
;
set-if-empty:meetingName=%
;
set-if-empty:meetingStatus=%
;
set-if-empty:agendaName=%
;
set-if-empty:userId=%
;
set-if-empty:lastName=%
;
set-if-empty:firstName=%
;
set-if-empty:enabled=%
;
select * from esp.v_log_access_status 
where TYPE_NAME is not null
and TYPE_NAME like :typeName
and MEETING_NAME like :meetingName
and MEETING_STATUS like :meetingStatus
and AGENDA_NAME like :agendaName
and USER_ID like :userId
and LAST_NAME like :lastName
and FIRST_NAME like :firstName
and cast(ENABLED as VARCHAR) like :enabled
;

