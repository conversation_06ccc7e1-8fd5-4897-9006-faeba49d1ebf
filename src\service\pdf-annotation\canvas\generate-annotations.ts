import { PDFDocument, PDFPage } from 'pdf-lib';
import { AnnotationPage, AnnotationType, DrawOperation, InstrumentType } from '../types';
import { createCanvas } from 'canvas';
import { maskImage } from './canvas-utils';
import { drawMarkerCanvas } from './drawMarker-canvas';
import { drawPencilCanvas } from './drawPencil-canvas';
import { drawArrowCanvas } from './drawArrow-canvas';
import { drawEraserCanvas } from './drawEraser-canvas';
import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';
import { drawCommentsAndFreetexts } from '../draw-comments-and-freetexts';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export async function generateAnnotations(pdfDoc: PDFDocument, annotation: AnnotationType) {
  processCanvasOperation(pdfDoc, annotation);
  processComments(pdfDoc, annotation);
}

export async function processCanvasOperation(pdfDoc: PDFDocument, annotation: AnnotationType) {
  const pdfPages: PDFPage[] = pdfDoc.getPages();
  annotation.pages.forEach((page) => {
    const pageIndex: number = page.pageMeta.pageNumber;
    if (pageIndex < pdfPages.length) {
      const pdfPage = pdfPages[pageIndex];
      processAnnotationPage(pdfDoc, pdfPage, page);
    } else {
      logger.warn(`Annotation page index (${pageIndex}) is outside of pdf pages range (${pdfPages.length})!`);
    }
  });
}

export async function processAnnotationPage(pdfDoc: PDFDocument, pdfPage: PDFPage, annotationPage: AnnotationPage) {
  try {
    const { width, height } = pdfPage.getSize();
    logger.debug('PDF page dimension:', width, height);
    const MAX_DIMENSION = 16384;
    if (width > MAX_DIMENSION || height > MAX_DIMENSION) {
      logger.warn(`Canvas dimensions too large (${width}x${height}), skipping page`);
      return;
    }
    const canvas = createCanvas(width, height);
    const canvasEraser = createCanvas(width, height);
    const ctx = canvas.getContext('2d');
    const ctxEraser = canvasEraser.getContext('2d');

    // const freeTextOperations: DrawOperation[] = [];
    for (const layer of annotationPage.layers) {
      for (const operation of layer.objects) {
        const instrument = operation.objectMeta.instrument;
        switch (instrument) {
          case InstrumentType.ERASER: {
            drawEraserCanvas(pdfPage, operation, canvasEraser);
            break;
          }
          case InstrumentType.MARKER: {
            logger.info('canvas ERASER drawing');
            drawMarkerCanvas(pdfPage, operation, canvas);
            break;
          }
          case InstrumentType.PENCIL: {
            drawPencilCanvas(pdfPage, operation, canvas);
            break;
          }
          case InstrumentType.ARROW: {
            drawArrowCanvas(pdfPage, operation, canvas);
            break;
          }
          default:
          // no operation
        }
      }
    }

    // finalize canvas and image
    const imageData = ctx.getImageData(0, 0, width, height);
    maskImage(imageData.data, ctxEraser.getImageData(0, 0, width, height).data);
    ctx.putImageData(imageData, 0, 0);

    const buff = canvas.toBuffer('image/png');
    const pdfImage = await pdfDoc.embedPng(new Uint8Array(buff));
    pdfImage.embed();
    pdfPage.drawImage(pdfImage);

  } catch (error) {
    logger.error('Error processing annotation page:', error);
  }
  // process all free text comments
  //await drawAllFreeTextComments(pdfDoc, pdfPage, freeTextOperations);
}

export async function processComments(pdfDoc: PDFDocument, annotation: AnnotationType) {
  const pages: PDFPage[] = pdfDoc.getPages();
  const commentAndFreeTextOperations: DrawOperation[][] = new Array(pages.length).fill(null);

  annotation.pages.forEach((annotationPage) => {
    if (annotationPage) {
      const pageIndex = annotationPage.pageMeta.pageNumber;
      for (const layer of annotationPage.layers) {
        for (const operation of layer.objects) {
          const instrument = operation.objectMeta.instrument;
          switch (instrument) {
            case InstrumentType.COMMENT:
            case InstrumentType.FREE_TEXT: {
              commentAndFreeTextOperations[pageIndex] = commentAndFreeTextOperations[pageIndex] || [];
              commentAndFreeTextOperations[pageIndex].push(operation);
              break;
            }
            default:
            // ignore others
          }
        }
      }
    }
  });

  pages.forEach((page, pageIndex) => {
    const ops = commentAndFreeTextOperations[pageIndex];
    if (ops?.length > 0) {
      drawCommentsAndFreetexts(pdfDoc, page, ops);
    }
  });
}
