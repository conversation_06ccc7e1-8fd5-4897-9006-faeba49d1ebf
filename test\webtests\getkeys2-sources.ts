import { localUrlNode } from '../common';
import { runGetkeys2 } from './getkeys2-utils';

const URL = `${localUrlNode}mobile/getkeys2`;

const URI = 'name=sources&sourcesOnly=true';
const runDataTest = async (url: string, uri: string) => {
  console.log(`Start Node webtest ${url}`);
  const resultNode = await runGetkeys2(`${url}?${uri}`, 'Node');
  console.log(`node: header        : ${JSON.stringify(resultNode.header)}`);
  console.log(`node: nr of records : ${resultNode.table?.length}`);
  console.log(`node: nr of keys    : ${Object.keys(resultNode).length}`);
};

describe('(webtests) Getkeys tree (node only)', () => {
  it(`getkeys2 ${URI}`, async () => {
    await runDataTest(URL, URI);
  });
});
