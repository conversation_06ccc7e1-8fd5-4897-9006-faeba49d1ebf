import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';
import { DbFile, MobDBFile } from '../esp/types';
import AppPropertiesService from '../AppPropertiesService';

import { StringRecord } from '../../../types';
import ServiceCenter from '../../ServiceCenter';
import { ajustDirectory, processUserServices } from '../MUpdateService';
import { hot_data, SYSTEM } from '../../constants';
import { isSqExceptionResult, singleValue, SqResult, toList } from '../../../serviceQuery';
import { getUserSalt } from './mobile-utils';
import { calculatePriority, getPathId, toPathString, useEncryption } from '../upload-file-utils';
import { createKeyString } from './crypto-utils';
import { track } from '../esp/DBLogUtils';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export type RowType = {
  encryptionKey: string;
  keyVersion: string;
  updateTime: string;
  fileTid: string;
  pathId: string;
  path: string;
  fileTypeId: string;
  length: string;
  loadPriority: string;
  annotationFileTid: string;
  readFlag: string;
  numberOfPages: string;
  visibility: string;
  supplFlag: string;
};

export const oldNames = [
  'v_iesp_prereadings_tree_documents',
  'v_iesp_prereadings_tree_meetings',
  'v_iesp_prereadings_tree_reports',
  'v_iesp_overview',
  'v_iesp_exceptions'
];

const MK_Document = 'MK-document';

export class KeyProcessor2 {
  private readonly userId: string;
  private readonly roles: string[];
  private name: string = '';
  private sourcesOnly: boolean = false;
  private readonly startTime = Date.now();

  constructor({ userId, roles }: { userId: string; roles: string[] }) {
    this.userId = userId;
    this.roles = roles;
  }

  public setName(name: string) {
    this.name = name;
  }

  setSourcesOnly(sourceOnly: boolean) {
    this.sourcesOnly = sourceOnly;
  }

  public async selectFiles(parameters: StringRecord): Promise<MobDBFile[]> {
    const sq = ServiceCenter.getInstance().getSq();
    const userId = this.userId;
    const roles = this.roles;
    let result: DbFile[] = [];
  
    if (this.name) {
      const services = await this.getServices(sq, userId, roles);
      if (services.length > 0) {
        result = await this.processServices(services, parameters, userId, roles);
      }
      const docus = await getDocuments(this.name, { userId });
      result = [...result, ...docus];
    }
  
    if (this.sourcesOnly) {
      await this.sourceFiles(result);
      logger.info('sourcesOnly DONE');
    }
  
    logger.info(`userId:${userId}::KEYProcessor2:selectFiles:name:${this.name}::DONE`);
  
    return result;
  }

  private async getServices(sq: any, userId: string, roles: string[]): Promise<string[]> {
    const pr = await sq.run({
      serviceId: 'm.getkeys.getServices',
      parameters: { name: this.name, userId },
      userId,
      roles
    });
  
    if (pr && pr.table?.length > 0) {
      const s = singleValue(pr);
      if (s) {
        return s.split(',').map((e) => e.trim()).filter((e) => !!e);
      }
    }
    return [];
  }
  
  private async processServices(services: string[], parameters: StringRecord, userId: string, roles: string[]): Promise<DbFile[]> {
    let result: DbFile[] = [];
    for (const serviceId of services) {
      const userServiceFiles = await processUserServices({ userId, roles }, parameters, [serviceId]);
      for (const dbFile of userServiceFiles) {
        dbFile.directory = ajustDirectory(serviceId, userId);
        result.push(dbFile);
      }
    }
    return result;
  }

  public async sourceFiles(result: DbFile[]) {
    const sq = ServiceCenter.getInstance().getSq();
    const serviceId = 'm.getkeys.getSourceDocuments';
    const pr1 = await sq.run({
      serviceId,
      parameters: { directory: '/hot/' + '%', exclusionDirectory: '/hot/definitions/' },
      userId: SYSTEM,
      roles: [SYSTEM]
    });
    logger.info(`nr of source files: ${pr1.table?.length}`);
    toList<DbFile>(pr1).forEach((e) => result.push(e));

    //
    // source files 2
    //
    const pr2 = await sq.run({
      serviceId,
      parameters: { directory: '/web/images/', exclusionDirectory: '-' },
      userId: SYSTEM,
      roles: [SYSTEM]
    });

    logger.info(`nr of source files (2): ${pr2.table?.length}`);
    const list2 = toList<DbFile>(pr2);
    list2.forEach((dbFile) => {
      dbFile.directory = '/hot/dbfile/';
      result.push(dbFile);
    });

    //
    // M.SourceServices
    //
    const pr3 = await sq.run({
      serviceId: 'esp.webSite.files',
      parameters: {},
      userId: SYSTEM,
      roles: [SYSTEM]
    });
    if (pr3?.table?.length > 0) {
      toList<DbFile>(pr3).forEach((dbFile) => result.push(dbFile));
    }
  }

  public async processKeydocumentAsPagingResult(parameters: StringRecord, outParameters: StringRecord) {
    const userId = this.userId;
    const userSalt = await getUserSalt(userId);
    if (!userSalt || isSqExceptionResult(userSalt)) {
      logger.error('Can not proceed. No user salt found!');
      return null;
    }

    logger.info('start createGetkeys');

    const allFiles = await this.selectFiles(parameters);

    const buDataDirs: string[] = ['/data/NA/'];

    // CREATE KEYS DOCUMENT

    //
    const rows: StringRecord[] = [];

    const fileTids = new Set<number>();

    for (const dbFile of allFiles) {
      fileTids.add(+dbFile.fileTid);
    }
    const header = [
      'encryptionKey',
      'keyVersion',
      'updateTime',
      'fileTid',
      'pathId',
      'path',
      'fileTypeId',
      'length',
      'loadPriority',
      'annotationFileTid',
      'readFlag',
      'numberOfPages',
      'visibility',
      'supplFlag'
    ];
    const _fileTids: string[] = [];
    for (const dbFile of allFiles) {
      try {
        const directory = buDataDirs.includes(dbFile.directory) ? hot_data : dbFile.directory;

        const row: Partial<RowType> = {};

        logger.debug('file ' + dbFile);

        if (useEncryption(dbFile)) {
          //
          // simplification for performance improvement
          //

          const kv = createKeyString(dbFile.filename, userSalt);

          row.encryptionKey = kv.keyHex;
          row.keyVersion = kv.version;
        } else {
          row.encryptionKey = '';
          row.keyVersion = '0';
        }

        row.updateTime = dbFile.creationTimestamp;
        row.fileTid = dbFile.fileTid;
        row.pathId = getPathId(dbFile);
        row.path = 'docu' + directory + dbFile.filename;

        row.fileTypeId = '';
        row.length = dbFile.length || '0';

        row.loadPriority = calculatePriority(dbFile);

        row.annotationFileTid = dbFile.annotationFileTid ?? '';
        row.readFlag = dbFile.readFlag ?? '';

        const numberOfPages = +dbFile.numberOfPages;
        row.numberOfPages = (numberOfPages >= 0 ? numberOfPages : -1).toString();
        // VISIBILITY
        row.visibility = dbFile.visibility || '1';
        // SUPPL_FLAG
        row.supplFlag = dbFile.supplFlag || 'N';
        rows.push(row);
        _fileTids.push(dbFile.fileTid);
      } catch (e) {
        logger.error(`${toPathString(dbFile)} failed: ${e.toString()}`);
      }
    }

    logger.info(_fileTids.join(','));
    outParameters.fileTidList = _fileTids.join(',');

    logger.info(`Execution time: ${Date.now() - this.startTime}ms`);

    const r: SqResult = {
      name: 'keys',
      type: 'pagingResult',
      username: userId.toLowerCase(),
      size: rows.length,
      totalCount: rows.length,
      hasMore: false,
      rowsAffected: -1,
      dbExecutionTime: 0,
      dbFetchingTime: 0,
      header,
      table: rows.map((e) => header.map((head) => e[head]))
    };
    // for Java compatibility

    return r;
  }

  public static async getBPReports(userId: string): Promise<DbFile[]> {
    logger.warn(`getBPReports ${userId} :: No yet implemented!`);
    return [];
  }

  public static async getESPReports(userId: string): Promise<DbFile[]> {
    logger.warn(`NYI getESPReports ${userId}`);
    return [];
  }
}

async function getDocuments(name: string, { userId }: { userId: string }): Promise<DbFile[]> {
  const sc = ServiceCenter.getInstance();
  const sq = sc.getSq();
  try {
    const serviceId = 'mob.getkeys.getDocuments.' + name;
    if (await sq.driver.hasServiceEntry(serviceId)) {
      const se = await sq.driver.getServiceEntry(serviceId);
      if (!isSqExceptionResult(se)) {
        const pr = await sc.runSystemSq({
          serviceId,
          parameters: { $USERID: userId, $CURRENTMILLIS: Date.now() }
        });
        const msg = `Number of documents found for: ${name}: ${pr.table?.length}`;
        await track('I', msg, MK_Document, userId);
        return toList<DbFile>(pr);
      } else {
        const msg = `No document service found for ${name}`;
        logger.info(msg);
      }
    }
  } catch (e) {
    logger.error(`origin=MK_Document; user:${userId}; processing document service for ${name} -> ${e.message}`);
  }
  return [];
}

export const getUserBasedServices = (): Promise<string[]> => {
  return AppPropertiesService.getInstance().getArray('IESP_USER_BASED_SERVICES');
};
export const getUserBasedServicesReportsOnly = (): Promise<string[]> => {
  return AppPropertiesService.getInstance().getArray('IESP_USER_BASED_SERVICES_REPORTS_ONLY');
};
export const getBuBasedServices = (): Promise<string[]> => {
  return AppPropertiesService.getInstance().getArray('IESP_BU_BASED_SERVICES');
};
