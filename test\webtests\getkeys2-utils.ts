/* eslint-disable no-console */
// called from admin
// serviceId: 'm.Getkeys2',
//     parameters: {
//   name: 'sources',
//       // sources only!
//       sourcesOnly: true,
//       uriVersion: '2'
// },
// ==> java:com.swissre.serviceMobile.lib.Getkeys2

import { expect } from 'chai';
import axios from 'axios';
import { SqResult } from '../../src/serviceQuery';

export async function runGetkeys2(url: string, testname: string): Promise<SqResult> {
  console.log(`Start ${testname} url: ${url}!`);

  const headers = {};

  try {
    const start = Date.now();
    const response = await axios.get(url, { headers });

    console.log(`${testname} successful, time used: ${Date.now() - start}ms`);
    const exceptionMsg = response.data.exceptionMsg;
    if (exceptionMsg) {
      expect.fail(`Exception in ${testname}; ${exceptionMsg}`);
    }
    expect(!!response.data).true;
    return response.data;
  } catch (error) {
    console.error(error);
    expect.fail(`<PERSON>rror received ${testname} ${error}`);
  }
}

export async function runGetBlob(url: string, testname = ''): Promise<Buffer> {
  console.log(`Start ${testname} url: ${url}!`);

  const headers = { responseType: 'blob' };

  try {
    const start = Date.now();
    const response = await axios.get(url, { headers });
    console.log(`${testname} successful, time used: ${Date.now() - start}ms`);
    return Buffer.from(response.data);
  } catch (error) {
    console.error(error);
    expect.fail(`Error received ${testname} ${error}`);
  }
}
