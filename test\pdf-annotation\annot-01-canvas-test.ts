/* eslint-disable no-console */
import { test } from 'mocha';
import { readFileSync, writeFileSync } from 'fs';
import path from 'path';
import { gridPdf, outdataDir, testdataDir } from '../common';
import { pdfAnnotationHandler } from '../../src/service/pdf-annotation/pdf-annotation-handler';

const annot = 'annot-01.json';

test(`annot ${annot} canvas`, async () => {
  await modifyPdf();
});

async function modifyPdf() {
  console.log(`processing ${gridPdf} with ${annot}`);
  const pdfBytesIn = readFileSync(gridPdf);
  const jsonIn = readFileSync(path.join(testdataDir, annot));
  const pdfBytes = await pdfAnnotationHandler(pdfBytesIn, jsonIn);
  writeFileSync(path.join(outdataDir, 'annot-01-canvas.pdf'), pdfBytes);
}
