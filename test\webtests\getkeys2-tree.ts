/* eslint-disable no-console */
// called from admin
// serviceId: 'm.Getkeys2',
//     parameters: {
//   name: 'sources',
//       // sources only!
//       sourcesOnly: true,
//       uriVersion: '2'
// },
// ==> java:com.swissre.serviceMobile.lib.Getkeys2

import { localUrlNode } from '../common';
import { runGetkeys2 } from './getkeys2-utils';

const localUrlNodeStart2 = `${localUrlNode}mobile/getkeys2`;
const names = {
  // cr: 'cr',
  tree: 'tree',
  current: 'current'
  // general: 'general'
  // archived1: 'archived1'
  // archived2: 'archived2'
};

const runDataTest = async (jsonDataService: string) => {
  console.log(`Start Node ${jsonDataService} url: ${localUrlNodeStart2}!`);
  const resultNode = await runGetkeys2(`${localUrlNodeStart2}?name=${jsonDataService}`, 'Node');

  console.log(`node: header        : ${JSON.stringify(resultNode.header)}`);
  console.log(`node: nr of records : ${resultNode.table?.length}`);
  console.log(`node: nr of keys    : ${Object.keys(resultNode).length}`);
};

describe('(webtests) Getkeys tree (node only)', () => {
  for (const name of Object.values(names)) {
    it(`getkeys2 ${name}`, async () => {
      await runDataTest(name);
    });
  }
});
