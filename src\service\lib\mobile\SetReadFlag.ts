import { SqNodeFunction, SqResult } from '../../../serviceQuery';
import Logger<PERSON>enter from '../../../logger/LoggerCenter';
import path from 'path';
import { track } from '../esp/DBLogUtils';
import { deleteUserRequest, saveUserRequest } from '../esp/UserService';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export const SetReadFlag: SqNodeFunction = async function ({ request }): Promise<SqResult> {
  const { parameters, userId } = request;
  if (!userId) {
    return { exception: 'No userid available (SetReadFlag)!' };
  }
  logger.info(`Set Read Flag START : user:${userId} `);
  const setUnread = parameters['setUnread'] === 'true';
  let trackingMessage = 'setUnread: ' + setUnread + ', fileTids: ';
  const documentFileTidList = (parameters['documentFileTidList'] || '').toString().split(',');
  if (documentFileTidList.length === 0) {
    logger.warn('documentFileTidList list is empty!');
  } else {
    if (setUnread) {
      for (const documentFileTid of documentFileTidList) {
        trackingMessage += ` ${documentFileTid}`;
        await deleteUserRequest(userId, 'FILE_TID:' + documentFileTid);
      }
    } else {
      for (const documentFileTid of documentFileTidList) {
        trackingMessage += ` ${documentFileTid}`;
        await saveUserRequest(userId, 'FILE_TID:' + documentFileTid);
      }
    }
  }
  await track('I', trackingMessage.toString(), 'SetReadFlag', userId);
  return { name: 'SetReadFlag' };
};
