import { SqNodeFunction, SqResult } from '../../../serviceQuery';
import ServiceCenter from '../../ServiceCenter';

export const LoadSqSqlFiles: SqNodeFunction = async(): Promise<SqResult> => {
  const sc = ServiceCenter.getInstance();

  const initRepositoryResult = await sc.loadSqSqlFiles();
  const header = ['message', 'content', 'origin'];
  const table: string[][] = [];
  const set = new Set<string>();
  initRepositoryResult.directories.forEach((dir) => {
    table.push(['Number of Services', dir.serviceIds.length.toString(), dir.name]);
    dir.serviceIds.forEach((serviceId) => {
      if (set.has(serviceId)) {
        table.push(['Duplicate!', serviceId, dir.name]);
      }
      set.add(serviceId);
    });
  });
  sc.clearServiceCache();
  return { name: 'LoadSqSqlFiles', header, table };
};
