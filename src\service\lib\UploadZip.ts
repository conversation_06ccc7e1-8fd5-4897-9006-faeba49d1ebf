import { isSqExceptionResult, ServiceFunction, SqResult } from '../../serviceQuery';
import { getDbFile, getDbFileContent, saveDbFile } from './upload-file-utils';
import JSZip from 'jszip';
import { cleanHotDirectory, processSources, SourceFile } from './MUpdateService';
import path from 'path';

const webDirectoryPrefix = '/web/fcrUpload/';
export const UploadZip: ServiceFunction = async({ request }): Promise<SqResult> => {
  const parameters = request.parameters;

  const processInstruction = (parameters.processInstruction || '').toString();
  const mode = (parameters.mode || '').toString();

  const dbFile = await getDbFile((parameters.fileTid || '').toString());
  if (isSqExceptionResult(dbFile)) {
    return dbFile;
  }
  const content = await getDbFileContent(dbFile.fileTid);
  if (isSqExceptionResult(content)) {
    return content;
  }
  if (processInstruction === 'hot') {
    return await processHot(content, mode);
  } else {
    return await processWeb(content);
  }
};

async function processHot(content: Buffer, mode: string): Promise<SqResult> {
  const new_zip = new JSZip();
  // more files !

  const zip = await new_zip.loadAsync(content);
  const table: string[][] = [];
  const hotFiles: SourceFile[] = [];
  for (const k of Object.keys(zip.files)) {
    const ze = zip.files[k];
    if (ze.dir) {
      table.push([ze.name, ze.dir.toString(), 'directory']);
    } else {
      if (k.startsWith('hot/')) {
        const data = await zip.file(k).async('nodebuffer');
        hotFiles.push({ path: k.substring('hot/'.length), data });
        table.push([ze.name, ze.dir.toString(), 'processed']);
      } else {
        table.push([ze.name, ze.dir.toString(), 'skipped']);
      }
    }
  }
  await cleanHotDirectory();
  await processSources(hotFiles, mode === 'force');
  return { table, header: ['name', 'dir', 'message'] };
}

async function processWeb(content: Buffer) {
  const new_zip = new JSZip();
  // more files !
  const zip = await new_zip.loadAsync(content);
  const table: string[][] = [];
  for (const k of Object.keys(zip.files)) {
    const ze = zip.files[k];
    const filename = path.basename(ze.name);
    const directory = webDirectoryPrefix + path.dirname(ze.name) + '/';
    if (ze.dir) {
      table.push(['', '', ze.name, '', 'dirctory']);
    } else {
      const data = await zip.file(k).async('nodebuffer');
      const savedFile = await saveDbFile({
        fileTid: '',
        filename,
        directory,
        data
      });
      if (isSqExceptionResult(savedFile)) {
        table.push(['', filename, directory, '', savedFile.exception]);
      } else {
        table.push([savedFile.fileTid, savedFile.filename, savedFile.directory, '' + data.length, 'saved']);
      }
    }
  }
  return { table, header: ['fileTid', 'filename', 'directory', 'size', 'status'] };
}
