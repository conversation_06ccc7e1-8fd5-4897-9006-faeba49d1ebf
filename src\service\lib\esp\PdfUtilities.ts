import { DbFile } from './types';
import { errorLine, isSqExceptionResult, SqExceptionResult } from '../../../serviceQuery';
import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';
import { getDbFileContent, saveDbFile } from '../upload-file-utils';
import { PDFDocument } from 'pdf-lib';
import { PREVIEW_ICON, saveFileRel, saveFileRelPageNr } from './DBFileRelSupport';
import { createDerivedPreviewFile } from './DBFileHelper';
import { getPDFPreviewInPNG } from './GetPDFPreviewInPNG';
import { updateTaskProgress } from '../task-utils';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export async function processPdfDerivedFiles(documentFile: Partial<DbFile>): Promise<number | SqExceptionResult> {
  const { filename, fileTid } = documentFile;
  if (!filename) {
    return { exception: 'Filename missing!' };
  }
  if (!fileTid) {
    return { exception: 'FileTid missing!' };
  }
  let nrOfPages = -1;
  if (filename.toLowerCase().endsWith('.pdf')) {
    logger.info(`PdfUtilities.getPDFPreviewInPNG :: about to start with ${fileTid}, ${filename}`);
    const documentBytes = await getDbFileContent(fileTid);
    if (isSqExceptionResult(documentBytes)) {
      return {
        ...documentBytes,
        processInfo: {
          lines: [errorLine(`Could not read content for ${fileTid}, ${filename}!`)]
        }
      };
    }
    try {
      // Warning: fetchStandardFontData: failed to fetch file "LiberationSans-Bold.ttf" with "UnknownErrorException: Unable to load font data at: C:\srdev\gitdata\esp-be\node_modules\node_modules\pdfjs-dist\standard_fonts\LiberationSans-Bold.ttf"
      const pdfDoc: PDFDocument = await PDFDocument.load(new Uint8Array(documentBytes), { ignoreEncryption: true });
      //
      // process nrOfPages
      //
      nrOfPages = pdfDoc.getPages().length;
      logger.info(`Number of pages : ${nrOfPages} (${fileTid})`);
      await saveFileRelPageNr(fileTid, nrOfPages);
    } catch (e) {
      logger.error(`processPdfDerivedFiles: Error when trying to read number of pages: ${e} `);
    }
    logger.info('processPdfDerivedFiles: documentBytes.length: ' + documentBytes.length);
    await updateTaskProgress({ fileTid, type: 'fileUpload', progress: 85, msg: 'Generating Preview', status: 'progressing' });
  
    try {
      //
      // process PNG preview
      //
      const fileLabel = `${fileTid}-${filename}`;
      const imageBytes = await getPDFPreviewInPNG(fileLabel, documentBytes);

      if (imageBytes && imageBytes.length > 0) {
        await finalizePreview(fileTid, imageBytes);
      } else {
        console.info('No preview could be created for file : ' + fileTid);
        logger.error(`No preview could be created for file : ${fileTid}, ${filename} `);
      }
    } catch (e) {
      console.error(`processPdfDerivedFiles: Error when creating an icon: ${e} `);
      logger.error(`processPdfDerivedFiles: Error when creating an icon: ${e} `);
    }
  }
  return nrOfPages;
}

export async function finalizePreview(id: string, imageBytes: Buffer) {
  if (imageBytes.length) {
    const iconFile = createDerivedPreviewFile(id);
    const r = await saveDbFile({ ...iconFile, data: imageBytes });
    if (isSqExceptionResult(r)) {
      logger.error(`Error while inserting iconFile into the DB: ${r.exception}!`);
      return;
    }
    const targetTid = r.fileTid;
    logger.info('preview file : ' + targetTid + ' created. (orig: ' + id + ')');
    return saveFileRel('' + id, targetTid + '', 'preview', PREVIEW_ICON, '-1');
  }
}
