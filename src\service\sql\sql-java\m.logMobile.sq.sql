--
-- KEY           = m.logMobile.select
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
--

set:$MAXROWS=100000
;
set:$MAXFETCHROWS=100500
;
set:timeFmt='YYYY-MM-DD HH24:MI:SS'
;
set-if-empty:deviceType=%
;
set-if-empty:logType=%
;
set-if-empty:iespVersion=%
;
set-if-empty:iosVersion=%
;
set-if-empty:userId=%
;
set-if-empty:logTimestamp=%
;
set-if-empty:created=%
;
select 
 LOG_MOBILE_TID, 
 IESP_VERSION, 
 IOS_VERSION, 
 DEVICE_TYPE, 
 LOG_TYPE, 
 LOG_TIMESTAMP,
 USER_ID, 
 LOG_MESSAGE,
 TO_CHAR(CREATED, :timeFmt) as "CREATED"
from ESP.T_LOG_MOBILE
where 
((DEVICE_TYPE like :deviceType and DEVICE_TYPE is not null) or (:deviceType = '%' and DEVICE_TYPE is null))
and
((LOG_TYPE like :logType and LOG_TYPE is not null) or (:logType = '%' and LOG_TYPE is null))
and
((IESP_VERSION like :iespVersion and IESP_VERSION is not null) or (:iespVersion = '%' and IESP_VERSION is null))
and
((IOS_VERSION like :iosVersion and IOS_VERSION is not null) or (:iosVersion = '%' and IOS_VERSION is null))
and
((LOG_TIMESTAMP like :logTimestamp and LOG_TIMESTAMP is not null) or (:logTimestamp = '%' and LOG_TIMESTAMP is null))
and
(USER_ID like :userId or USER_ID is null)
and
TO_CHAR(CREATED, :timeFmt) like :created
order by LOG_TIMESTAMP desc
;

--
-- KEY           = m.logMobile.insert.sql
-- ACCESS_GROUPS = SYSTEM
--

create-tid-for:logMobileTid
;
insert into ESP.T_LOG_MOBILE
(
    LOG_MOBILE_TID,
    IESP_VERSION,
    IOS_VERSION,
    DEVICE_TYPE,
    LOG_TYPE,
    LOG_TIMESTAMP,
    USER_ID,
    LOG_MESSAGE,
    CREATED)
select
    :logMobileTid,
    :iespVersion,
    :iosVersion,
    :deviceType,
    :logType,
    :logTimestamp,
    :$USERID,
    :logMessage,
    current_timestamp where :logMessage <> 'Open Current Meeting'
;
select count(*) from ESP.T_LOG_MOBILE where LOG_MOBILE_TID = :logMobileTid 
;


--
-- KEY           = m.logMobile.get
-- ACCESS_GROUPS = ESP_ADMIN
--

set:timeFmt='YYYY-MM-DD HH24:MI:SS'
;
select LOG_MOBILE_TID,
       IESP_VERSION,
       IOS_VERSION,
       DEVICE_TYPE,
       LOG_TYPE,
       LOG_TIMESTAMP,
       USER_ID,
       LOG_MESSAGE,
       TO_CHAR(CREATED, :timeFmt) as "CREATED"
from ESP.T_LOG_MOBILE
where LOG_MOBILE_TID = :logMobileTid
