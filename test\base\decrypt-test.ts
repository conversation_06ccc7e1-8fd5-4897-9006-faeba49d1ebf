/* eslint-disable no-console */
import { test } from 'mocha';
import { decrypt, encrypt } from '../../src/service/lib/mobile/crypto-utils';
import { expectBufferEquals } from '../test-utils';

const en =
  'ZUs2GTnaFilMKmdENMZaFqqexITduUOx9VGAJWT5lHBNJyUg0ApfvCaBUwLfCAEBvdQUNiVpUqNX17aj8OwlyzyExgM9HxKLmYy1draqa6aH2o86hXLf1/GyRFy2Ss4DKyviLEy1TZQdFHzimd7ghw5CZe1pMzCGAsM/Z4JKHxlwjU0vYUudbrTLUhYXwxuSUsZt3MIGqQT46ApxgrauqSODolGTbEccPNAV4eXvivz+euAa3YLsmvQ2ZHAGbpysuBHJAh7Mv9pYhlXwIdkJZxEcgmw1aY//************************************/7xHypgzo6SBNX2eS5rRW8raa7egBUkyWxLCQm6HHTTqBKOGmG2xCqZUcQtLP7d8BpC01Sd+/IJ1x2zAEYvrwu20Fwohvfd8pn3nakX32323FcyMYhlDreyky2gjic0KRRPDeKXn5ENrekK8XfEh43ECCb+TGf12cuUJcTkWYqLnTg4paA1bzv3KGNEl';
const key = 'f4d201a82c9bcf9d0140f8a84b11c6f9';
test('decrypt test', async () => {
  const buff = Buffer.from(en, 'base64');
  console.log(`len: ${buff.length}`);
  const decbuff = decrypt(key, buff);
  console.log(decbuff.toString('utf8'));
  const en2 = encrypt(key, decbuff);
  expectBufferEquals(buff, en2);
});
