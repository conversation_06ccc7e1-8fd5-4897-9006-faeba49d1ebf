/* eslint-disable space-before-function-paren */
import { SqFunctionArg, SqNodeFunction, SqResult } from '../../../serviceQuery';
import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';
import ServiceCenter, { newTid } from '../../ServiceCenter';
import { $REQUESTID, ANONYMOUS } from '../../constants';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export const MP_TYPE__insert: SqNodeFunction = async ({ request }: SqFunctionArg): Promise<SqResult> => {
  logger.debug('start MP_TYPE__insert');
  const { userId = ANONYMOUS } = request;
  const sq = ServiceCenter.getInstance().getSq();
  const driver = sq.driver;
  const selectByTypeId = await driver.processSql(
    'select TYPE_ID as VALUE from ESP.T_MP_TYPE where TYPE_ID = :typeId',
    request.parameters
  );
  if (selectByTypeId?.table?.length === 0) {
    let parameters = request.parameters;
    const requestId = request.parameters[$REQUESTID];
    if (!requestId) {
      const newId = await newTid();
      parameters = { ...request.parameters, [$REQUESTID]: newId };
    }
    await sq.run({ serviceId: 'mpType.insert', parameters, userId, roles: request.roles });
    return await sq.run({ serviceId: 'CreateMPMessages', parameters, userId, roles: request.roles });
  }

  return {};
};

//
// CreateMPMessages
//
//
// --
//     -- KEY = MP_TYPE.insert
// -- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
// --
//
//     [
//       "create-tid-if-empty-for:$REQUESTID"
//       ,
//       {
//         'switch' : "select TYPE_ID as VALUE from ESP.T_MP_TYPE where TYPE_ID = :typeId",
//         'default' : {
//           'userMessage' : 'Warning:--- Record already exists! ---'
//         },
//         'null' : "call ESP.SP_MP_TYPE_NEW(:typeId, :name, :roleReader, :linkReader, :roleAdmin, :linkAdmin, :mpType, :$REQUESTID) ; set:crudMode=insert"
//       },
//
//       "serviceId:CreateMPMessages" ]
