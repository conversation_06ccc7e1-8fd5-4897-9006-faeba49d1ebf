import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';
import { PDFToImage, PDFToIMGOptions } from 'pdf-to-image-generator';

const logger = LoggerCenter.getLogger(path.basename(__filename));

const options: PDFToIMGOptions = {
    // the name of the folder where files will be rendered
    includeBufferContent: true,
    // controls scaling
    viewportScale: 2,
};

export async function getPDFPreviewInPNG(label: string, content: Buffer) {
  try {
    const pdf = await new PDFToImage().load(content);
    const pdfConversion = await pdf.convert(options);
    if (!!pdfConversion && pdfConversion[0].content) {
      return pdfConversion[0].content;
    } else {
      logger.error(`Could not create image for ${label}`);
    }
  } catch (reason) {
    logger.error(`Error creating image for ${label}: ${reason}`);
  }
}
