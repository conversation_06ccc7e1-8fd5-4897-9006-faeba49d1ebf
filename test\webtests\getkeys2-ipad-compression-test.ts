// called from admin
// serviceId: 'm.Getkeys2',
//     parameters: {
//   name: 'sources',
//       // sources only!
//       sourcesOnly: true,
//       uriVersion: '2'
// },
// ==> java:com.swissre.serviceMobile.lib.Getkeys2

import { test } from 'mocha';
import { localUrlNode } from '../common';
import axios from 'axios'; // import pako from 'pako';
// import pako from 'pako';

const getkeysUri = `${localUrlNode}mobile/getkeys2`;
const startUri = `${localUrlNode}mobile/start2`;
const getSources = 'm.Getkeys2 sources';
test(`(webtests) ${getSources}`, async () => {
  const name = 'sources';

  console.log(`Start Node ${getSources} url: ${getkeysUri}!`);

  const urls = [`${getkeysUri}?sourcesOnly=true&compression=gzip&name=${name}`, `${startUri}?compression=gzip`];

  for (const url of urls) {
    console.log(`start ${url}`);
    const response = await axios.get(url, { headers: {} });
    // variante
    // const data = pako.ungzip(response.data);
    // const json = Buffer.from(data).toString();
    console.log(`result from ${url}`);
    console.log(JSON.stringify(response.data).substring(0, 120));
  }
});
