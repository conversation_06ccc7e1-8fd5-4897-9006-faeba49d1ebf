import { SessionCache } from '../../SessionCache';
import { SqNodeFunction, SqResult } from '../../../serviceQuery';
import { send } from '../EMailService';
import { getDbFile, getDbFileContent } from '../upload-file-utils';
import { AttachmentData } from '@sendgrid/helpers/classes/attachment';

const fromEmailAddress = process.env.FROM_EMAIL_ADDRESS;

export const MailServiceTester: SqNodeFunction = async({ request }): Promise<SqResult> => {
    const { parameters: { subject, body, fileTid }, userId } = request;
    const recipients = [];
    const attachments = new Array<AttachmentData>();

    if (userId.toString().indexOf('@') === -1) {
        const userEmail = SessionCache.getInstance().getEmail(userId.toString());
        recipients.push(userEmail);
    }
    if (fileTid) {
        const fileContent = await getDbFileContent(fileTid.toString());
        const f = await getDbFile(fileTid.toString());

        attachments.push({
            content: fileContent.toString('base64'),
            filename: f.filename,
            disposition: 'attachment'
        });
    }
    await send(recipients, fromEmailAddress, subject.toString(), body.toString(), attachments);
    return { };
};
