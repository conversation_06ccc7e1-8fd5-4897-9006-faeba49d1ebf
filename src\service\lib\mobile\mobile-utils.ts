import { randomBytes } from 'node:crypto';

import { SqExceptionResult, SqResult, toList } from '../../../serviceQuery';
import { SYSTEM } from '../../constants';
import ServiceCenter from '../../ServiceCenter';
import { UserSalt } from './types';

export async function getUserSalt(userId: string): Promise<UserSalt | SqExceptionResult> {
  const sq = ServiceCenter.getInstance().getSq();
  let res0: SqResult = await sq.run({
    serviceId: 'esp.userSalt.get',
    parameters: { userId },
    userId: SYSTEM,
    roles: [SYSTEM]
  });
  if (!res0?.table?.length) {
    const res1: SqResult = await sq.run({
      serviceId: 'esp.userSalt.insert',
      parameters: { userId, version: '1000', salt: createSecureSalt(32), createdUt: Date.now(), $USERID: userId },
      userId: SYSTEM,
      roles: [SYSTEM]
    });
    if (res1.rowsAffected !== 1) {
      return { exception: 'Could not insert user salt! Insert failed!' };
    }
    res0 = await sq.run({
      serviceId: 'esp.userSalt.get',
      parameters: { userId },
      userId: SYSTEM,
      roles: [SYSTEM]
    });
  }
  return toList<UserSalt>(res0)[0];
}

export function createSecureSalt(byteCount: number) {
  const secureRandomBytes = randomBytes(byteCount);
  return secureRandomBytes.toString('hex');
}
