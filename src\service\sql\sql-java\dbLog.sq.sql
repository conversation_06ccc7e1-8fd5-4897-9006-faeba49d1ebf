-- noinspection SqlDialectInspectionForFile

--
-- DBLog services
--
--     

--
-- KEY           = dbLog.insert
-- ACCESS_GROUPS = SYSTEM
--

create-tid-for:logTid
;
set-if-empty:severity=D
;
insert into ESP.T_LOG (
    LOG_TID, ERR_SEVERITY, ERR_SP_NAME, ERR_SP_PART, ERR_ORIGIN, SESSION_ID, ERR_MESSAGE, ERR_TIMESTAMP, USER_ID)
values
    (cast(:logTid as numeric), :severity, :spName, :spPart, :origin,
     cast(:sessionId as numeric), :message, current_timestamp, :userId)
;


--
--  KEY           = dbLog.select
--  ACCESS_GROUPS = ESP_ADMIN
--

set-if-empty:errSeverity=%
;
set-if-empty:errSpName=%
;
set-if-empty:errSpPart=%
;
set-if-empty:errSqlcode=%
;
set-if-empty:errMessage=%
;
set-if-empty:errTimestamp=%
;
set-if-empty:errOrigin=%
;
set-if-empty:userId=%
;
set-if-empty:$MAXROWS=10000
;
select LOG_TID,
       TO_CHAR(ERR_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS') as ERR_TIMESTAMP,
       USER_ID,
       ERR_SEVERITY,
       ERR_SP_NAME,
       ERR_SP_PART,
       ERR_SQLCODE,
       SESSION_ID,
       ERR_MESSAGE,
       ERR_ORIGIN
from ESP.T_LOG
where (USER_ID like :userId
    or
       ((:userId = '%' or :userId = '%%') and USER_ID is null)
    )
  and ERR_SEVERITY like :errSeverity
  and (ERR_SP_NAME like :errSpName
    or
       (:errSpName = '%' and ERR_SP_NAME is null))
  and (ERR_SP_PART like :errSpPart
    or
       (:errSpPart = '%' and ERR_SP_PART is null))
  and (
    ERR_MESSAGE like :errMessage
        or
    (:errMessage = '%' and ERR_MESSAGE is null)
    )
  and TO_CHAR(ERR_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS') like :errTimestamp
  and (cast(ERR_SQLCODE as varchar) like cast(:errSqlcode as varchar)
    or
       ERR_SQLCODE is null)
  and ((ERR_ORIGIN like :errOrigin and ERR_ORIGIN is not null) or (:errOrigin = '%' and ERR_ORIGIN is null))
order by LOG_TID desc
limit :$MAXROWS
;

--
-- KEY           = dbLog.get
-- ACCESS_GROUPS = ESP_ADMIN
--

select
LOG_TID,
TO_CHAR(ERR_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS')
as ERR_TIMESTAMP,
USER_ID,
ERR_SEVERITY, ERR_SP_NAME, ERR_SP_PART,
ERR_SQLCODE, SESSION_ID, ERR_MESSAGE, ERR_ORIGIN
from
ESP.T_LOG where LOG_TID  = :logTid

--
--
-- Log Tracking
--
--     
--


--
-- KEY           = dbLog.tracking.insert
-- ACCESS_GROUPS = SYSTEM
--

create-tid-for:logTid
;
set-if-empty:userId=SYSTEM
;
insert into ESP.T_LOG 
(LOG_TID, ERR_SEVERITY, ERR_ORIGIN, ERR_MESSAGE, ERR_TIMESTAMP, USER_ID, SESSION_ID, ERR_SP_NAME) 
values 
(:logTid, :errSeverity, :errOrigin, :errMessage, current_timestamp, :userId, -12345, 'tracking')

;

--
--  KEY           = dbLog.tracking.select
--  ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
--

set:$MAXROWS=10000
;
set:$MAXFETCHROWS=10000
;
set-if-empty:errSeverity=%
;
set-if-empty:errMessage=%
;
set-if-empty:errOrigin=%
;
set-if-empty:errTimestamp=%
;
set-if-empty:userId=%
;
select 
LOG_TID,
TO_CHAR(ERR_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS') as "ERR_TIMESTAMP",
ERR_SEVERITY,
ERR_MESSAGE, 
ERR_ORIGIN,
USER_ID 
from ESP.T_LOG 
where 
((USER_ID like :userId and USER_ID is not null) or :userId = '%' or :userId = '%%' or (USER_ID is null and :userId = '-1'))
and
ERR_SEVERITY like :errSeverity
and
SESSION_ID = -12345
and
ERR_MESSAGE like :errMessage
and
ERR_ORIGIN like :errOrigin
and
TO_CHAR(ERR_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS') like :errTimestamp
order by 
LOG_TID desc
;
