/* eslint-disable */
import { test } from 'mocha';
import { getEncrContent, getMeta, getNrOfFiles, getOrigContent } from './crypto-utils';
import { expect } from 'chai';
import { createHash } from 'crypto';
import { createKeyString, encrypt } from '../../src/service/lib/mobile/crypto-utils';
import { getUserSalt } from '../../src/service/lib/mobile/mobile-utils';
import { isSqExceptionResult } from '../../src/serviceQuery';

test(`Read test file and compare sha to the meta data`, async () => {
  const userId = 'SRZXLM';

  const userSalt = await getUserSalt(userId);
  if (isSqExceptionResult(userSalt)) {
    expect.fail(`Could not get UserSalt for ${userId}!`);
    return;
  }

  const nrOfFiles = getNrOfFiles() / 3;
  for (let index = 1; index <= nrOfFiles; index++) {
    const meta = getMeta(index);

    if (!meta) {
      continue;
    }

    expect(!!meta.encryptionKey).true;
    const { keyHex } = await createKeyString(meta.filename, userSalt);

    expect(meta.encryptionKey).equals(keyHex);

    const encr = getEncrContent(index);

    const sha1Hash = createHash('sha1');
    sha1Hash.update(encr);
    const sha1HashResult = sha1Hash.digest('hex');
    console.log(`java: meta: ${meta.sha1hash}, calculated: ${sha1HashResult}`);
    expect(meta.sha1hash).equals(sha1HashResult);

    // get unencrypted data
    const orig = getOrigContent(index);

    // encrypt with node
    const nodeEnc = encrypt(meta.encryptionKey, orig);
    const sha1HashNodeEnc = createHash('sha1');
    sha1HashNodeEnc.update(nodeEnc);
    const sha1HashResultOrig = sha1HashNodeEnc.digest('hex');
    console.log(`node: meta: ${meta.sha1hash}, calculated: ${sha1HashResultOrig}`);

    expect(meta.sha1hash).equals(sha1HashResultOrig);
  }
});
