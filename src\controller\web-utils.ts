import { Request } from 'express';
import xss from 'xss';
import { ServiceQuery } from '../serviceQuery';
import { parseRequestDevice } from '../utils';
import { UserProfile } from '../service/types';
import LoggerCenter from '../logger/LoggerCenter';
import path from 'path';

import { StringRecord } from '../types';
import { SessionCache } from '../service/SessionCache';
import { resolveUidFromAuthorizationBearer } from '../auth/sso/resolve-authorisation-bearer';
import ServiceCenter from '../service/ServiceCenter';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export function toParameters(req: Request, userId: string): StringRecord {
  const parameters: Record<string, string> = {};
  fillParameters(parameters, req.query);
  fillParameters(parameters, req.body);
  const deviceInfo = parseRequestDevice(req.get('user-agent'));
  parameters.BROWSER = deviceInfo.BROWSER;
  parameters.DEVICE = deviceInfo.DEVICE;
  parameters.IOSVERSION = deviceInfo.IOSVERSION;
  parameters.$CURRENTMILLIS = Date.now() + '';
  parameters.USERAGENT = req.get('user-agent') || '';
  parameters.$USERID = userId;
  return parameters;
}

export function fillParameters(parameters: Record<string, string>, params: any) {
  if (typeof params === 'object') {
    for (const [name, value] of Object.entries(params)) {
      if (value === undefined || value === null) {
        continue;
      }
      if (Array.isArray(value)) {
        parameters[name + '_array'] = xss(value.join(','));
      } else {
        parameters[name] = name === 'content' ? value.toString() : xss(value.toString());
      }
    }
  }
}

const userProfileCache = new Map<string, UserProfile>();

export async function getUserProfile(sq: ServiceQuery, request: Request): Promise<UserProfile> {
  let userId;

  if (ServiceCenter.getInstance().isLocal()) {
    userId = process.env.LOCAL_USER_ID;
  } else {
    userId = await resolveUidFromAuthorizationBearer(request);
  }
  if (!userId) {
    return null;
  }

  logger.info('userId', userId);
  const userEmail = SessionCache.getInstance().getEmail(userId);
  // logger.info('userEmail', userEmail);
  const roles = await SessionCache.getInstance().getRoles(userId);
  const userProfile: UserProfile = { userId, userEmail, roles, created: 0 };
  userProfileCache.set(userId, userProfile);
  return userProfile;
}
