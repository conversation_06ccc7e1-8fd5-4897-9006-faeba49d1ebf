



--
-- KEY           = UserInfo
--  

java:com.swissre.serviceQuery.lib.UserInfo

--
-- KEY           = zz-user.get
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

select upper(user_id) as user_id from esp.t_sr_user where lower(email_address) = lower(:userEmail)
;

--
-- KEY           = userData.get
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM  
--  

select USER_ID, FIRST_NAME, LAST_NAME from ESP.V_SR_USER
where upper(USER_ID) = upper(:userId)
;


--
-- KEY           = zz-userPref.select
-- ACCESS_GROUPS_XXX = ESP_ADMIN,SYSTEM
--

set-if-empty:userId=%
;
set-if-empty:userName=%
;
set-if-empty:prefName=%
;
select
  p.USER_ID,
  coalesce(u.LAST_NAME,  '-na-') || ', ' || coalesce(u.FIRST_NAME, '-na-') as USER_NAME,
  p.PREF_NAME,
  p.PREF_VALUE
from ESP.T_USER_PREF p
       left join ESP.V_SR_USER u on (p.USER_ID = u.USER_ID  )
where upper(p.USER_ID) like upper(:userId)
  and upper(p.PREF_NAME) like upper(:prefName)
  and (
      upper(u.FIRST_NAME) like upper(:userName) OR
      upper(u.LAST_NAME) like upper(:userName) OR
      (u.FIRST_NAME is null and :userName = '%%'))
order by u.LAST_NAME, u.FIRST_NAME
;


--
-- KEY           = userPref.get
-- ACCESS_GROUPS = ESP_ADMIN
--

select p.USER_ID, 
coalesce(u.LAST_NAME, '-na-') || ', ' || coalesce(u.FIRST_NAME, '-na-') as USER_NAME,
p.PREF_NAME, p.PREF_VALUE 
from ESP.T_USER_PREF p 
left join ESP.V_SR_USER_ALLu on (p.USER_ID = u.USER_ID) 
where p.USER_ID = :userId AND p.PREF_NAME = :prefName



--
-- KEY           = userPref.update
-- ACCESS_GROUPS = ESP_ADMIN
--

update ESP.T_USER_PREF set PREF_VALUE = :prefValue where USER_ID = :userId AND PREF_NAME = :prefName



--
-- KEY           = userPref.delete
--

delete from ESP.T_USER_PREF where USER_ID = :userId AND PREF_NAME = :prefName



--
-- KEY           = userPref.insert
--

insert into ESP.T_USER_PREF (USER_ID, PREF_NAME, PREF_VALUE) VALUES (:userId,:prefName,:prefValue)



--
-- KEY           = MyAccessProfile
--

java:com.swissre.serviceQuery.lib.MyAccessProfile




--
-- KEY           = userRequest.save
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM  
-- 
delete from ESP.T_USER_REQUEST where USER_ID = :userId AND  REQUEST_ID = :requestId 
;
insert into ESP.T_USER_REQUEST (USER_ID, REQUEST_ID, REQUEST_TIME, BU_ID) 
values 
(:userId, :requestId, CURRENT_TIMESTAMP, 'NA')
ON CONFLICT (USER_ID, REQUEST_ID)
DO UPDATE SET REQUEST_TIME = CURRENT_TIMESTAMP, BU_ID = 'NA'
;


--
-- KEY           = userRequest.delete
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM  
-- 

delete from ESP.T_USER_REQUEST 
where 
USER_ID = :userId 
AND  
REQUEST_ID = :requestId 
;


--
-- KEY = userFileAccess
--

select ACCESS_RIGHT_TID 
from ESP.V_MP_ACCESS_FILE_CURR_USER
where FILE_TID = :fileTid
;


--
-- KEY           = vUsersAll.select
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM  
--

select * from ESP.V_SR_USER



--
-- KEY           = Users
--  

java:com.swissre.serviceQuery.lib.Users

