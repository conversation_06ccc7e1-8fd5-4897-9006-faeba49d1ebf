# GLOBAL CONFIG ITEMS

# project's APMID (required)
# Type: string
apmid: esp

# Path to a Paketo bindings directory (see also
# https://paketo.io/docs/howto/configuration/#bindings")
# Type: string
bindings:

# Paketo builder/buildpack config values (see also
# https://paketo.io/docs/reference/configuration/#environment-variables)
# Type: a map of names with strings
builder-envs:
  # BPE_PROFILE: dev

# type of Paketo builder base image (default "base")
# (see also https://paketo.io/docs/concepts/builders/)
# Type: string
builder: full

# Paketo buildpacks to be added (make sure you understand how to configure the
# buildpacks you use: https://paketo.io/docs/reference/configuration/)
# (see also https://paketo.io/docs/concepts/buildpacks/)
# Type: list of Paketo buildpack names
buildpacks:
#  - gcr.io/paketo-buildpacks/nodejs

# clear Paketo image's associated cache before building
clear-cache: false

# using the proxy in Paketo builder (if not defined, it will auto-detect by default)
# NOTE: This setting doesn't replace the system ENV vars HTTP(S)_PROXY
# that have to be independently configured
# Type: bool
proxy: 

# component name (required)
# Type: string
component: esp-be

# prevent modifying actions to happen (default false)
# Type: bool
dry-run: false

# deployment manifest template file (default "deployment-dev.yaml")
# Type: string
manifest: ./deployment.yaml

# path to the sources to be package (can contain wildcards) (default "./")
# Type: string
path: .

# name of target container registry resource (default is stratumpub.azurecr.io)
# Type: string
registry: stratumpub.azurecr.io

# target stage name (required)
# RECOMMENDATION: We suggest to NOT have a 'stage' in this config file as it
# might not be the one you actually want to have if you forgot to supply it as
# a option flag on the command line. Exeption: you ever only have one single stage
# Type: string
stage: 

# tag for the docker image (REQUIRED)
# RECOMMENDATION: We suggest to NOT have a 'tag' in this config file as it
# might not be the one you actually want to have if you forgot to supply it as
# a option flag on the command line. Exception: NONE
# Type: string
tag: 

# digest for the docker image
# RECOMMENDATION: We suggest to NOT have a 'digest' in this config file as it
# might not be the one you actually want to have if you forgot to supply it as
# a option flag on the command line.
# Type: string
digest: 

# path to a yaml/json file which values will be provided to the templating process of the manifest file
# Type: string
template-data:

# use the 'temp' folder for a 'kubectl' configuration file (provided by
# 'connect' command) instead of using default location ($HOME/.kube/config) (default false)
# Type: bool
temp-kube-config: false

# directory to store temporary files (default OS location)
# Type: string
temp: 

# increase command output (default is false)
# Type: bool
verbose: false
