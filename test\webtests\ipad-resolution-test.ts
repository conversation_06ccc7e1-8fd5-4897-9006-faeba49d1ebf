/* eslint-disable */
import { test } from 'mocha';
import { prodUrlJava } from '../common';
import axios from 'axios';
import { expect } from 'chai';

const url = `${prodUrlJava}serviceQuery/m.Getkeys2`;

test(`(webtests) ${url}`, async () => {
  console.log(`Start Java url: ${url}!`);
  const headers = {
    cookie:
      'X-TOKEN=T1579506654062214978;LtpaToken2=crwAudhA3ZO88uLZhiVFbqK5gm+fDSaYgkQ7wyYxgKS7FCVogXcDP/yzg4EdPvfPH3pIoiu/KSRIjdSody2NVu1qxedI6MOpiJrwWL8uc0JxCGe3/RuJilkYqcJVD3RI93FlzlkaekoLlPdJpQbK05XKldFf1ubdq4hC+gB3+/Bd58/1+FIWVlGo9GipJ9W2LkDs9paABLzGMJNaCMf9DIpCxj71cZJKfktW/J8TvhxZl3cPp26JaMBtMUUGkmgL6OHC0DUdh0QzHKB+J5hzPC9sw+hW9YcKl8gWICOufGcrEsHrOax1o4ZB9C+aErEimigKaVcf+Z/am/g1C9M4OapNmXfPIK4gOoy9FmaPiMZ7as+SiEegA1a5LFfwaR3ked964P/C1XCVUCjBAZKrVw=='
  };
  const response = await axios.get(url, { headers });
  console.log('select-app-properties Done');
  expect(!!response.data).true;
  expect(!!response.data.header).true;
  expect(response.data.header.length === 2).true;
});
