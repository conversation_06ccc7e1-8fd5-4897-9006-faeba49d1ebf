import { createS<PERSON><PERSON><PERSON><PERSON> } from 'node:crypto';
import { createCipheriv, createDecipheriv, createHash } from 'crypto';
import { KeyVersion, UserSalt } from './types';
import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export function encrypt(encryptionKey: string, data: Buffer) {
  if (data === undefined || data === null || data.length === 0) {
    logger.warn('Encrypt data is null');
    return null;
  }
  // Convert the key to bytes using UTF-8 encoding
  const secretKey = Buffer.from(encryptionKey, 'utf8');

  const aesKey = createSecretKey(secretKey);

  // Create AES cipher in ECB mode with no padding
  const cipher = createCipheriv('aes-256-ecb', aesKey, null);
  cipher.setAutoPadding(false);

  const blockSize = 16; // AES block size
  let paddingLength = blockSize - (data.length % blockSize);
  paddingLength = paddingLength === blockSize ? 0 : paddingLength;
  const padding = Buffer.alloc(paddingLength, 0);
  const paddedData = Buffer.concat([data, padding]);

  // Encrypt the data
  let encrypted = cipher.update(paddedData);
  encrypted = Buffer.concat([encrypted, cipher.final()]);

  return encrypted;
}

// TODO : test needed
export function decrypt(encryptionKey: string, encryptedData: Buffer) {
  // Convert the key to bytes using UTF-8 encoding
  const secretKey = Buffer.from(encryptionKey, 'utf8');

  const aesKey = createSecretKey(secretKey);

  // Create AES decipher in ECB mode with no padding
  const decipher = createDecipheriv('aes-256-ecb', aesKey, null);
  decipher.setAutoPadding(false);

  // Decrypt the data
  let decrypted = decipher.update(encryptedData);
  decrypted = Buffer.concat([decrypted, decipher.final()]);

  // Remove padding (assuming padding was added during encryption)
  const blockSize = 16;
  const paddingLength = decrypted[decrypted.length - 1];

  if (paddingLength > 0 && paddingLength <= blockSize) {
    return decrypted.slice(0, decrypted.length - paddingLength);
  } else {
    return decrypted;
  }
}

export function getExtension(filename: string) {
  let e: string = path.extname(filename);
  if (e.length > 0 && e[0] === '.') {
    e = e.substring(1);
  }
  return e;
}

export function createKeyString(filename: string, { userId, salt, version }: UserSalt): KeyVersion {
  const kv: KeyVersion = { keyHex: '', version };
  const ext = getExtension(filename);
  kv.keyHex = createKeyStringByExtension(salt, ext, userId);
  return kv;
}

function createKeyStringByExtension(salt: string, extension: string, userId: string): string {
  return createKeyString0(0, salt + extension, userId);
}

export function createKeyString0(fileTid: number, salt: string, userId: string): string {
  const input = fileTid + salt + userId.toUpperCase();
  const hash = createHash('sha1');
  const buff: Buffer = Buffer.from(input, 'utf8');
  hash.update(buff);
  const h = hash.digest();
  const hs = h.toString('hex') + '12345678901234567890123456789012';
  return hs.substring(0, 32);
}

export function toSha1(input: string | Buffer): string {
  const hash = createHash('sha1');
  let buff;
  if (typeof input === 'string') {
    buff = Buffer.from(input, 'utf8');
  } else {
    buff = input;
  }
  hash.update(buff);
  const h = hash.digest();
  const hs = h.toString('hex');
  return hs;
}
