import ServiceCenter from '../ServiceCenter';
import { PoolClient } from 'pg';
import { PSQLDriverExtension } from '../../serviceQuery/PSQLDriver';
import LoggerCenter from '../../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export const isLocked = async (lockName: string): Promise<boolean> => {
  const pgDriver = ServiceCenter.getInstance().getSq().driver;
  const r1 = await pgDriver.processSql(selectSync, {
    lockName
  });
  return r1?.table?.length > 0;
};
const lockSync = 'lock table esp.t_sync IN EXCLUSIVE mode';
export const selectSync = 'select current_timestamp, tme, details from esp.t_sync where NME = :lockName';
export const insertSync = 'insert into esp.t_sync (TME, NME, DETAILS) values (CURRENT_TIMESTAMP, :lockName, :details)';
export const deleteSync = 'delete from esp.t_sync where NME= :lockName';

export const getLock = async (lockName: string, details: string): Promise<boolean> => {
  const pgDriver = ServiceCenter.getInstance().getSq().driver as PSQLDriverExtension;
  let con: PoolClient | undefined;
  try {
    con = await pgDriver.getConnection();
    let ok = false;
    await con.query('BEGIN');
    await pgDriver.processSql_con(con, lockSync, { lockName });
    const selectRes = await pgDriver.processSql_con(con, selectSync, { lockName });
    if (selectRes?.table.length === 0) {
      const r1 = await pgDriver.processSql_con(con, insertSync, { lockName, details });
      ok = r1.rowsAffected === 1;
    }
    await con.query('COMMIT');
    return ok;
  } catch (e) {
    await con.query('ROLLBACK');
    logger.error(e.message);
  } finally {
    if (con) {
      pgDriver.returnConnection(con);
    }
  }
};

export const releaseLock = async (lockName: string): Promise<void> => {
  const pgDriver = ServiceCenter.getInstance().getSq().driver as PSQLDriverExtension;
  let con: PoolClient | undefined;
  try {
    con = await pgDriver.getConnection();
    await con.query('BEGIN');
    await pgDriver.processSql_con(con, deleteSync, { lockName });
    await con.query('COMMIT');
    logger.info(`Lock ${lockName} released!`);
  } catch (e) {
    await con.query('ROLLBACK');
    logger.error(e.message);
  } finally {
    if (con) {
      pgDriver.returnConnection(con);
    }
  }
};
