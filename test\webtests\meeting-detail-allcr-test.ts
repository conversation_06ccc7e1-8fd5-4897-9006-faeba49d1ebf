import { test } from 'mocha';
import * as dotenv from 'dotenv';
import { localUrlJava, localUrlNode } from '../common';
import axios from 'axios';
import { compareJson } from '../test-utils';
import { toList } from '../../src/serviceQuery';

dotenv.config();
const serviceId = 'meeting_detail_allcr';
const sqPart = `serviceQuery/${serviceId}`;
const localUrlJavaStart2 = `${localUrlJava}${sqPart}`;
const localUrlNodeStart2 = `${localUrlNode}${sqPart}`;
test(`(webtests) meeting-detail-allcr-test`, async () => {
  console.log(`Start Java ${serviceId} url: ${localUrlJavaStart2}!`);
  console.log(`Start Node ${serviceId} url: ${localUrlNodeStart2}!`);

  const resultJava = toList((await axios.get(localUrlJavaStart2, {})).data);
  const resultNode = toList((await axios.get(localUrlNodeStart2, {})).data);

  compareJson(resultNode, resultJava, {
    ignore: ['dbExecutionTime', 'dbFetchingTime'],
    isJsonString: []
  });
});
