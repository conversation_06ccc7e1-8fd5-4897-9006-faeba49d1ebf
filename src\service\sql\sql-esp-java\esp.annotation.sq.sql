--
-- KEY           = esp.annotationDownload.select
-- ACCESS_GROUPS = SYSTEM
--

select FILE_TID, ANNOTATION_FILE_TID
from (select d.FILE_TID,
             (select max(FILE_TID) " + "
              from " + SCH + ".T_FILE
              where directory =
                    '/.derived/' || d.FILE_TID || '/' || ? || '/'
                and filename not like '%.pdf'
              group by FILENAME, DIRECTORY) as "ANNOTATION_FILE_TID"
      from ESP.T_FILE d
      where d.FILE_TID in (:fileTids[]))
where ANNOTATION_FILE_TID is not null
;

--
-- KEY           = esp.annotation.selectDocsWithAnnotations
--

select d.FILE_TID    as DOC_TID,
       d.FILENAME       DOC_NAME,
       ad.FILE_TID  ANNOT_DOC_TID,
       ad.FILENAME  ANNOT_DOC_NAME,
       TO_CHAR(ad.CREATION_TIMESTAMP, 'YYYY-MM-DD HH24:MI') AS ANNOT_DOC_CREATION_TIMESTAMP,
       af.FILE_TID  ANNOT_FILE_TID,
       af.FILENAME  ANNOT_FILE_NAME,
       TO_CHAR(af.CREATION_TIMESTAMP, 'YYYY-MM-DD HH24:MI') AS ANNOT_FILE_CREATION_TIMESTAMP

from esp.T_FILE_REL r1
         left join esp.T_FILE d on d.FILE_TID = r1.FILE_TID
         left join esp.T_FILE ad on ad.FILE_TID = r1.TARGET_TID
         left join esp.T_FILE_REL r2 on r2.FILE_TID = d.FILE_TID and r2.REL_TID = -101
         left join esp.T_FILE af on af.FILE_TID = r2.TARGET_TID
where r1.REL_TID = -102
  and ad.OWNER = :$USERID
  and r1.TARGET_VALUE = :$USERID
  and r2.TARGET_VALUE = :$USERID
order by ad.FILE_TID  desc
;

--
-- KEY           =esp.AnnotationFile
--

java:com.swissre.esp.service.queryServices.AnnotationFile
;


--
-- KEY           =test.selectAnnotationsAndDocuments
-- ACCESS_GROUPS = SYSTEM
--
select d.FILE_TID                                           as DOC_TID,
       d.FILENAME                                           as DOC_NAME,
       TO_CHAR(d.CREATION_TIMESTAMP, 'YYYY-MM-DD HH24-MI')  AS DOC_CREATION_TIMESTAMP,
       ad.FILE_TID                                          as ANNOT_DOC_TID,
       ad.FILENAME                                          as ANNOT_DOC_NAME,
       TO_CHAR(ad.CREATION_TIMESTAMP, 'YYYY-MM-DD HH24-MI') AS ANNOT_DOC_CREATION_TIMESTAMP,
       af.FILE_TID                                          as ANNOT_FILE_TID,
       af.FILENAME                                          as ANNOT_FILE_NAME,
       TO_CHAR(af.CREATION_TIMESTAMP, 'YYYY-MM-DD HH24-MI') as ANNOT_FILE_CREATION_TIMESTAMP

from esp.T_FILE_REL r1
         left join esp.T_FILE d on d.FILE_TID = r1.FILE_TID
         left join esp.T_FILE ad on ad.FILE_TID = r1.TARGET_TID
         left join esp.T_FILE_REL r2 on r2.FILE_TID = d.FILE_TID and r2.REL_TID = -101
         left join esp.T_FILE af on af.FILE_TID = r2.TARGET_TID
where r1.REL_TID = -102
  and d.FILE_TID > 0
  and ad.FILE_TID > 0
  and af.FILE_TID > 0
  and ad.owner = af.owner
order by af.FILE_TID
desc
;
