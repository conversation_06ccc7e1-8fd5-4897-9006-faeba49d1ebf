--
-- KEY = m.userMessages
--

serviceId:esp.userMessages


--
-- KEY   = esp.userMessages
-- CACHE_MINUTES = 10
--

select value as MESSAGE, 'ESP' as APP  from ESP.T_APP_PROPERTIES
where NAME = 'userMessage.esp' and VALUE > ' '
  and 0 < (select count(*) from ESP.V_ROLE where ROLE_NAME like 'ESP%READER' or ROLE_NAME = 'ESP_ADMIN')
union
select value as MESSAGE, 'PCL' as APP  from ESP.T_APP_PROPERTIES
where NAME = 'userMessage.pcl' and VALUE > ' '
  and 0 < (select count(*) from ESP.V_ROLE where ROLE_NAME like 'PCL_%READER' or ROLE_NAME = 'PCL_ADMIN')
union
select value as MESSAGE, 'PCDB' as APP  from ESP.T_APP_PROPERTIES
where NAME = 'userMessage.pcdb' and VALUE > ' '
  and 0 < (select count(*) from ESP.V_ROLE where ROLE_NAME like 'BODREADER_PCRESDASH' or ROLE_NAME = 'ESP_ADMIN')
union
select value as MESSAGE, 'ALL' as APP  from ESP.T_APP_PROPERTIES
where NAME = 'userMessage.all' and VALUE > ' '
;

--
--
-- KEY         = m.getkeys.getCycleDefinition
-- select * from ESP.T_CYCLE_DEFINITION_USER 
--

set-if-empty:mVersion=noversion
;
select NAME, (KEYS_URL || '&mVersion=' || :mVersion) as KEYS_URL, WAIT_MSG, 
KEYS_LOG_URL, DOCU_LOG_URL, DELAY, INTERVAL, PRIORITY, USER_ID, SERVICES
from ESP.T_CYCLE_DEFINITION_USER
where
(
USER_ID = 
( 
select coalesce( max(USER_ID ), '_DEFAULT_') 
from ESP.T_CYCLE_DEFINITION_USER where USER_ID = :$USERID
)
) and server is null or (server not like '%cta%' and server not like '%fim%' and server not like '%pcl%')



--
-- KEY         = m.getkeys.getCycleDefinitionSplit
--

set-if-empty:mVersion=noversion
;
select NAME, (KEYS_URL || '&mVersion=' || :mVersion) as KEYS_URL, WAIT_MSG,
KEYS_LOG_URL, DOCU_LOG_URL, DELAY, INTERVAL, PRIORITY, USER_ID, SERVICES, SERVER, BACKEND_URL
from ESP.T_CYCLE_DEFINITION_USER
where
USER_ID = (SELECT COALESCE (MAX (USER_ID), '_DEFAULT_')
                             FROM ESP.T_CYCLE_DEFINITION_USER
                            WHERE USER_ID = :$USERID)
            AND (SERVER IS NULL)
            AND (SELECT COUNT (*)
                   FROM ESP.v_m_tabs
                  WHERE tab_code IN ('bod',
                                     'mp_gen')) > 0
         OR                                                         -- cockpit
           SERVER = 'fim'
            AND user_id = '_DEFAULT_'
            AND (SELECT COUNT (*)
                   FROM ESP.v_m_tabs
                  WHERE tab_code = 'co') > 0
         OR                                                     -- market view
           SERVER = 'fim'
            AND user_id = '_DEFAULT_'
            AND (SELECT COUNT (*)
                   FROM ESP.v_m_tabs
                  WHERE tab_code = 'mv') > 0
         OR                                                             -- pcl
           SERVER = 'pcl'
            AND user_id = '_DEFAULT_'
            AND ( (SELECT COUNT (*)
                     FROM ESP.v_m_tabs
                    WHERE tab_code = 'pcl') > 0)
         OR                                                      -- competitor
           SERVER = 'cta'
ORDER BY priority;



--
-- KEY    = ReportMailMA
--

java:com.swissre.esp.service.queryServices.ReportMailMA



--
-- KEY           = ReportMail
--

java:com.swissre.esp.service.queryServices.ReportMail



--
-- KEY    = ipad_client_properties
--

set-if-empty:mode=OldConnect
;
select substring(NAME from 17) as "NAME", VALUE from ESP.T_APP_PROPERTIES where NAME like 'ipad-'|| :mode ||  '%'
union
select substring(NAME from 6) as "NAME", VALUE from ESP.T_APP_PROPERTIES where NAME like 'ipad.%'
;


--
-- KEY    = m.visibleTabs
--

select * from ESP.V_M_TABS



