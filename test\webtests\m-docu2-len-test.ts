/* eslint-disable no-console,quotes */
import { test } from 'mocha';
import { localUrlJava, localUrlNode } from '../common';
import { runGetBlob, runGetkeys2 } from './getkeys2-utils';
import { toList } from '../../src/serviceQuery';
import { expect } from 'chai';

const localUrlNodeGetkeys2Sources = `${localUrlNode}serviceQuery/m.Getkeys2?sourcesOnly=true&name=sources`;

const docu2UrlNode = `${localUrlNode}mobile/docu2/`;
const docu2UrlJava = `${localUrlJava}mobile/docu2/`;

const testname = `(webtests) m-docu2-len-test`;
test(testname, async () => {
  console.log(`Start Node ${testname} get sources...: ${localUrlNodeGetkeys2Sources}!`);
  const getkeys2Json = toList(await runGetkeys2(localUrlNodeGetkeys2Sources, `${testname} Node`));

  const list = getkeys2Json.map(({ fileTid }) => fileTid);

  for (const fileTid of list) {
    const docu2UrlFileTidListJava = `${docu2UrlJava}?fileTidList=${fileTid}`;
    const docu2ResultJava = await runGetBlob(docu2UrlFileTidListJava, '');
    const lenJava = docu2ResultJava.length;

    console.debug(`len java: ${lenJava}`);
    expect(lenJava).gt(10, `Java failed for ${fileTid}!`);

    const docu2UrlFileTidListNode = `${docu2UrlNode}?fileTidList=${fileTid}`;
    const docu2ResultNode = await runGetBlob(docu2UrlFileTidListNode, '');
    const lenNode = docu2ResultNode.length;

    console.debug(`len node: ${lenNode}`);
    expect(lenNode).gt(10, `Node failed for ${fileTid}!`);

    expect(lenNode).equals(lenJava, `Length of ${fileTid} result is not the same!`);
  }

  //
  // All together...
  //

  const fileTidList = list.join(',');

  const docu2UrlFileTidListJava = `${docu2UrlJava}?fileTidList=${fileTidList}`;
  const docu2ResultJava = await runGetBlob(docu2UrlFileTidListJava, '');
  const lenJava = docu2ResultJava.length;

  console.debug(`len java: ${lenJava}`);
  expect(lenJava).gt(10, 'Java failed!');

  const docu2UrlFileTidListNode = `${docu2UrlNode}?fileTidList=${fileTidList}`;
  const docu2ResultNode = await runGetBlob(docu2UrlFileTidListNode, '');
  const lenNode = docu2ResultNode.length;

  console.debug(`len node: ${lenNode}`);
  expect(lenNode).gt(10, 'Node failed!');

  expect(lenNode).equals(lenJava, 'Length of result is not the same!');
});
