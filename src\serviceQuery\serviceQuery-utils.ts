import { isError, SqResult } from './serviceQuery-common';
import * as fs from 'fs';
import { processParameter } from './utils';
import { ServiceQuery } from './serviceQuery';
import LoggerCenter from '../logger/LoggerCenter';
import path from 'path';
import { SYSTEM } from '../service/constants';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export type GResult = { name: string; counter: number; serviceIds: string[] };
export type DirectoryResult = { name: string; nrOfFiles: number; serviceIds: string[] };
export type InitRepositoryResult = { directories: DirectoryResult[] };

export class ServiceQueryUtils {
  private sq: ServiceQuery;
  private saveServiceId: string;
  private ignoredErrors: string[] = [];

  constructor(sq: ServiceQuery, saveServiceId: string) {
    this.sq = sq;
    this.saveServiceId = saveServiceId;
  }

  public async initRepository(sqlDirectories: string[]): Promise<InitRepositoryResult> {
    const initRepositoryResult: InitRepositoryResult = { directories: [] };
    logger.info(`sqlDirectories: ${sqlDirectories.join(', ')}`);
  
    for (const sqlDirectory of sqlDirectories) {
      const directoryResult = this.initializeDirectoryResult(sqlDirectory);
      initRepositoryResult.directories.push(directoryResult);
  
      if (!this.isValidDirectory(sqlDirectory)) {
        logger.warn(`Directory ${sqlDirectory} does not exist!`);
        continue;
      }
  
      const sqlfileNames = fs.readdirSync(sqlDirectory);
      directoryResult.nrOfFiles = sqlfileNames.length;
  
      await this.processSqlFiles(sqlDirectory, sqlfileNames, directoryResult);
      await this.processSqSqlFiles(sqlDirectory, sqlfileNames, directoryResult);
    }
  
    return initRepositoryResult;
  }

  private initializeDirectoryResult(sqlDirectory: string): DirectoryResult {
    return { name: sqlDirectory, nrOfFiles: 0, serviceIds: [] };
  }
  
  private isValidDirectory(sqlDirectory: string): boolean {
    return fs.existsSync(sqlDirectory) && fs.lstatSync(sqlDirectory).isDirectory();
  }
  
  private async processSqlFiles(sqlDirectory: string, sqlfileNames: string[], directoryResult: DirectoryResult) {
    for (const filename of sqlfileNames) {
      try {
        if (filename.endsWith('.sql') && !filename.endsWith('.sq.sql')) {
          logger.info(`Start loading as SQL file: ${filename}`);
          const text = fs.readFileSync(path.join(sqlDirectory, filename), 'utf8');
          logger.debug(text);
          await this.processSqlText(text, filename);
        }
      } catch (err) {
        this.filteredError(err);
      }
    }
  }
  
  private async processSqSqlFiles(sqlDirectory: string, sqlfileNames: string[], directoryResult: DirectoryResult) {
    for (const filename of sqlfileNames) {
      try {
        if (filename.endsWith('.sq.sql')) {
          logger.info(`Start loading as SQ-SQL file: ${filename}`);
          const text = fs.readFileSync(path.join(sqlDirectory, filename), 'utf8');
          logger.debug(text);
          const result = await this.processSqSqlText(text, filename);
          result.serviceIds.forEach((serviceId) => directoryResult.serviceIds.push(serviceId));
          if (!result) {
            this.filteredError('Result is undefined! ');
          }
        }
      } catch (err) {
        this.filteredError(err);
      }
    }
  }

  public async initRepository2(sqlDirectories: string[]): Promise<InitRepositoryResult> {
    const initRepositoryResult: InitRepositoryResult = { directories: [] };
    logger.info(`sqlDirectories: ${sqlDirectories.join(', ')}`);
    for (const sqlDirectory of sqlDirectories) {
      const directoryResult: DirectoryResult = { name: sqlDirectory, nrOfFiles: 0, serviceIds: [] };
      initRepositoryResult.directories.push(directoryResult);
      if (!fs.existsSync(sqlDirectory) || !fs.lstatSync(sqlDirectory).isDirectory()) {
        logger.warn(`Directory ${sqlDirectory} does not exist!`);
        continue;
      }
      const sqlfileNames = fs.readdirSync(sqlDirectory);
      directoryResult.nrOfFiles = sqlfileNames.length;
      //
      // SQL
      //
      for (const filename of sqlfileNames) {
        try {
          if (filename.endsWith('.sql') && !filename.endsWith('.sq.sql')) {
            logger.info(`Start loading as SQL file: ${filename}`);
            const text = fs.readFileSync(sqlDirectory + '/' + filename, 'utf8');
            logger.debug(text);
            await this.processSqlText(text, filename);
          }
        } catch (err) {
          this.filteredError(err);
        }
      }

      //
      // sq.sql
      //
      for (const filename of sqlfileNames) {
        try {
          if (filename.endsWith('.sq.sql')) {
            logger.info(`Start loading as SQ-SQL file: ${filename}`);
            const text = fs.readFileSync(sqlDirectory + '/' + filename, 'utf8');
            logger.debug(text);
            const result = await this.processSqSqlText(text, filename);
            result.serviceIds.forEach((serviceId) => directoryResult.serviceIds.push(serviceId));
            if (!result) {
              this.filteredError('Result is undefined! ');
            }
          }
        } catch (err) {
          this.filteredError(err);
        }
      }
    }
    return initRepositoryResult;
  }

  //'delete from ESP.T_SERVICEQUERIES where KEY = :KEY;
  // insert into ESP.T_SERVICEQUERIES (KEY, QUERY, TAGS, ACCESS_GROUPS) values (:KEY, :QUERY, :TAGS, :ACCESS_GROUPS);
  // insert into ESP.T_SERVICEQUERIES_COUNTER select KEY, 0, null, null from ESP.T_SERVICEQUERIES where (KEY = :KEY ) and KEY not in (select i.KEY from ESP.T_SERVICEQUERIES_COUNTER i);
  // update ESP.T_SERVICEQUERIES_COUNTER set LAST_LOAD = :$CURRENTMILLIS where KEY = :KEY;serviceId:serviceQueries.updateCacheMinutes',
  async saveSqService(
    parameters: Record<string, string>,
    statements: string,
    source: string
  ): Promise<{
    result: SqResult;
    serviceId: string;
    roles: string;
  }> {
    // esp mapping
    parameters.SOURCE = source;
    parameters.KEY = (parameters['SERVICE_ID'] || parameters['KEY'] || '').toString().trim();
    parameters.ACCESS_GROUPS = parameters['ROLES'] || parameters['ACCESS_GROUPS'];
    parameters.QUERY = statements;
    if (parameters.KEY) {
      const result = await this.sq.run({
        serviceId: this.saveServiceId,
        userId: SYSTEM,
        roles: [SYSTEM],
        parameters
      });
      return { result, serviceId: parameters.KEY, roles: parameters.ACCESS_GROUPS };
    }
  }

  filteredError(_error: string | Error | unknown): void {
    let finalError = '';
    if (_error) {
      if (typeof _error === 'string') {
        finalError = _error;
      } else if (isError(_error)) {
        finalError = _error.message;
      }
      if (finalError && !this.isFilteredOut(this.ignoredErrors, finalError)) {
        logger.error(finalError);
      }
    }
  }

  isFilteredOut(ignoredErrors: string[], errorString: string): boolean {
    return (ignoredErrors || []).reduce((a: boolean, e: string) => errorString.includes(e) || a, false);
  }

  async processSqlText(statements: string, source: string): Promise<{ name: string; counter: number }> {
    const gResult = { name: source || 'processSqlText-process', counter: 0 };

    const lines = statements.split('\n');
    let sqlStatement = '';
    for (let i = 0; i < lines.length; i++) {
      const origLine = lines[i];
      const line = lines[i].trim();
      // comment
      if (line.startsWith('--') || !line) {
        continue;
      }
      // sqlStatement end
      if (line.endsWith(';')) {
        sqlStatement += line.substring(0, line.length - 1) + '\n';
        try {
          const [result] = await this.sq.processStatements([sqlStatement]);
          logger.info(`Result: rowsAffected: ${result.rowsAffected}`);
          gResult.counter++;
        } catch (e) {
          logger.error(source + ':' + i + ': ' + e);
        }
        sqlStatement = '';
        continue;
      }
      sqlStatement += origLine + '\n';
    }
    logger.info(source + ' : ' + gResult.counter + ' sql statements done.');
    return gResult;
  }

  async processSqSqlText(sqSqlText: string, source: string): Promise<GResult> {
    let parameters = {};
    let statements = '';
    const gResult: GResult = {
      name: source || 'processSqSqlText-process',
      counter: 0,
      serviceIds: []
    };
    try {
      const lines = sqSqlText.split('\n');
      let inComment = false;
      let inStatement = false;

      for (const line2 of lines) {
        const line = line2.trim();
        if (!line) {
          continue;
        }
        // comment
        if (line.startsWith('--')) {
          if (!inComment) {
            //
            // execute collected
            //
            if (inStatement) {
              const res = await this.saveSqService(parameters, statements, source);
              if (res) {
                const { result, serviceId, roles } = res;
                logger.info(
                  `saveSqService: rowsAffected: ${result.rowsAffected}, serviceId: ${serviceId}, roles: ${roles} `
                );
                gResult.serviceIds.push(serviceId);
                statements = '';
                parameters = {};
                inStatement = false;
                gResult.counter++;
              }
            }
          }
          inComment = true;
          processParameter(parameters, line.substring(2));
          continue;
        }
        inComment = false;
        inStatement = true;
        statements += line2 + '\n';
      }
      if (inStatement) {
        const res = await this.saveSqService(parameters, statements, source);
        if (res) {
          const { result, serviceId, roles } = res;
          logger.info(`saveSqService: rowsAffected: ${result.rowsAffected}, serviceId: ${serviceId}, roles: ${roles} `);
          gResult.serviceIds.push(serviceId);
          statements = '';
          parameters = {};
          inStatement = false;
          gResult.counter++;
        }
      }
    } catch (err) {
      if (isError(err)) {
        this.filteredError(err.message);
        if (err.stack) {
          logger.error(`processSqSqlText error message: ${err.message}\n, stack: ${err.stack}`);
        }
      }
    }
    logger.info(`${source} : ${gResult.counter} sq sql statements done.`);
    return gResult;
  }
}

export function legacyPreprocessor(s: string): string {
  let s2 = s.replace(/set:/g, 'set ');
  s2 = s2.replace(/set-if-empty:/g, 'set-if-empty ');
  s2 = s2.replace(/copy:/g, 'set ');
  s2 = s2.replace(/copy-if-empty:/g, 'set-if-empty ');
  s2 = s2.replace(/parameters:/g, 'parameters ');
  s2 = s2.replace(/parameters-if-empty:/g, 'parameters-if-empty ');
  s2 = s2.replace(/create-tid-for:/g, 'create-tid ');
  s2 = s2.replace(/create-tid-if-empty-for:/g, 'create-tid-if-empty ');
  s2 = s2.replace(/serviceId:/g, 'serviceId ');
  s2 = s2.replace(/include:/g, 'include ');
  s2 = s2.replace(/java:/g, 'node ');
  s2 = s2.replace(/node:/g, 'node ');
  s2 = s2.replace(/\{/g, '');
  s2 = s2.replace(/}/g, '');
  return s2;
}
