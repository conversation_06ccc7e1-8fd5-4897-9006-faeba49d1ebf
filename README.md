# ESP Node Backend

## System Requirements

- NodeJS 20.14.0
- PostgreSql
- 

## Breaking System Requirements

| Topic                | Description                                            | Status    |
|----------------------|--------------------------------------------------------|-----------|
| Rendering            | Render PDF to PNG using nodejs                         | 100% Done |
| Transport encryption | Compatible encryption for PDF / data transport to iPad | 100% Done |
| DBFile               | Save and retrieve files with DB encryption             | 100% Done |
| User Auth            | Autenticate user internally and externally             | 100% Done |
| File Upload          | Uploading files and saving them in T_FILE              | 100% Done |
| Downloading Files    | Reading files from T_FILE and service to request       | 100% done |
| Sending Email        | Sending Email with attachments                         | Done      |

## Migration

## Configuration (.env)

```
# Directive to run the server in local mode.
IS_LOCAL=true

# When in local mode, the LOCAL_USER_ID is taken for running all processes.
LOCAL_USER_ID=SRZMIY

# Port used by the server process.
SERVER_PORT=8080

# DB Credentials
DB_TRX_USER=esp_trx
DB_TRX_PASSWORD=...
DB_HOST=dcp0000076.dbaas.swissre.com
DB_PORT=5432
DB_DATABASE=ddp0005782
NODE_TLS_REJECT_UNAUTHORIZED=0

# Writing a start time into the app properties table.
LOG_STARTTIME_IN_APP_PROPERTIES=true

# All sql and sq files are in the sql service directory are read into the service table. Should be true on remote deployment.
LOAD_SQ_SQL_FILES_AT_STARTUP=false

# Set some logger names to INFO level. Most often the name of the logger is the filename.
LOG_LEVEL_INFO=serviceQuery.ts,PSQLDriver.ts

# Same for debug would be like:
#LOG_LEVEL_DEBUG=serviceQuery.ts,PSQLDriver.ts
```

## Urls

| Env                      | Url                              |
|--------------------------|----------------------------------|
| Dev with Okta and Nodejs | https://esp.apps-dev.swissre.com |
| Np with Okta and Nodejs  | https://esp.apps-np.swissre.com  |
|                          |                                  |
|                          |                                  |
|                          |                                  |
|                          |                                  |
|                          |                                  |
|                          |                                  |
|                          |                                  |


## PDF to Image Libraries

- "pdf-to-png-converter": "^3.2.0",
- "pdf-img-convert": "^1.2.1"

