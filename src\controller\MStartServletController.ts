import * as express from 'express';
import { Request, Response } from 'express';
import { isSqExceptionResult, singleValue, sqExceptionResult } from '../serviceQuery';
import ServiceCenter from '../service/ServiceCenter';
import { getUserProfile, toParameters } from './web-utils';
import { Controller } from '../types';
import LoggerCenter from '../logger/LoggerCenter';
import path from 'path';
import { processSessionMonitor } from '../service/lib/service-util';
import { track } from '../service/lib/esp/DBLogUtils';
import { hot_data, SYSTEM } from '../service/constants';
import AppPropertiesService from '../service/lib/AppPropertiesService';
import {
  getBuBasedServices,
  getUserBasedServices,
  getUserBasedServicesReportsOnly
} from '../service/lib/mobile/KeyProcessor2';
import { ajustDirectory } from '../service/lib/MUpdateService';
import { newMSession } from './MSession';
import pako from 'pako';

const logger = LoggerCenter.getLogger(path.basename(__filename));
const CONTROLLER_NAME = 'StartServlet2';

const INIT_SERVICES_TAG: Record<string, string> = {
  v_iesp_overview: 'overviewxml',
  v_iesp_exceptions: 'exceptionsxml',
  v_iesp_reports: 'reportsxml',
  v_page_service_rel: 'pageservicerelxml',
  v_iesp_prereadings_tree: 'prereadingstreexml',
  //
  v_iesp_prereadings_tree_documents: 'prereadingstreedocumentsxml',
  //
  v_iesp_prereadings_tree_meetings: 'prereadingstreemeetingsxml',
  //
  v_iesp_prereadings_tree_reports: 'prereadingstreereportsxml'
};

export default class MStartServletController implements Controller {
  public readonly name = CONTROLLER_NAME;
  public readonly paths = ['/api/mobile/start2'];
  public readonly router = express.Router();

  constructor() {
    this.router.post('/*', processHttpRequest);
    this.router.get('/*', processHttpRequest);
  }
}

async function processHttpRequest(req: Request, res: Response) {
  try {
    const sc = ServiceCenter.getInstance();
    const sq = sc.getSq();
    const userProfile = await getUserProfile(sq, req);
    if (!userProfile) {
      res.json(sqExceptionResult('Could not create user profile!'));
      return;
    }
    const { userId, roles } = userProfile;

    roles.push(SYSTEM);
    const userMessages: string[] = [];

    const parameters = toParameters(req, userId);
    const requestUri = req.path;
    logger.info(`Request Uri ${requestUri}`);

    processSessionMonitor(req.toString(), userId, 'MStartServlet');
    const { sessionKey } = newMSession(userId);
    //
    //
    //
    //const deviceId = parameters['deviceId'] || '-no device id-';
    const mVersion = parameters['iespVersion'] || 'noversion';
    const deviceType = parameters['deviceType'] || '-no device type-';
    const iosVersion = parameters['iosVersion'] || '-no ios version-';
    const compression = parameters['compression'];
    const mode = parameters['mode'];
    const outputMime = 'application/json';
    await track('I', `userId:${userId}`, 'MStartServlet');

    await sq.run({ serviceId: 'm.setMVersion', parameters: { mVersion }, userId: SYSTEM, roles: [SYSTEM] });
    logger.info(`${CONTROLLER_NAME}: mode=` + mode);

    //saveDeviceInfo(ap.getUserId(), mVersion, deviceType, iosVersion);
    const mRes = await sq.run({
      serviceId: 'm.start.saveDeviceInfo',
      parameters: {
        userId,
        $CURRENTMILLIS: Date.now(),
        deviceId: `${userId}-${deviceType}`,
        version: mVersion,
        deviceType,
        iosVersion
      },
      userId: SYSTEM,
      roles
    });
    if (mRes?.table?.length > 0) {
      mRes.table.forEach(([m]) => userMessages.push(m || '-no message-'));
    }

    //
    //
    //

    res.setHeader('Content-disposition', 'attachment; filename=MStartServlet.json');
    res.setHeader('Content-Type', outputMime);
    const data = await responseJson({ userId, roles, sessionKey, mode, userMessages, mVersion });
    if (compression === 'gzip') {
      const s = JSON.stringify(data);
      const compressedData = pako.gzip(s);
      res.set('Content-Type', 'application/octet-stream'); // Important header
      // variante code decompressing: res.send(compressedData);
      res.set('Content-Encoding', 'gzip'); // If applicable (e.g., pako compression)
      res.send(Buffer.from(compressedData));
    } else {
      res.json(data);
    }
  } catch (e) {
    logger.error(`${CONTROLLER_NAME} ${e}`);
    res.sendStatus(500);
  }
}

type PrepareDataArgs = {
  userId: string;
  roles: string[];
  sessionKey: string;
  mode: string;
  userMessages: string[];
  mVersion: string;
};

async function responseJson({ userId, roles, sessionKey, mode, userMessages, mVersion }: PrepareDataArgs) {
  const data = await prepareData({ userId, roles, sessionKey, mode, userMessages, mVersion });

  await addUserBasedServices(data, userId);
  await addBuBasedServices(data, userId);
  await addCollectionNames(data, userId);

  return data;
}

async function addUserBasedServices(data: Record<string, string>, userId: string) {
  const fileFormat = '.json';
  for (const initService of await getUserBasedServices()) {
    const tagName: string = INIT_SERVICES_TAG[initService];
    if (tagName) {
      data[tagName] = 'docu' + ajustDirectory(initService, userId) + initService + fileFormat;
    }
  }

  for (const initService of await getUserBasedServicesReportsOnly()) {
    const tagName = INIT_SERVICES_TAG[initService];
    if (tagName) {
      data[tagName] = 'docu' + ajustDirectory(initService, userId) + initService + fileFormat;
    }
  }
}

async function addBuBasedServices(data: Record<string, string>, userId: string) {
  const buBasedServices = await getBuBasedServices();
  const buId = 'NA';
  const fileFormat = '.json';
  for (const initService of buBasedServices) {
    const tagName = INIT_SERVICES_TAG[initService];
    if (tagName) {
      data[tagName + '-' + buId] = `docu${hot_data}${initService}-${buId}${fileFormat}`;
    }
  }
}

async function addCollectionNames(data: Record<string, string>, userId: string) {
  const collectionNames = ['IESP_TREE_MEETINGS', 'IESP_TREE_DOCUMENTS'];
  const property = await AppPropertiesService.getInstance().getAll();
  const fileFormat = '.json';
  for (const collectionName of collectionNames) {
    const initServices = (property[collectionName] || '')
      .split(',')
      .map((e) => e.trim())
      .filter((e) => !!e);
    let urls = '';
    for (const initService of initServices) {
      if (urls) {
        urls += ',';
      }
      const adjustedDirectory = 'docu/data/';
      urls += adjustedDirectory + initService + fileFormat;
    }
    data[collectionName.toLowerCase()] = urls;
  }
}

async function prepareData({ userId, sessionKey, mode, mVersion }: PrepareDataArgs) {
  const sc = ServiceCenter.getInstance();
  const sq = sc.getSq();
  const properties = await AppPropertiesService.getInstance().getAll();

  const data: Record<string, string> = {
    userid: userId,
    sessionid: sessionKey,
    keysxml: 'getkeys2',
    startfirst: 'DASHBOARD',
    annotation_upload_url: 'restJson/AnnotationUpload',
    annotation_delete_url: 'restJson/AnnotationDelete',
    report_mail_url: 'restJson/ReportMailMA',
    crashlog_url: 'restJson/Crashlog',
    monitorlog_url: 'restJson/Monitorlog',
    setreadflag_url: 'restJson/SetReadFlag',
    senddata_url: 'restJson/SendData',
    logging_url: 'restJson/m.log.insert',
    keepalive: 'keepalive',
    keepalivetime: properties['ipad_keepalive_time'] || '',
    offline: properties['ipad_offline'] || '',
    multitasking: properties['ipad_multitasking'] || '',
    base_url: properties['base_url'] || '',
    ipad_pub_version: properties['ipad_pub_version'],
    ipad_pub_version_min: properties['ipad_pub_version_min'],
    ipad_pub_url: properties['ipad_pub_url'],
    cr_base_url: 'docu/hot/index_res.html',
    change_password_url: 'http://web.swissre.com/webapp/adm/change',
    feedback_url: '<EMAIL>'
  };

  await addAnnotationExpirationDays(data, sq, userId);
  await addStartBuId(data, sc, userId);
  await addTabs(data, sc, userId);
  await addVisibleTabs(data, sc, userId);

  if (mode !== 'info') {
    await addCycleDefinition(data, sc, userId, mode, mVersion);
    await addCycleDefinitionSplit(data, sc, userId, mode, mVersion);
  }

  await addTestTabUrls(data, properties);
  await addUserMessages(data, sc, userId);

  return data;
}

async function addAnnotationExpirationDays(data: Record<string, string>, sq: any, userId: string) {
  const expRes = await sq.run({
    serviceId: 'userPref.annotationExpirationDays',
    parameters: { userId },
    userId: SYSTEM,
    roles: [SYSTEM]
  });
  data['annotation_expiration_days'] = singleValue(expRes) || '0';
  data['save_document_read_flag_off'] = 'FALSE';
}

async function addStartBuId(data: Record<string, string>, sc: any, userId: string) {
  const startRes = await sc.runSystemSq({
    serviceId: 'StartBuId',
    parameters: { $USERID: userId }
  });
  data['startBuId'] = singleValue(startRes) || 'NA';
}

async function addTabs(data: Record<string, string>, sc: any, userId: string) {
  const tabsRes = await sc.runSystemSq({
    serviceId: 'm.tabs',
    parameters: { $USERID: userId }
  });

  if (isSqExceptionResult(tabsRes)) {
    logger.error(`could not process 'm.tabs' : ${tabsRes.exception}`);
  } else {
    delete tabsRes['headerSql'];
    delete tabsRes['from'];
    data['tabs'] = JSON.stringify({
      name: 'tabs',
      username: userId.toLowerCase(),
      type: 'pagingResult',
      totalCount: tabsRes.table.length,
      size: tabsRes.table.length,
      ...tabsRes,
      dbFetchingTime: 0
    });
  }
}

async function addVisibleTabs(data: Record<string, string>, sc: any, userId: string) {
  const visibleTabsRes = await sc.runSystemSq({
    serviceId: 'm.visibleTabs',
    parameters: { $USERID: userId }
  });

  if (!visibleTabsRes || isSqExceptionResult(visibleTabsRes)) {
    logger.error(`could not process 'm.visibleTabs' : ${visibleTabsRes.exception || 'No data'}`);
  } else {
    delete visibleTabsRes['headerSql'];
    delete visibleTabsRes['from'];
    data['visibleTabs'] = JSON.stringify({
      name: 'm.visibleTabs',
      username: userId.toLowerCase(),
      type: 'pagingResult',
      totalCount: visibleTabsRes.table.length,
      size: visibleTabsRes.table.length,
      ...visibleTabsRes,
      dbFetchingTime: 0
    });
  }
}

async function addCycleDefinition(data: Record<string, string>, sc: any, userId: string, mode: string, mVersion: string) {
  const cycleRes = await sc.runSystemSq({
    serviceId: 'm.getkeys.getCycleDefinition',
    parameters: { $USERID: userId, mode, mVersion }
  });

  if (isSqExceptionResult(cycleRes)) {
    logger.error(`could not process 'm.getkeys.getCycleDefinition' : ${cycleRes.exception}`);
  } else {
    delete cycleRes['headerSql'];
    delete cycleRes['from'];
    data['cycleDefinition'] = JSON.stringify({
      name: 'CycleDefinition',
      username: userId.toLowerCase(),
      type: 'pagingResult',
      totalCount: cycleRes.table.length,
      size: cycleRes.table.length,
      ...cycleRes,
      dbFetchingTime: 0
    });
  }
}

async function addCycleDefinitionSplit(data: Record<string, string>, sc: any, userId: string, mode: string, mVersion: string) {
  const cycleSplitRes = await sc.runSystemSq({
    serviceId: 'm.getkeys.getCycleDefinitionSplit',
    parameters: { $USERID: userId, mode, mVersion }
  });

  if (isSqExceptionResult(cycleSplitRes)) {
    logger.error(`could not process 'm.getkeys.getCycleDefinitionSplit' : ${cycleSplitRes.exception}`);
  } else {
    delete cycleSplitRes['headerSql'];
    delete cycleSplitRes['from'];
    data['cycleDefinitionSplit'] = JSON.stringify({
      name: 'CycleDefinitionSplit',
      username: userId.toLowerCase(),
      type: 'pagingResult',
      totalCount: cycleSplitRes.table.length,
      size: cycleSplitRes.table.length,
      ...cycleSplitRes,
      dbFetchingTime: 0
    });
  }
}

async function addTestTabUrls(data: Record<string, string>, properties: any) {
  const testTabUrls = properties['testTabUrls'] || '';
  data['testTabUrls'] = testTabUrls;
}

async function addUserMessages(data: Record<string, string>, sc: any, userId: string) {
  const userMessagesRes = await sc.runSystemSq({
    serviceId: 'm.userMessages',
    parameters: { $USERID: userId }
  });

  if (isSqExceptionResult(userMessagesRes)) {
    logger.error(`could not process 'm.userMessages' : ${userMessagesRes.exception}`);
  } else {
    delete userMessagesRes['headerSql'];
    delete userMessagesRes['from'];
    data['userMessages'] = JSON.stringify({
      name: 'UserMessages',
      username: userId.toLowerCase(),
      type: 'pagingResult',
      totalCount: userMessagesRes.table.length,
      size: userMessagesRes.table.length,
      ...userMessagesRes,
      dbFetchingTime: 0
    });
  }
}

// Expected :docu/data/v_iesp_prereadings_tree_meetings_current.json,docu/data/v_iesp_prereadings_tree_meetings_past.json
// Actual   :v_iesp_prereadings_tree_meetings_current,v_iesp_prereadings_tree_meetings_past,docu/data/v_iesp_prereadings_tree_meetings_current.json,docu/data/v_iesp_prereadings_tree_meetings_past.json
