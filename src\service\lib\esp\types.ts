import { AttachmentData } from '@sendgrid/helpers/classes/attachment';

export type IDType = string | number;

export type DbFile = {
  fileTid: string;
  filename: string;
  directory: string;
  owner?: string;
  tags?: string;
  nrOfPages?: string;
  creationTimestamp?: string;
  length?: string;
  prio?: string;
  pathId?: string;
};

export type DBFileSelect = {
  fileTid?: string;
  filename?: string;
  directory?: string;
  owner?: string;
  createdIso?: string;
  deletionIso?: string;
  length?: string;
  tags?: string;
  realLength?: string;
};

export type MobDBFile = DbFile & {
  length?: string;
  prio?: string;
  supplFlag?: string;
  annotationFileTid?: string;
  readFlag?: string;
  numberOfPages?: string;
  visibility?: string;
};

export type MailUnSent = {
  mailTid: string;
  recipient: string;
  ccRecipient: string;
  subject: string;
  text: string;
  encryption: string;
  sent: string;
};

export type MailItem = {
  from: string;
  to: string[];
  cc: string;
  bcc: string;
  subject: string;
  body: string;
  attachment: AttachmentData[];
};

export type ReportMailParam = {
  fileTid: string;
  withAnnotation: boolean;
};

export type AttachedFile = DbFile & { fileRel: number };