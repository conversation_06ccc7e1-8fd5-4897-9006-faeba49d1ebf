import { legacyPreprocessor, ServiceQuery } from './serviceQuery';
import PSQLDriver, { PSQLDriverExtension } from './serviceQuery/PSQLDriver';
import { Response } from 'express';
import mime from 'mime';
// import { deviceDetect } from 'react-device-detect';
import DeviceDetector from 'node-device-detector';
import DeviceHelper from 'node-device-detector/helper';
import { createHash } from 'crypto';
import LoggerCenter from './logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export function createPsqlDriverFromEnv(): PSQLDriverExtension {
  const user = process.env.DB_TRX_USER;
  const password = process.env.DB_TRX_PASSWORD;
  const host = process.env.DB_HOST;
  const database = process.env.DB_DATABASE;
  const port = +(process.env.DB_PORT || '5432');
  const psqlDriver = new PSQLDriver({ user, password, host, database, port, ssl: true });

  // PSQL Driver

  psqlDriver.setServiceEntrySql(
    'select key as service_id, query as statements, access_groups as roles, tags from esp.t_servicequeries where key = :serviceId'
  );

  return psqlDriver;
}

export function createServiceQueryFromEnv(): ServiceQuery {
  const sq = new ServiceQuery(createPsqlDriverFromEnv());
  sq.setStatementsPreprocessor(legacyPreprocessor);
  return sq;
}

export function canonicalPath(directory2: string) {
  directory2 = !directory2 ? '' : directory2.trim();
  directory2 = '/' + directory2.replaceAll('\\', '/') + '/';
  while (directory2.includes('//')) {
    directory2 = directory2.replaceAll('//', '/');
  }
  return directory2;
}

export function sendFileBuffer(
  filename: string,
  content: Buffer,
  response: Response,
  option?: { contentType?: string; headers?: Record<string, string> }
) {
  response.contentType(option?.contentType || mime.getType(filename) || 'application/octet-stream');
  if (option?.headers) {
    Object.entries(option.headers).forEach(([key, value]) => response.setHeader(key, value));
  }
  response.send(content);
}

export function parseRequestDevice(userAgent: string | undefined): {
  BROWSER: string;
  DEVICE: string;
  IOSVERSION: string;
} {
  const deviceDetector = new DeviceDetector({
    clientIndexes: true,
    deviceIndexes: true,
    deviceAliasCode: false
  });
  const deviceInfo = deviceDetector.detect(userAgent);
  const isTablet = DeviceHelper.isTablet(deviceInfo);
  const isMobile = DeviceHelper.isMobile(deviceInfo);
  const isDesktop = DeviceHelper.isDesktop(deviceInfo);
  // const deviceInfo = deviceDetect(userAgent);
  const browser = '';
  const device = '';
  const iosVersion = '';

  if (isTablet) {
    // from pad , tablelet
    //   browser = _.join([deviceInfo.vendor, deviceInfo.model, deviceInfo.os], ' ');
    //   device = _.join([deviceInfo.model, deviceInfo.os, deviceInfo.osVersion], ' ');
    //   if (_.upperCase(deviceInfo.vendor) === 'APPLE' || _.upperCase(deviceInfo.model) === 'iPad') {
    //     iosVersion = _.join([deviceInfo.os, deviceInfo.osVersion], ' ');
    //   }
  } else if (isDesktop) {
    //   // from PC
    //   browser = _.join([deviceInfo.browserName, deviceInfo.browserMajorVersion], ' ');
    //   device = _.join([deviceInfo.osName, deviceInfo.osVersion], ' ');
  } else if (isMobile) {
    //   // from mobile phone
    //   browser = _.join([deviceInfo.vendor, deviceInfo.model, deviceInfo.os, deviceInfo.osVersion], ' ');
    //   device = _.join([deviceInfo.vendor, deviceInfo.model], ' ');
    //   if (_.upperCase(deviceInfo.vendor) === 'APPLE' || _.upperCase(deviceInfo.model) === 'iPhone') {
    //     iosVersion = _.join([deviceInfo.os, deviceInfo.osVersion], ' ');
    //   }
  }

  return {
    BROWSER: browser,
    DEVICE: device,
    IOSVERSION: iosVersion
  };
}

export const winFoldername80 = (folder: string) => {
  let digest = 'ABCD';
  try {
    const hash = createHash('sha256');
    hash.update(`${folder}ABCD`);
    digest = hash.digest('hex');
  } catch (e) {
    logger.error(e);
  }
  folder = asDirname(folder);
  if (folder.length > 80) {
    folder = folder.substring(0, 80 - 5) + '_' + digest;
  }
  return folder;
};

const cs = '><:\\?*';

function asDirname(folder: string) {
  let fn = '';
  for (const s of folder) {
    if (isWhitespace(s) || cs.includes(s)) {
      fn = `${fn}_`;
      continue;
    }
    fn = `${fn}${s}`;
  }
  return fn;
}

function isWhitespace(character: string) {
  return /\s/.test(character);
}

export const fixEscapeCharForJava = (s: string) =>
  s.replaceAll("'", '\\u0027').replaceAll('=', '\\u003d').replaceAll('&', '\\u0026');

export const abbreviateString = (str: string, maxLength: number) =>
  str.length <= maxLength ? str : str.slice(0, maxLength - 3) + '...';
