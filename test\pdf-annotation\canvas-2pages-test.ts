import { PDFDocument, PDFPage } from 'pdf-lib';
import { test } from 'mocha';
import { readFileSync, writeFileSync } from 'fs';
import { outdataDir, testdataDir } from '../common';
import path from 'path';
import { createCanvas } from 'canvas';

const origPdf = 'gametheory-orig.pdf';
const outpath = path.join(outdataDir, 'gametheory-canvas-2pages.pdf');

test('pdf-comment-test', async () => {
  await addComment();
});

async function addComment() {
  const existingPdfBytes = readFileSync(path.join(testdataDir, origPdf));

  const pdfDoc = await PDFDocument.load(existingPdfBytes);
  // const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);

  const pages = pdfDoc.getPages();

  await drawOnPage(pdfDoc, pages[0], 100, 100);
  await drawOnPage(pdfDoc, pages[1], 300, 600);

  const pdfBytes = await pdfDoc.save();
  writeFileSync(outpath, pdfBytes);
}

async function drawOnPage(pdfDoc: PDFDocument, pdfPage: PDFPage, x: number, y: number) {
  const { width, height } = pdfPage.getSize();
  const canvas = createCanvas(width, height);

  const ctx = canvas.getContext('2d');

  ctx.beginPath();
  ctx.lineWidth = 20;
  ctx.strokeStyle = '#aa000066';
  ctx.moveTo(20, height - 20);
  ctx.lineTo(x, height - y);
  ctx.stroke();
  const buff = canvas.toBuffer('image/png');
  const pdfImage = await pdfDoc.embedPng(buff);
  pdfPage.drawImage(pdfImage);
  pdfImage.embed();
}
