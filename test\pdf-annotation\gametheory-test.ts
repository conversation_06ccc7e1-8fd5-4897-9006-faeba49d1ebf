/* eslint-disable no-console */
import { test } from 'mocha';
import { readFileSync, writeFileSync } from 'fs';
import path from 'path';
import { outdataDir, testdataDir } from '../common';
import { pdfAnnotationHandler } from '../../src/service/pdf-annotation/pdf-annotation-handler';

const origPdf = 'gametheory-orig.pdf';
const annot = 'gametheory-annot.json';
const outPdf = path.join(outdataDir, 'gametheory-annot-node.pdf');

test(`annot ${annot} canvas`, async () => {
  console.log(`processing ${origPdf} with ${annot}`);
  const pdfBytesIn = readFileSync(path.join(testdataDir, origPdf));
  const jsonIn = readFileSync(path.join(testdataDir, annot));
  const pdfBytes = await pdfAnnotationHandler(pdfBytesIn, jsonIn);
  writeFileSync(outPdf, pdfBytes);
});
