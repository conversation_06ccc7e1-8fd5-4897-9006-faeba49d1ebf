import { SqNodeFunction, SqResult } from '../../serviceQuery';
import ServiceCenter from '../ServiceCenter';
import LoggerCenter, { isWinstonLogLevel, WinstonLogLevel } from '../../logger/LoggerCenter';
import AppPropertiesService from './AppPropertiesService';
import { JavaLogManager_filter, JavaLogManager_level } from '../constants';
import { stringValue } from '../service-utils';

export const LoggerCenterManager: SqNodeFunction = async({ request: { parameters } }): Promise<SqResult> => {
  const action = stringValue(parameters, 'action', 'info');
  const filter = stringValue(parameters, 'filter');
  const level = stringValue(parameters, 'level');

  switch (action) {
    case 'info':
      return processInfo();
    case 'setLevel':
      return processSetLevel(level as WinstonLogLevel, filter);
    case 'updateByAppProperties': {
      return processUpdateByAppProperties();
    }
  }
  return { exception: `Unexpected action ${action}!` };
};

const processInfo = (): SqResult => {
  const loggerByName = LoggerCenter.getLoggerByName();
  return {
    header: ['message'],
    table: Object.keys(loggerByName).map((key) => {
      const logger = loggerByName[key];
      return [`${logger.level} : ${key}`];
    })
  };
};

const processSetLevel = async(level: WinstonLogLevel, filter: string): Promise<SqResult> => {
  const messages: string[][] = [];
  if (isWinstonLogLevel(level)) {
    if (ServiceCenter.getInstance().isLocal()) {
      LoggerCenter.setLevelToAll(level, filter);
      messages.push([`Level ${level} with filter ${filter} applied locally.`]);
    } else {
      const as = AppPropertiesService.getInstance();
      await as.saveProperty(JavaLogManager_level, level);
      await as.saveProperty(JavaLogManager_filter, filter);
      messages.push([`Level ${level} with filter ${filter} applied saved. Will be activated in some minutes...`]);
    }
  }
  return {
    header: ['message'],
    table: messages
  };
};

const processUpdateByAppProperties = async() => {
  const messages: string[][] = [];

  const aps = AppPropertiesService.getInstance();
  const level = await aps.get(JavaLogManager_level, 'warn');
  const filter = await aps.get(JavaLogManager_filter, '');
  if (isWinstonLogLevel(level)) {
    LoggerCenter.setLevelToAll(level, filter);
    messages.push([`Level <${level}> applied!`]);
    messages.push([`Filter <${filter}> applied!`]);
  } else {
    return { excpetion: `Unknown WinstonLogLevel ${level}. (filter: ${filter})` };
  }
  return {
    header: ['message'],
    table: messages
  };
};

// export const SetLoggerLevel: SqNodeFunction = async ({ request }): Promise<SqResult> => {
//   const level = request.parameters.level.toString();
//   const filter = request.parameters.filter.toString();
//   const messages: string[][] = [];
//   if (isWinstonLogLevel(level)) {
//     if (ServiceCenter.getInstance().isLocal()) {
//       LoggerCenter.setLevelToAll(level, filter);
//       messages.push([`Level ${level} with filter ${filter} applied locally.`]);
//     } else {
//       const as = AppPropertiesService.getInstance();
//       await as.saveProperty(JavaLogManager_level, level);
//       await as.saveProperty(JavaLogManager_filter, filter);
//       messages.push([`Level ${level} with filter ${filter} applied saved. Will be activated in some minutes...`]);
//     }
//   }
//   return {
//     header: ['message'],
//     table: messages
//   };
// };
