import ServiceCenter from '../ServiceCenter';
import { SYSTEM } from '../constants';

type Task = {
    fileTid?: string;
    userId?: string;
    meetingItemTid?: string;
    type?: string;
    status?: string;
    progress?: number;
    msg?: string;
};

export const addTask = async (task : Task) => {
    const sq = ServiceCenter.getInstance().getSq();
    return sq.run({
      serviceId: 'task.add',
      parameters: { ...task },
      userId: SYSTEM,
      roles: [SYSTEM]
    });
};

export const updateTaskProgress = (task : Task) => {
    const sq = ServiceCenter.getInstance().getSq();
    return sq.run({
      serviceId: 'task.update',
      parameters: { ...task },
      userId: SYSTEM,
      roles: [SYSTEM]
    });
};

export const checkTasks = ($USERID: string) => {
    const sq = ServiceCenter.getInstance().getSq();
    return sq.run({
      serviceId: 'task.check',
      parameters: { $USERID },
      userId: SYSTEM,
      roles: [SYSTEM]
    });
};