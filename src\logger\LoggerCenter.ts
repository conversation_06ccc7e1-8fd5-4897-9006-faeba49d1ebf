import winston, { createLogger, format, transports } from 'winston';
// import { SqFunctionArg, SqNodeFunction, SqResult } from '../serviceQuery';
import * as dotenv from 'dotenv';

export enum WinstonLogLevel {
  Debug = 'debug',
  Info = 'info',
  Warn = 'warn',
  Error = 'error'
}

export const isWinstonLogLevel = (level: string): level is WinstonLogLevel =>
  level === WinstonLogLevel.Debug ||
  level === WinstonLogLevel.Info ||
  level === WinstonLogLevel.Warn ||
  level === WinstonLogLevel.Error;

dotenv.config();

export default class LoggerCenter {
  public static defaultLevel = WinstonLogLevel.Warn;
  private static readonly loggerByName: Record<string, winston.Logger> = {};

  public static getLoggerByName() {
    return LoggerCenter.loggerByName;
  }

  static getLogger(name: string) {
    if (!LoggerCenter.loggerByName[name]) {
      LoggerCenter.loggerByName[name] = createNewLogger(LoggerCenter.defaultLevel);
      if ((process.env.LOG_LEVEL_INFO || '').includes(name)) {
        LoggerCenter.loggerByName[name].level = WinstonLogLevel.Info;
      }
      if ((process.env.LOG_LEVEL_DEBUG || '').includes(name)) {
        LoggerCenter.loggerByName[name].level = WinstonLogLevel.Debug;
      }
    }
    return LoggerCenter.loggerByName[name];
  }

  static setLevel(name: string, level: WinstonLogLevel) {
    const logger = LoggerCenter.loggerByName[name];
    if (logger) {
      logger.level = level;
    }
  }

  static setLevelToAll(level: WinstonLogLevel, filter: string) {
    const names = Object.keys(LoggerCenter.loggerByName);
    names.forEach((key) => {
      if (key.includes(filter)) {
        const logger = LoggerCenter.loggerByName[key];
        logger.level = level;
      }
    });
  }
}

function createNewLogger(level: WinstonLogLevel): winston.Logger {
  const wLogger = createLogger({
    level,
    format: format.combine(
      format.timestamp(),
      format.json()
    ),
    // format: format.combine(
    //   format.timestamp({
    //     format: 'YYYY-MM-DD HH:mm:ss'
    //   }),
    //   format.errors({ stack: true }),
    //   format.splat(),
    //   format.json()
    // ),
    // defaultMeta: { service: 'your-service-name' },
    // transports: [
    //   //
    //   // - Write to all logs with level `info` and below to `quick-start-combined.log`.
    //   // - Write all logs error (and below) to `quick-start-error.log`.
    //   //
    //   new transports.File({ filename: 'quick-start-error.log', level: 'error' }),
    //   new transports.File({ filename: 'quick-start-combined.log' })
    // ]
  });

  if (process.env.NODE_ENV !== 'production') {
    wLogger.add(
      new transports.Console({
        format: format.combine(format.timestamp(), format.colorize(), format.simple())
      })
    );
  }

  return wLogger;
}
