type REntry = Record<string, string>;
export const requestAttributes: Record<string, REntry> = {};

export const getRequestAttribute = (userId: string, name: string) =>
  requestAttributes[userId] ? requestAttributes[userId][name] : undefined;
export const setRequestAttribute = (userId: string, name: string, value: string) =>
  requestAttributes[userId]
    ? (requestAttributes[userId][name] = value)
    : (requestAttributes[userId] = { [name]: value });
