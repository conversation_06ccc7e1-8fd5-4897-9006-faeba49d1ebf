--
--
-- USING MAIL SPOOLER
--
--



--
-- KEY           = mails.lock
-- ACCESS_GROUPS = SYSTEM
--

update ESP.T_MAIL 
set LOCK_ID = :lockId
where LOCK_ID is null



--
-- KEY           = mails.selectByLock
-- ACCESS_GROUPS = SYSTEM
--

select * from ESP.T_MAIL where LOCK_ID = :lockId and SENT is null



--
-- KEY           = mails.selectAttachments
-- ACCESS_GROUPS = SYSTEM
--

select 
f.FILE_TID, f.FILENAME 
from ESP.T_FILE f 
left join ESP.T_MAIL_ATTACHMENT a on a.FILE_TID = f.FILE_TID 
where
f.FILE_TID > -1
and
a.MAIL_TID = :mailTid



--
-- KEY           = mails.sent
-- ACCESS_GROUPS = SYSTEM
--

update ESP.T_MAIL
set sent = CURRENT_TIMESTAMP
where
MAIL_TID = :mailTid
;



--
-- KEY = mails.resolveUser
--

set-if-empty:userId=nobody
;
select coalesce(email_address, :userId) as EMAIL from ESP.V_SR_USER where  USER_ID = :userId
;


--
-- KEY           = mails.select
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

set:$MAXROWS=100000
;
set:$MAXFETCHROWS=100050
;
set-if-empty:subject=%
;
set-if-empty:recipient=%
;
set-if-empty:ccRecipient=%
;
select
    MAIL_TID,
    RECIPIENT,
    CC_RECIPIENT,
    SUBJECT,
    SENT,
    INPUT_PARAMETER,
    RECORD_CREATED,
    TYPE
from ESP.T_MAIL
where
    (SUBJECT like :subject or SUBJECT is null)
  and
    (RECIPIENT like :recipient or RECIPIENT is null)
  and
    (CC_RECIPIENT like :ccRecipient or CC_RECIPIENT is null)

order by
    SENT desc

--
-- KEY           = mails.get
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

select MAIL_TID, RECIPIENT, CC_RECIPIENT, SUBJECT, TEXT, SENT, RECORD_CREATED, LOCK_ID
from ESP.T_MAIL where MAIL_TID = :mailTid
;


--
--
-- CONVENIENCE MAIL SERVICES
--
--



--
-- KEY           = mails.add
-- ACCESS_GROUPS = SYSTEM,ESP_ADMIN
--

create-tid-for:mailTid
;
insert into ESP.T_MAIL
(MAIL_TID, RECIPIENT, SUBJECT, TEXT)
values
(:mailTid, :recipient, :subject, :text)
;


