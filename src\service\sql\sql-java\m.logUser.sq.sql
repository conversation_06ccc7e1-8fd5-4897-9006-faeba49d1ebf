--
-- KEY           = m.logUser.select
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
--

set:$MAXROWS=500000
;
set:$MAXFETCHROWS=500500
;
set-if-empty:device=%
;
set-if-empty:activity=%
;
set-if-empty:firstName=%
;
set-if-empty:lastName=%
;
set-if-empty:userId=%
;
set-if-empty:dateTime=%
;
select
    USER_ID,
    FIRST_NAME,
    LAST_NAME,
    DEVICE,
    ACTIVITY,
    DATE_TIME
from ESP.V_USER_ACTIVITY
where 
((DEVICE like :device and DEVICE is not null) or (:device = '%' and DEVICE is null))
and
((ACTIVITY like :activity and ACTIVITY is not null) or (:activity = '%' and ACTIVITY is null))
and
((FIRST_NAME like :firstName and FIRST_NAME is not null) or (:firstName = '%' and FIRST_NAME is null))
and
((LAST_NAME like :lastName and LAST_NAME is not null) or (:lastName = '%' and LAST_NAME is null))
and
((DATE_TIME like :dateTime and DATE_TIME is not null) or (:dateTime = '%' and DATE_TIME is null))
and
(USER_ID like :userId or USER_ID is null)
;

