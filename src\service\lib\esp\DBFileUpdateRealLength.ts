import { isSqExceptionResult, singleValue, SqNodeFunction, SqResult, toList } from '../../../serviceQuery';
import ServiceCenter from '../../ServiceCenter';
import { DbFile } from './types';

export const DBFileUpdateRealLength: SqNodeFunction = async(): Promise<SqResult> => {
  const sc = ServiceCenter.getInstance();

  const r = await sc.runSystemSq({ serviceId: 'dbFile.selectMissingRealLength' });
  if (isSqExceptionResult(r)) {
    return r;
  }
  const files = toList<DbFile>(r);
  const header = ['message', 'content', 'origin'];
  const table: string[][] = [];
  table.push(['Nr of files to update', files.length.toString(), '']);
  for (const { fileTid, filename, directory } of files) {
    const updateRes = await sc.runSystemSq({ serviceId: 'dbFile.updateRealLength.dbside', parameters: { fileTid } });
    if (isSqExceptionResult(updateRes)) {
      table.push([`Updated failed for: ${filename}`, updateRes.exception, directory]);
      continue;
    }
    if (updateRes.rowsAffected === 0) {
      table.push([`Could no update: ${filename}`, 'No rows affected', directory]);
      continue;
    }
    const newRes = await sc.runSystemSq({ serviceId: 'dbFile.getRealLength', parameters: { fileTid } });
    const newLength = singleValue(newRes);
    if (newLength) {
      table.push([`Updated successful for: ${filename}`, singleValue(newRes), directory]);
    } else {
      table.push([`Length still empty for: ${filename}!`, singleValue(newRes), directory]);
    }
  }
  return { name: 'DBFileUpdateRealLength', header, table };
};
