import { test } from 'mocha';
import { expect } from 'chai';
import * as dotenv from 'dotenv';
import { localUrlNode } from '../common';
import axios from 'axios';

dotenv.config();

test(`(webtests) health-check-test`, async () => {
  const url = `${localUrlNode}api/v1/health`;

  const headers = {
    'Content-Type': 'multipart/form-data'
  };

  try {
    const response = await axios.get(url, { headers });
    console.log('Health check access successful:', response.data);
    expect(response.data.toString().trim()).equals('Ok');
  } catch (error) {
    console.error(error);
    expect.fail(`Error received from Health check ${error}`);
  }
});
