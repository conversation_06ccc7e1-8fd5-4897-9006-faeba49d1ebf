/* eslint-disable no-console,space-before-function-paren */
// called from admin
// serviceId: 'm.Getkeys2',
//     parameters: {
//   name: 'sources',
//       // sources only!
//       sourcesOnly: true,
//       uriVersion: '2'
// },
// ==> java:com.swissre.serviceMobile.lib.Getkeys2

import { localUrlJava, localUrlNode } from '../common';
import { runGetkeys2 } from './getkeys2-utils';
import { SqResult, toList } from '../../src/serviceQuery';
import { expect } from 'chai';
import { compareJson, CompareJsonOptions } from '../test-utils';

const sqPart = 'mobile/getkeys2';

const localUrlJavaStart2 = `${localUrlJava}${sqPart}`;
const localUrlNodeStart2 = `${localUrlNode}${sqPart}`;
const names = {
  cr: 'cr',
  tree: 'tree',
  current: 'current',
  general: 'general',
  archived1: 'archived1',
  archived2: 'archived2'
};

const runDataTest = async (jsonDataService: string) => {
  console.log(`Start Node ${jsonDataService} url: ${localUrlNodeStart2}!`);
  const resultNode = await runGetkeys2(`${localUrlNodeStart2}?name=${jsonDataService}`, 'Node');
  const listNode = toList(resultNode);

  console.log(`node: header        : ${JSON.stringify(resultNode.header)}`);
  console.log(`node: nr of records : ${resultNode.table?.length}`);
  console.log(`node: nr of keys    : ${Object.keys(resultNode).length}`);

  console.log(`Start Java ${jsonDataService} url: ${localUrlJavaStart2}!`);

  const resultJava = (await runGetkeys2(`${localUrlJavaStart2}?name=${jsonDataService}`, 'Java')) as SqResult;
  const listJava = toList(resultJava);

  console.log(`java: header        : ${JSON.stringify(resultJava.header)}`);
  console.log(`java: nr of records : ${resultJava.table?.length}`);
  console.log(`java: nr of keys    : ${Object.keys(resultJava).length}`);
  expect(listNode.length).equals(listJava.length, 'The number of files are different!');
  const compareJsonOptions: CompareJsonOptions = {
    ignore: ['fileTid', 'updateTime', 'length', 'pathId'],
    isJsonString: []
  };
  for (let i = 0; i < listJava.length; i++) {
    const java1 = listJava[1];
    const node1 = listNode[1];
    console.log(`checking : ${java1.path}`);
    compareJson(node1, java1, compareJsonOptions);
  }
};

describe('Test All (Json) Data Services', () => {
  for (const name of Object.values(names)) {
    it(`(webtests) m.Getkeys2  ${name}`, async () => {
      console.log(`Start test for ${name} data.`);
      await runDataTest(name);
    });
  }
});
