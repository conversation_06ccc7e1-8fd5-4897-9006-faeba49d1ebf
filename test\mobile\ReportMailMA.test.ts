import { readFileSync } from 'fs';
import { send } from '../../src/service/lib/EMailService';
import { Compile, parse } from 'velocityjs';

const fromEmailAddress = process.env.FROM_EMAIL_ADDRESS;

async function getHtmlTemplateWithVariables(templatePath: string, variables: {[key: string]: any}): Promise<string> {
  let template = readFileSync(templatePath, 'utf-8');
  var asts = parse(template);
  const content = new Compile(asts).render(variables);
  return content;
}

test('should correctly send template email by SendGrid', async () => {
  
  let html = await getHtmlTemplateWithVariables('test/templates/DocumentAnnotationMailTemplate.html', {
    userName: 'value1',
    mutlipleFiles: false,
    comment: 'comment value',
});
  const sent = await send(['<EMAIL>'], fromEmailAddress, 'subject', null, [], html);
  console.info(sent);
  expect(sent).toBe('SUCCESS');
});
