--
-- KEY           = esp.userDetailsTt.select
-- ACCESS_GROUPS = ESP_ADMIN
--


set-if-empty:relObj=%
;
set-if-empty:userId=%
;
set-if-empty:relType=%
;
select 
 USER_ID, 
 REL_TYPE, 
 REL_OBJ, 
 UPDATE_DATE, 
 UPDATE_USER_ID  
from ESP.T_USER_DETAILS
where 
(upper(REL_OBJ) like upper(:relObj) or REL_OBJ is null)
and
(upper(USER_ID) like upper(:userId) or USER_ID is null)
and
(upper(REL_TYPE) like upper(:relType) or REL_TYPE is null)
order by USER_ID













--
-- KEY           = esp.userDetailsTt.insert
-- ACCESS_GROUPS = ESP_ADMIN
--





insert into ESP.T_USER_DETAILS
( 
 USER_ID, 
 REL_TYPE, 
 REL_OBJ, 
 UPDATE_DATE, 
 UPDATE_USER_ID)
values
(
:userId, 
  :relType, 
  :relObj, 
  current_timestamp, 
  :$USERID)
  






--
-- KEY           = esp.userDetailsTt.delete
-- ACCESS_GROUPS = ESP_ADMIN
--













delete from ESP.T_USER_DETAILS
where 
REL_OBJ = :relObj
and
USER_ID = :userId
and
REL_TYPE = :relType
  



--
-- KEY           = esp.userDetailsTt.get
-- ACCESS_GROUPS = ESP_ADMIN
--




select 
 USER_ID, 
 REL_TYPE, 
 REL_OBJ, 
 UPDATE_DATE, 
 UPDATE_USER_ID  
from ESP.T_USER_DETAILS
where 
REL_OBJ = :relObj
and
USER_ID = :userId
and
REL_TYPE = :relType

















-- END
