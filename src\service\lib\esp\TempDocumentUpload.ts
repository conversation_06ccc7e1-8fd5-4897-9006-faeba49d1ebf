import { SqContext, SqRequest, SqResult } from '../../../serviceQuery';
import Logger<PERSON>enter from '../../../logger/LoggerCenter';
import path from 'path';
import { getFiles } from './DBFileHelper';
import { getDbFile } from '../upload-file-utils';
import { _temp_ } from '../../constants';
import { moveFile } from '../DBFileService';
import { processPdfDerivedFiles } from './PdfUtilities';
import { updateTaskProgress } from '../task-utils';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export async function TempDocumentUpload({ request }: { request: SqRequest; context: SqContext }): Promise<SqResult> {
  const header: string[] = ['fileTid', 'filename', 'created', 'owner', 'history', 'pages'];
  const table: string[][] = [];

  const fileTids: string[] = getFiles(request);

  if (fileTids.length === 0) {
    logger.error('No upload file provided!');
  }

  for (const fileTid of fileTids) {
    if (!fileTid) {
      logger.error('Empty fileTid - serious error!');
    } else {
      await moveFile(fileTid, _temp_);
      await updateTaskProgress({ fileTid, type: 'fileUpload', progress: 20, msg: 'Generating Preview', status: 'progressing' });
      const dbFile = await getDbFile(fileTid);
      if (dbFile) {
        await updateTaskProgress({ fileTid, type: 'fileUpload', progress: 70, msg: 'Processing', status: 'progressing' });
        await processPdfDerivedFiles(dbFile);
        table.push([fileTid, dbFile.filename, dbFile.creationTimestamp, dbFile.owner, 'nohist', '1']);
        await updateTaskProgress({ fileTid, type: 'fileUpload', progress: 100, msg: 'Finish Upload', status: 'progressing' });
      } else {
        logger.error(`File not found for ${fileTid} - serious error!`);
      }
    }
  }

  return { header, table };
}
