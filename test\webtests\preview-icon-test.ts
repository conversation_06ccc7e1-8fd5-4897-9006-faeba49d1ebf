// http://localhost:8081/api/docremote/icon.png?opid=getIconFor&subid=141662473

import { test } from 'mocha';
import { expect } from 'chai';
import * as dotenv from 'dotenv';
import { localUrlNode } from '../common';
import { runGetBlob } from './getkeys2-utils';

const fileTid = 141662473;
dotenv.config();
const testname = `(webtests) preview-icon-test`;
test(testname, async () => {
  const url = `${localUrlNode}api/docremote/icon.png?opid=getIconFor&subid=${fileTid}`;

  try {
    const res = await runGetBlob(url, testname);
    console.log(`docremote/icon.png?opid=getIconFor&subid=${fileTid} Done`);

    expect(res.length).gt(1000, `${testname} failed`);
  } catch (error) {
    console.error(error);
    expect.fail(`Error received from Health check ${error}`);
  }
});
