import { PDFPage } from 'pdf-lib';
import { DrawOperation } from '../types';
import { Canvas } from 'canvas';
import { getColor } from './draw-utils-canvas';
import { getPoints, getWidth } from '../draw-utils';

const WIDTH_PENCIL = 8.0;

export function drawPencilCanvas(page: PDFPage, object: DrawOperation, canvas: Canvas) {
  const { height } = page.getSize();
  const points = getPoints(object.points);
  const ctx = canvas.getContext('2d');

  const scale = getWidth(object);
  const color = getColor(object);

  // TODO BasicStroke(WIDTH_PENCIL * scale, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND)

  points.forEach(([x, y], i) => {
    if (i === 0) {
      ctx.beginPath();
      ctx.lineWidth = WIDTH_PENCIL * scale;
      ctx.strokeStyle = color;
      ctx.moveTo(x, height - y);
    } else {
      ctx.lineTo(x, height - y);
    }
  });
  ctx.stroke();
}
