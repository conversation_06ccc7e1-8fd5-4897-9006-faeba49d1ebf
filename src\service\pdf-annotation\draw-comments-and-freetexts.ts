import { PDFContext, PDFDocument, PDFName, PDFPage, PDFRef, PDFString } from 'pdf-lib';
import { DrawOperation, InstrumentType } from './types';
import { getPoint } from './draw-utils';
import { getColorNumbers } from './canvas/draw-utils-canvas';

export async function drawCommentsAndFreetexts(pdfDoc: PDFDocument, page: PDFPage, objects: DrawOperation[]) {
  if (!objects || objects.length === 0) {
    return;
  }
  const { height } = page.getSize();
  const context = pdfDoc.context;
  const annotatesRefs: PDFRef[] = [];
  for (const object of objects) {
    const isFreeText = object.objectMeta.instrument === InstrumentType.FREE_TEXT;
    const text = object.objectMeta.text;
    if (text) {
      if (isFreeText) {
        annotatesRefs.push(createFreeTextRef(object, context, height, text));
      } else {
        annotatesRefs.push(createTextRef(object, context, height, text));
      }
    }
  }
  if (annotatesRefs.length > 0) {
    page.node.set(PDFName.of('Annots'), pdfDoc.context.obj(annotatesRefs));
  }
}

export function createTextRef(object: DrawOperation, context: PDFContext, height: number, text: string): PDFRef {
  const [x, y] = getPoint(object.points, 0);

  const annots = context.obj({
    Type: 'Annot',
    Subtype: 'Text',
    Rect: [x, height - y, x + 20, height - y + 20],
    flags: 1,
    C: [1, 1, 0],
    Contents: PDFString.of(text)
  });
  return context.register(annots);
}

export function createFreeTextRef(object: DrawOperation, context: PDFContext, height: number, text: string): PDFRef {
  const s = getPoint(object.points, 0);
  const e = getPoint(object.points, 1);

  const { r, g, b } = getColorNumbers(object);

  const annots = context.obj({
    Type: 'Annot',
    Subtype: 'FreeText',
    Rect: [s[0], height - s[1], e[0], height - e[1]],
    flags: 1,
    DA: PDFString.of(`/Helv 18 Tf ${r / 255} ${g / 255} ${b / 255}  rg`),
    Contents: PDFString.of(text),
    BS: { W: 0 }
  });
  return context.register(annots);
}
