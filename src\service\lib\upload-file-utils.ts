import { FileArray, UploadedFile } from 'express-fileupload';
import {
  isError,
  isSqExceptionResult,
  singleValue,
  sqExceptionResult,
  SqExceptionResult,
  toList
} from '../../serviceQuery';
import ServiceCenter, { newTid } from '../ServiceCenter';
import PSQLDriver from '../../serviceQuery/PSQLDriver';
import { PoolClient, QueryResult } from 'pg';
import LoggerCenter from '../../logger/LoggerCenter';
import path from 'path';
import { DbFile, DBFileSelect, IDType } from './esp/types';
import { _derived, hot_definitions, SYSTEM, WITH_ENCRYPTION } from '../constants';
import { base64_to_utf8 } from '../base64-utils';
import { addTask } from './task-utils';
import { StringRecord } from '../../types';

const logger = LoggerCenter.getLogger(path.basename(__filename));
const MAX_FILE_SIZE = 50 * 1024 * 1024;

export const toPathString = ({ directory, filename }: DbFile) => `${directory}${filename}`;
export const getDbFileTag = (file: DbFile, tag: string) => {
  const tagPair = (file.tags ?? '').split(',').filter((pair) => pair.startsWith(tag))[0];
  if (tagPair) {
    return tagPair.split(':')[1] ?? '';
  }
  return '';
};

export const sanitizeFilename = (filename: string): string => {
  return filename.replace(/[^\p{L}\p{N}\s.\-_]/gu, '');
};

export async function processUploadFiles(files: FileArray, userId: string, parameters: StringRecord): Promise<string[] | SqExceptionResult> {
  const fileTids: string[] = [];
  try {
    for (const [name, file] of Object.entries(files)) {
      if (Array.isArray(file)) {
        logger.warn(`Can not process multi file per form data name: ${name}!`);
      } else {
        if (file.size > MAX_FILE_SIZE) {
          logger.error(`File size exceeds the maximum limit (50M): ${file.name}`);
          return { exception: `File size exceeds the maximum limit (50M): ${file.name}` };
        }
        const result = await processSingleFile(name, file, userId);
        if (isSqExceptionResult(result)) {
          return result;
        }
        const { meetingItemTid = '' } = parameters
        await addTask({ fileTid: result, userId, meetingItemTid, type: 'fileUpload', msg: 'Uploading' })
        if (result) {
          fileTids.push(result);
        }
      }
    }
  } catch (e) {
    if (isError(e)) {
      return { exception: e.message };
    }
  }
  return fileTids;
}

async function processSingleFile(name: string, file: UploadedFile, userId: string): Promise<string | SqExceptionResult | null> {
  let filename = base64_to_utf8(name);
  if (!filename) {
    filename = Buffer.from(file.name, 'binary').toString('utf8');
  }
  filename = sanitizeFilename(filename);

  const directory = '/temp/';
  const owner = userId;
  const uploadFile = await saveDbFile({
    filename,
    directory,
    owner,
    data: file.data
  });

  if (isSqExceptionResult(uploadFile)) {
    return uploadFile;
  }

  return uploadFile?.fileTid || null;
}

interface SaveFileToDBArg {
  fileTid?: IDType;
  filename: string;
  directory: string;
  owner?: string;
  tags?: string;
  data: Buffer | string;
  encryption?: boolean;
}

export async function saveDbFile({
  fileTid,
  filename,
  directory,
  owner,
  tags = '',
  data,
  encryption = true
}: SaveFileToDBArg): Promise<DBFileSelect | SqExceptionResult> {
  const sc = ServiceCenter.getInstance();
  const sq = ServiceCenter.getInstance().getSq();
  const isNew = !fileTid;
  fileTid = isNew ? await newTid() : fileTid;
  const serviceId = isNew ? 'dbFile.insert' : 'dbFile.update';

  const res0 = await sc.runSystemSq({
    serviceId,
    parameters: { fileTid, filename, directory, owner, tags }
  });
  if (res0.exception) {
    return res0 as SqExceptionResult;
  }
  if (res0.rowsAffected !== 1) {
    return { exception: `Could not save (${serviceId}) file ${filename}` };
  }

  const res1 = await sq.driver.processSqlDirect('update esp.t_file set document=$1 where file_tid = $2', [
    data,
    +fileTid
  ]);
  if (res1.exception) {
    return res1 as SqExceptionResult;
  }
  if (res1.rowsAffected !== 1) {
    return { exception: `Could not update document for file ${filename}` };
  }
  if (encryption) {
    const res2 = await sc.runSystemSq({
      serviceId: 'dbFile.encryptDocument',
      parameters: { fileTid }
    });
    if (res2.rowsAffected !== 1) {
      return { exception: `Could not encrypt file: ${fileTid}` };
    }
  }

  const r = await sc.runSystemSq({
    serviceId: 'dbFile.get',
    parameters: { fileTid }
  });
  if (r.exception) {
    return r as SqExceptionResult;
  } else {
    const list = toList<DBFileSelect>(r);
    return list[0];
  }
}

export async function getDbFile(fileTid: string): Promise<DbFile | undefined> {
  const sq = ServiceCenter.getInstance().getSq();
  const res = await sq.run({
    serviceId: 'dbFile.get',
    parameters: { fileTid },
    userId: SYSTEM,
    roles: [SYSTEM]
  });
  return toList<DbFile>(res)[0];
}

export async function getDbFileByPath(directory: string, filename: string): Promise<DbFile | undefined> {
  const sq = ServiceCenter.getInstance().getSq();
  const res = await sq.run({
    serviceId: 'dbFile.getByDirectoryAndFilename',
    parameters: { directory, filename },
    userId: SYSTEM,
    roles: [SYSTEM]
  });
  const fileTid = singleValue(res);
  if (fileTid) {
    return getDbFile(fileTid);
  }
  return undefined;
}

export async function getSHA1(fileTid: string) {
  const sq = ServiceCenter.getInstance().getSq();
  const res = await sq.run({
    serviceId: 'dbFile.getSHA1',
    parameters: { fileTid },
    userId: SYSTEM,
    roles: [SYSTEM]
  });
  return singleValue(res);
}

export async function insertSHA1(fileTid: string, sha1: string) {
  const sq = ServiceCenter.getInstance().getSq();
  return sq.run({
    serviceId: 'dbFile.insertSHA1',
    parameters: { fileTid, sha1 },
    userId: SYSTEM,
    roles: [SYSTEM]
  });
}

export async function getDbFileContent(fileTid: IDType, decrypt = true): Promise<Buffer | SqExceptionResult> {
  const psqlDriver = ServiceCenter.getInstance().getSq().driver as PSQLDriver;
  let con: PoolClient | undefined;

  try {
    con = await psqlDriver.getConnection();
    if (con) {
      let pqResult: QueryResult;
      if (decrypt) {
        pqResult = await con.query({
          text: 'select pkg_crypto.decrypt_blob(document) as document from esp.t_file where file_tid = $1',
          values: [fileTid],
          rowMode: 'array'
        });
      } else {
        pqResult = await con.query({
          text: 'select document from esp.t_file where file_tid = $1',
          values: [fileTid],
          rowMode: 'array'
        });
      }
      return pqResult.rows[0][0];
    } else {
      return { exception: 'No connection!' };
    }
  } catch (e) {
    return sqExceptionResult(e);
  } finally {
    if (con) {
      psqlDriver.returnConnection(con);
    }
  }
}

export const useEncryption = ({ filename, directory }: DbFile) => {
  const fn = filename.toLowerCase();

  if (directory.startsWith(_derived) && fn.endsWith('.png')) {
    return true;
  }

  if (fn.endsWith('.html') || fn.endsWith('.htm')) {
    return true;
  }

  if (fn.endsWith('.js') || fn.endsWith('.css')) {
    return false;
  }

  for (const element of WITH_ENCRYPTION) {
    if (directory.startsWith(element)) {
      return true;
    }
  }
  return false;
};
export const getPathId = ({ fileTid, tags, pathId }: DbFile) => {
  if (pathId) {
    return pathId;
  }
  pathId = '';
  if (tags && tags.includes('pathId:')) {
    tags.split(',').forEach((tag) => {
      const [name, value] = tag.split(':');
      if (name === 'pathId' && value) {
        pathId = value;
      }
    });
  }
  return pathId || fileTid;
};

export const calculatePriority = (dbFile: DbFile) => {
  const prio = +dbFile.prio;
  if (prio > 0) {
    return prio.toString();
  }

  const fileName = dbFile.filename.toLowerCase();

  if (fileName.endsWith('.css')) {
    return '20';
  }
  if (fileName.endsWith('.js')) {
    return '20';
  }
  if (fileName.endsWith('.html')) {
    return '20';
  }
  if (fileName.startsWith(hot_definitions)) {
    return '20';
  }
  if (fileName.endsWith('.xml')) {
    return '20';
  }
  if (fileName.endsWith('.json')) {
    return '20';
  }
  return '50';
  // 1. .xml .js
  // 2. .xml
  // 3. .html
  // 4.*
  // 100. .pdf
};
export const listCurrentFiles = async (directory: string): Promise<DbFile[]> => {
  const sq = ServiceCenter.getInstance().getSq();
  const res = await sq.run({
    serviceId: 'dbFile.listFilesDeep',
    parameters: { directory },
    userId: SYSTEM,
    roles: [SYSTEM]
  });
  if (isSqExceptionResult(res)) {
    logger.error(`listCurrentFiles ${res.exception}`);
    return [];
  }
  return toList<DbFile>(res);
};

export const updateRealLength = (fileTid: string) => {
  const sq = ServiceCenter.getInstance().getSq();
  return sq.run({
    serviceId: 'dbFile.updateRealLength.dbside',
    parameters: { fileTid },
    userId: SYSTEM,
    roles: [SYSTEM]
  });
};
