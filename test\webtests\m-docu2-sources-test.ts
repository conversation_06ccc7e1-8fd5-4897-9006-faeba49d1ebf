import { test } from 'mocha';
import { localUrlJava, localUrlNode } from '../common';
import { runGetBlob, runGetkeys2 } from './getkeys2-utils';
import { toList } from '../../src/serviceQuery';
import { expect } from 'chai';
import { expectBufferEquals } from '../test-utils';

const localUrlNodeGetkeys2Sources = `${localUrlNode}serviceQuery/m.Getkeys2?sourcesOnly=true&name=sources`;

const docu2UrlNode = `${localUrlNode}mobile/docu2/`;
const docu2UrlJava = `${localUrlJava}mobile/docu2/`;

const testname = `(webtests) mdocu2-test`;
test(testname, async () => {
  console.log(`Start Node ${testname} get sources...: ${localUrlNodeGetkeys2Sources}!`);
  const getkeys2Json = toList(await runGetkeys2(localUrlNodeGetkeys2Sources, `${testname} Node`));

  const fileTidList = getkeys2Json.map(({ fileTid }) => fileTid).join(',');

  const docu2UrlFileTidListJava = `${docu2UrlJava}?fileTidList=${fileTidList}`;
  const docu2ResultJava = await runGetBlob(docu2UrlFileTidListJava, '');
  expect(docu2ResultJava.length).gt(1000, 'Java failed!');

  const docu2UrlFileTidListNode = `${docu2UrlNode}?fileTidList=${fileTidList}`;
  const docu2ResultNode = await runGetBlob(docu2UrlFileTidListNode, '');
  expect(docu2ResultNode.length).gt(1000, 'Node failed!');

  expectBufferEquals(docu2ResultNode, docu2ResultJava);
});
