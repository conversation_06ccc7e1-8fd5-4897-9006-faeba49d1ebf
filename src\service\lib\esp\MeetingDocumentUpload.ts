/* eslint-disable space-before-function-paren */
import {
  errorLine,
  isSqExceptionResult,
  ProcessLine,
  SqExceptionResult,
  sqExceptionResult,
  SqNodeFunction,
  SqResult,
  toString
} from '../../../serviceQuery';

import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';
import { getDbFile, getDbFileContent, sanitizeFilename } from '../upload-file-utils';
import { updateTaskProgress } from '../task-utils';
import winston from 'winston';
import { DbFile } from './types';
import ServiceCenter from '../../ServiceCenter';
import { PARAMs, SYSTEM } from '../../constants';
import { processPdfDerivedFiles } from './PdfUtilities';
import { moveFile } from '../DBFileService';
import { getFiles } from './DBFileHelper';
import { isAdminFor, isPublished } from '../service-util';

const logger = LoggerCenter.getLogger(path.basename(__filename));


export const MeetingDocumentUpload: SqNodeFunction = async ({ request }): Promise<SqResult> => {
  const { userId = 'ANONYMOUS', parameters } = request;
  logger.info(`MeetingDocumentUpload START : user: ${userId}`);
  logger.info(`MeetingDocumentUpload START : parameters: ${parameters}`);
  const lines: ProcessLine[] = [];
  const meetingItemTid = toString(parameters[PARAMs.meetingItemTid]);
  const ord = toString(parameters[PARAMs.ord]);

  const fileTids: string[] = getFiles(request);

  if (!meetingItemTid) {
    return errorResult('No meetingItemTid provided!', lines, logger);
  }
  if (fileTids.length === 0) {
    return errorResult('No upload file provided!', lines, logger);
  }
  let ex: SqExceptionResult;
  for (const fileTid of fileTids) {
    if (fileTid) {
      // await addTask({ fileTid, userId, meetingItemTid, type: 'fileUpload', msg: 'Uploading' })
      const dbFile = await getDbFile(fileTid);

      if (dbFile) {
        ex = await processSingleFile(dbFile, meetingItemTid, ord, userId);
        // CRI log pdf handling event
        logger.warn('>>>CRI event log: ' + userId + ' uploaded file ' + fileTid + ' within meeting item ' + meetingItemTid);
      } else {
        logger.error(`File not found for ${fileTid} - serious error!`);
      }
    } else {
      logger.error('Empty fileTid - serious error!');
    }
  }
  if (ex) {
    return ex;
  }
  return { processInfo: { lines } };
};

async function processSingleFile(dbFile: DbFile, meetingItemTid: string, ord: string, userId: string) {
  const { fileTid, filename } = dbFile;
  const sc = ServiceCenter.getInstance();
  const sq = sc.getSq();

  // Sanitize filename
  const sanitizedFilename = sanitizeFilename(filename);
  if (sanitizedFilename !== filename) {
    logger.warn(`Filename sanitized from ${filename} to ${sanitizedFilename}`);
  }

  if (!(await isAdminFor(meetingItemTid, userId))) {
    return sqExceptionResult(`User ${userId} is not admin for ${meetingItemTid}`);
  }

  if (await isPublished(meetingItemTid)) {
    return sqExceptionResult(`Meeting is already published or archived: ${meetingItemTid}`);
  }

  const directory = '/mp/' + meetingItemTid + '/';
  let mime = 'not-detected';
  await updateTaskProgress({ fileTid, type: 'fileUpload', progress: 20, msg: 'Fetching', status: 'progressing' });
  const fileContent = await getDbFileContent(fileTid);
  if (isSqExceptionResult(fileContent)) {
    logger.error(`Could not read file: ${fileTid}, ${filename}`);
    return;
  }
  mime = 'pdf';

  if (!filename.toLowerCase().endsWith('.pdf') || !mime.includes('pdf')) {
    const msg = `origin=MeetingDocumentUpload; userId=${userId}; Uploaded file is not a PDF document. Only PDF accepted for meeting documents! : ${filename} (mime:${mime})`;
    logger.error(msg);
    return;
  }

  await updateTaskProgress({ fileTid, type: 'fileUpload', progress: 70, msg: 'Processing', status: 'progressing' });
  await sq.run({
    serviceId: 'esp.mp.processUpload',
    userId: SYSTEM,
    roles: [SYSTEM],
    parameters: { fileTid, filename, ord, meetingItemTid, $USERID: userId }
  });
  const processPdfRes = await processPdfDerivedFiles(dbFile);
  if (isSqExceptionResult(processPdfRes)) {
    return processPdfRes;
  }
  moveFile(dbFile.fileTid, directory);
  await updateTaskProgress({ fileTid, type: 'fileUpload', progress: 100, msg: 'Finish Upload', status: 'progressing' });
}

function errorResult(m: string, lines: ProcessLine[], logger: winston.Logger): SqResult {
  logger.error(m);
  lines.push(errorLine(m));
  return { name: 'MeetingDocumentUpload', processInfo: { lines } };
}
