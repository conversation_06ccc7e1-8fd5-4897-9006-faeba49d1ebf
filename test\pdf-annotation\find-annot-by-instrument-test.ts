import { test } from 'mocha';
import fs from 'fs';
import path from 'path';
import { AnnotationType, InstrumentType } from '../../src/service/pdf-annotation';

const inDir = 'C:\\srdev\\tmp\\annot-env';

test('find-annot-by-instrument', async () => {
  await ********************(InstrumentType.ERASER);
});

async function ********************(id: InstrumentType) {
  const list = fs.readdirSync(inDir);
  list.forEach((f) => {
    if (path.basename(f).endsWith(`.json`)) {
      const fpath = path.join(inDir, f);
      const str = fs.readFileSync(fpath).toString('utf8');
      try {
        const json: AnnotationType = JSON.parse(str);
        if (json.pages?.length) {
          let hasInstrument = false;
          json.pages.forEach((p) =>
            (p.layers || []).forEach((l) =>
              (l.objects || []).forEach((o) => {
                if (o.objectMeta.instrument === id) {
                  hasInstrument = true;
                }
              })
            )
          );
          if (hasInstrument) {
            console.log(`${f} has instrument!`);
          }
        }
      } catch (e) {
        console.error(`${f} not parsable!`);
      }
    }
  });
}
