import { <PERSON><PERSON><PERSON><PERSON> } from 'cron';
import Logger<PERSON>enter from '../logger/LoggerCenter';
import path from 'path';
import moment from 'moment';
import { SqRequest } from '../serviceQuery';
import ServiceCenter from './ServiceCenter';
import { SYSTEM } from './constants';
import { getLock, releaseLock } from './lib/SyncService';
import { nowIso } from './service-utils';

const logger = LoggerCenter.getLogger(path.basename(__filename));

interface ScheduledJob {
  name: string;
  schedule: string;
  // Add other properties as needed
}

const allJobs: CronJob<any, ScheduledJob>[] = [];

export function startAllSchedulers() {
  // heart beat every minute

  addSqJob('QUARTZ_HEARTBEAT', '0 */5 * * * *', {
    serviceId: 'esp.saveByLabel',
    userId: SYSTEM,
    roles: [SYSTEM],
    parameters: { label: 'cloud.QUARTZ_HEARTBEAT' }
  });

  addSqJob('DB_CLEANUP', '0 0 3,6,11,15 * * *', {
    serviceId: 'dbFile.cleanupTempByDays',
    userId: SYSTEM,
    roles: [SYSTEM],
    parameters: {}
  });

  addSqJob('LOGMANAGER_UPDATE', '30 * * * * *', {
    serviceId: 'JavaLogManager.updateByAppProperties',
    userId: SYSTEM,
    roles: [SYSTEM],
    parameters: {}
  });

  addSqJob('fiveMinuteTask', '0 */5 * * * *', {
    serviceId: 'esp.fiveMinuteTask',
    userId: SYSTEM,
    roles: [SYSTEM],
    parameters: { label: 'cloud.FIVE_MINUTE_TASK_TS' }
  });

  addSqJob('fourThirtyTask', '0 30 4 * * *', {
    serviceId: 'fourThirtyTask',
    userId: SYSTEM,
    roles: [SYSTEM],
    parameters: { label: 'cloud.FOUR_THIRTY_TASK_TS' }
  });

  addSqJob('fiveThirtyTask', '0 30 5 * * *', {
    serviceId: 'fiveThirtyTask',
    userId: SYSTEM,
    roles: [SYSTEM],
    parameters: { label: 'cloud.FIVE_THIRTY_TASK_TS' }
  });
}

export function addTest(crondef: string) {
  const job = new CronJob(
    crondef, // cronTime
    function() {
      logger.info(`crondef: ${crondef} ${moment().format('YYYY-MM-DD hh:mm:ss')}`);
    }, // onTick
    null, // onComplete
    true, // start
    null // timeZone
  );
  allJobs.push(job);
  return job;
}

function addSqJob(jobName: string, crondef: string, request: SqRequest) {
  const sqJob = new CronJob(
    crondef, // cronTime
    async function() {
      const lock = await getLock(jobName, `Lock for ${jobName}, at: ${nowIso()}`);
      if (lock) {
        try {
          logger.info(`starting: ${jobName} with: ${crondef} at: ${nowIso()}`);
          const sq = ServiceCenter.getInstance().getSq();
          sq.run(request);
          logger.info(`done: ${jobName} with: ${crondef} at: ${nowIso()}`);
        } finally {
          await releaseLock(jobName);
        }
      } else {
        logger.info(`skip while locked: ${jobName} with: ${crondef} at: ${nowIso()}`);
      }
    }, // onTick
    null, // onComplete
    true, // start
    null // timeZone
  );
  allJobs.push(sqJob);
}

export function stopAllJobs() {
  for (const job of allJobs) {
    job.stop();
  }
}
