/* eslint-disable no-console */
import { expect } from 'chai';
import { SqResult } from '../src/serviceQuery';

/**
 *
 */
export type CompareJsonOptions = {
  // values of names in this list are not compared
  ignore?: string[];

  // array values of attributes in this list are only checked for their length
  sameLength?: string[];

  // every value attached to the name in this array is parsed as JSON and then compared
  isJsonString?: string[];

  // The name of the current value
  name?: string;

  // the path to the current value
  path?: string;
  verbose?: 'boolean';
};

/**
 *
 * @param actualJson
 * @param expectedJson
 * @param options
 */
export const compareJson = (actualJson: any, expectedJson: any, options: CompareJsonOptions = {}) => {
  const ignore: string[] = options.ignore || [];
  const sameLength: string[] = options.sameLength || [];
  const isJsonString: string[] = options.isJsonString || [];
  const path = options.path || '';
  const verbose = !!options.verbose;

  expect(typeof actualJson).equals(typeof expectedJson, 'Types do not match!');

  if (Array.isArray(actualJson)) {
    compareArrays(actualJson, expectedJson, options);
    return;
  }
  if (typeof actualJson === 'function') {
    return;
  }
  if (typeof actualJson !== 'object') {
    expect(actualJson).equals(expectedJson, `Attribute: ${options.name} Path: ${options.path}`);
  } else {
    const key1 = Object.keys(actualJson);
    const key2 = Object.keys(expectedJson);
    expect(key1.length).equals(
      key2.length,
      `Number of keys (attributes) is different! (path:${path}) \nkey1:${key1.join(',')} \nkey2:${key2.join(',')}`
    );

    const keyStr1 = key1.sort().join(',');
    const keyStr2 = key2.sort().join(',');
    expect(keyStr1).equals(keyStr2, `List of keys (attributes) is different! path:${path}`);

    for (const name of Object.keys(actualJson)) {
      if (ignore.includes(name)) {
        continue;
      }

      const value1 = actualJson[name];
      const value2 = expectedJson[name];
      expect(typeof value1).equals(typeof value2, `${name} has different value types! path:${path}`);
      // compareJson(value1, value2, { ...options, name });
      if (!value1 && !value2 && typeof value1 === typeof value2) {
        continue;
      }
      if (sameLength.includes(name)) {
        expect(value1.length).equals(value2.length, `different length for attribute: ${name} path:${path}`);
        continue;
      }
      if (isJsonString.includes(name)) {
        const json11 = JSON.parse(value1);
        const json12 = JSON.parse(value2);
        compareJson(json11, json12, { ...options, name, path: `${path}-${name}` });
      } else {
        compareJson(value1, value2, { ...options, name, path: `${path}-${name}` });
      }
      if (verbose) {
        console.info(`Attribute: ${name} successful checked! path:${path}`);
      }
    }
  }
};

export const compareArrays = (actualJson: Array<any>, expectedJson: Array<any>, options: CompareJsonOptions = {}) => {
  const path = options.path || '';
  expect(actualJson.length).equals(expectedJson.length, `Array are of different length! path: ${path}`);
  const name = options.name;
  if ((options.sameLength || []).includes(name)) {
    return;
  }
  for (let i = 0; i < actualJson.length; i++) {
    compareJson(actualJson[i], expectedJson[i], { ...options, name: '', path: `${path}-[${i}]` });
  }
};

export const expectBufferEquals = (actual: Buffer, expected: Buffer) => {
  let index = 0;
  for (const ex of expected) {
    const ac = actual[index];
    expect(ac).equals(ex, `Difference at ${index}`);
    index++;
  }
};

/**
 *
 */
export type CompareResultOptions = {
  // values of names in this list are not compared
  ignoreHeader?: string[];

  // every value attached to the name in this array is parsed as JSON and then compared
  ignoredFields?: string[];
};
export const compareResult = (actualResult: SqResult, expectedResult: SqResult, options: CompareResultOptions = {}) => {
  const ignoreHeader = options.ignoreHeader || [];
  const actualHeader = actualResult.header;
  const expectedHeader = expectedResult.header;
  expect(actualHeader?.length).equals(expectedHeader?.length, 'Different header length!');
  const actualTable = actualResult.table;
  const expectedTable = expectedResult.table;
  expect(actualTable?.length).equals(expectedTable?.length, 'Different table length!');
  if (!actualHeader) {
    return;
  }
  // check header
  expect(actualHeader.join(',')).equals(expectedHeader.join(','), 'Different header!');
  if (!actualTable) {
    return;
  }
  for (let i = 0; i < actualTable.length; i++) {
    const actualRow = actualTable[i];
    const expectedRow = expectedTable[i];
    for (let j = 0; j < actualRow.length; j++) {
      const actualCell = actualRow[j];
      const expectedCell = expectedRow[j];
      const actualHead = actualHeader[j];
      if (actualCell !== expectedCell && !ignoreHeader.includes(actualHead)) {
        expect.fail(`Different column (${actualHead}) values: expected: ${expectedCell}, actual: ${actualCell} `);
      }
    }
  }
};
