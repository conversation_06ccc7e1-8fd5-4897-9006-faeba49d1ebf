import { test } from 'mocha';
import { readFileSync } from 'fs';
import path from 'path';
import { testdataDir } from '../common';
import decompress from 'decompress';
import { expect } from 'chai';
import { AnnotationType } from '../../src/service/pdf-annotation';
import { isAnnotationTypeEmpty } from '../../src/service/lib/esp/annotation-utils';

const cabcomFile = path.join(testdataDir, 'cabcom.zip');

test(`open and read cabcom zip file`, async () => {
  await readAndOpenCabcom();
});

async function readAndOpenCabcom() {
  const zipContent = readFileSync(cabcomFile);
  const res = await decompress(zipContent);
  expect(res.length).equals(1);
  const content = res[0].data;
  const json = JSON.parse(content.toString('utf8')) as AnnotationType;
  expect(isAnnotationTypeEmpty(json)).false;
  expect(json.pages[0].layers.length).equals(1);
}
