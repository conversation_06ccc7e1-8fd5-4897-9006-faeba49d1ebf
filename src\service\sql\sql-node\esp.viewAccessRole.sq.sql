--
-- SERVICE_ID       = esp.viewAccessRole.select
-- ACCESS_GROUPS    = ESP_ADMIN
-- 

set-if-empty:userId=%
;
set-if-empty:roleName=%
;
set-if-empty:lastName=%
;
set-if-empty:firstName=%
;
select 
ar.USER_ID, ar.ROLE_NAME,  vu1.FIRST_NAME, vu1.LAST_NAME
from esp.v_role_all ar
left join 
ESP.V_USER vu1 on vu1.USER_ID = ar.USER_ID
where 
ar.USER_ID is not null
and
ar.USER_ID like :userId
and
ar.ROLE_NAME like :roleName
and
upper(vu1.FIRST_NAME) like upper(:firstName)
and
upper(vu1.LAST_NAME) like upper(:lastName)
order by 1

