








-- NEW STUFF


--
-- KEY           = role.select
-- ACCESS_GROUPS = ESP_ADMIN_MGMT,ESP_ADMIN
--

set-if-empty: roleName = %
;
set-if-empty: userId = %
;
select *
from(
        select *
        from esp.v_access_role
        union all
        select *
        from esp.v_access_role_temp
    ) r
where r.USER_ID like :userId
  and r.ROLE_NAME like :roleName

--
-- KEY           = role.get
-- ACCESS_GROUPS = ESP_ADMIN_MGMT,ESP_ADMIN
--
select 
 USER_ID, 
 ROLE_NAME, 
 TO_CHAR(VALID_FROM, 'YYYY-MM-DD') as VALID_FROM, 
 TO_CHAR(VALID_TO, 'YYYY-MM-DD') as VALID_TO
from ESP.T_ROLE
where 
ROLE_NAME = :roleName
and
USER_ID = :userId

--
-- KEY           = role.update
-- ACCESS_GROUPS = ESP_ADMIN_MGMT,ESP_ADMIN
--
update ESP.T_ROLE
set
    VALID_FROM = TO_DATE(:validFrom,'YYYY-MM-DD') ,
    VALID_TO = TO_DATE(:validTo,'YYYY-MM-DD')
where 
ROLE_NAME = :roleName
and
USER_ID = :userId
