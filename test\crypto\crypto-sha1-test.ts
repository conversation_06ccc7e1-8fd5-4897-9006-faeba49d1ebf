import { test } from 'mocha';
import { getEncrContent, getMeta } from './crypto-utils';
import { expect } from 'chai';
import { createHash } from 'crypto';

test(`Read test file and compare sha to the meta data`, async () => {
  for (let index = 1; index <= 10; index++) {
    const meta = getMeta(index);
    if (!meta) {
      continue;
    }
    expect(!!meta.encryptionKey).true;
    const encr = getEncrContent(index);

    const sha1Hash = createHash('sha1');
    sha1Hash.update(encr);
    const sha1HashResult = sha1Hash.digest('hex');
    console.log(`meta: ${meta.sha1hash}, calculated: ${sha1HashResult}`);

    expect(meta.sha1hash).equals(sha1HashResult);
  }
});
