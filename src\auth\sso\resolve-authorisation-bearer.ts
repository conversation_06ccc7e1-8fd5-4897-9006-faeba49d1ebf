import { Request } from 'express';
import LoggerCenter from '../../logger/LoggerCenter';
import path from 'path';
import * as process from 'process';
import OktaJwtVerifier from '@okta/jwt-verifier';
import { SessionCache } from '../../service/SessionCache';

const SECURITY_TOKEN_NAME = 'Authorization';
const logger = LoggerCenter.getLogger(path.basename(__filename));

const oktaJwtVerifier = new OktaJwtVerifier({
  issuer: process.env.OKTA_ISSUER
});
const oktaAudience = process.env.OKTA_AUDIENCE;

function getCookie(name: string, req: Request) {
  let cookieValue = '';
  if (req.cookies) {
    cookieValue = req.cookies[name];
    //logger.info('cookie: '+cookieValue);
  }
  return cookieValue;
}

function getSecurityCookieToken(req: Request) {
  return getCookie(SECURITY_TOKEN_NAME, req);
}

const getTokenFromAuthorizationHeader = (req: Request): string | null => {
  logger.info('No security cookie found, trying Authorization header');
  const authHeader = req.header('Authorization') || req.header('authorization');
  logger.info('Authorization header: ' + authHeader);
  if (authHeader) {
    return (authHeader || ' ').split(' ')[1].trim();
  } else {
    logger.error(`No Authorization header found: ${req.originalUrl}`);
    logger.error(`Authorization header: ${authHeader}`);
    return null;
  }
};

const getUserIdFromToken = async (token: string): Promise<string> => {
  let userId = SessionCache.getInstance().getUserId(token);
  if (userId) {
    return userId;
  }

  try {
    const oktaJwt = await oktaJwtVerifier.verifyAccessToken(token, oktaAudience);
    if (oktaJwt) {
      userId = oktaJwt?.claims['swissreuid'] as string;
      if (!userId) {
        logger.warn('No swissreuid claim found!');
        return '';
      }
      const userEmail = oktaJwt?.claims?.sub || '';
      SessionCache.getInstance().addToken(token, userId, userEmail);
      return userId;
    } else {
      logger.warn('Invalid token found');
      return '';
    }
  } catch (error) {
    handleTokenVerificationError(error);
    return '';
  }
};

const handleTokenVerificationError = (error: any) => {
  if (error.name === 'JwtExpiredError') {
    logger.info(error.message);
  } else {
    logger.warn(error.message);
  }
};

export const resolveUidFromAuthorizationBearer = async (req: Request) => {
  let token = getSecurityCookieToken(req);
  if (!token) {
    token = getTokenFromAuthorizationHeader(req);
    if (!token) {
      return '';
    }
  }

  return await getUserIdFromToken(token);
};