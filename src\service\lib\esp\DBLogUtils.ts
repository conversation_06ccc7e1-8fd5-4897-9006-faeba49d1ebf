/* eslint-disable no-console,space-before-function-paren */
import ServiceCenter from '../../ServiceCenter';
import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';
import { SYSTEM } from '../../constants';
import { abbreviateString } from '../../../utils';

export type ErrSeverity = 'I' | 'W' | 'E';
const logger = LoggerCenter.getLogger(path.basename(__filename));
export const track = async (errSeverity: ErrSeverity, errMessage = '', errOrigin: string, userId = '') => {
  const sc = ServiceCenter.getInstance();
  const sq = sc.getSq();
  errMessage = abbreviateString(errMessage, 3900);
  if (sc.isLocal()) {
    logger.info(`track: ${errSeverity}: ${errMessage} errOrigin:${errOrigin}`);
  } else {
    return sq.run({
      serviceId: 'dbLog.tracking.insert',
      userId: SYSTEM,
      roles: [SYSTEM],
      parameters: {
        errSeverity,
        errMessage,
        errOrigin,
        userId
      }
    });
  }
};
