import LoggerCenter from '../../logger/LoggerCenter';
import path from 'path';
import axios from 'axios';
import { AutoRestartManager } from './AutoRestartManager';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export interface HealthMonitorConfig {
  enabled: boolean;
  checkInterval: number; // seconds
  failureThreshold: number; // consecutive failures before restart
  healthCheckUrl: string;
  timeout: number; // milliseconds
  restartEnabled: boolean;
}

export class HealthMonitorService {
  private static instance: HealthMonitorService;
  private config: HealthMonitorConfig;
  private intervalId: NodeJS.Timeout | null = null;
  private consecutiveFailures = 0;
  private lastHealthCheck: Date | null = null;
  private isMonitoring = false;
  private autoRestartManager: AutoRestartManager;

  private constructor() {
    this.config = this.loadConfig();
    this.autoRestartManager = AutoRestartManager.getInstance();
    logger.info('HealthMonitorService initialized with config:', this.config);
  }

  public static getInstance(): HealthMonitorService {
    if (!HealthMonitorService.instance) {
      HealthMonitorService.instance = new HealthMonitorService();
    }
    return HealthMonitorService.instance;
  }

  private loadConfig(): HealthMonitorConfig {
    const port = process.env.PORT || '3001';
    return {
      enabled: process.env.HEALTH_MONITOR_ENABLED === 'true',
      checkInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '60'), // 60 seconds default
      failureThreshold: parseInt(process.env.HEALTH_FAILURE_THRESHOLD || '3'), // 3 failures default
      healthCheckUrl: process.env.HEALTH_CHECK_URL || `http://localhost:${port}/api/v1/health/detailed`,
      timeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT || '10000'), // 10 seconds default
      restartEnabled: process.env.AUTO_RESTART_ENABLED === 'true'
    };
  }

  public start(): void {
    if (!this.config.enabled) {
      logger.info('Health monitoring is disabled');
      return;
    }

    if (this.isMonitoring) {
      logger.warn('Health monitoring is already running');
      return;
    }

    logger.info(`Starting health monitoring with ${this.config.checkInterval}s interval`);
    this.isMonitoring = true;
    this.consecutiveFailures = 0;

    // Perform initial health check after a short delay to allow server to fully start
    setTimeout(() => {
      this.performHealthCheck();
    }, 30000); // 30 seconds delay

    // Set up periodic health checks
    this.intervalId = setInterval(() => {
      this.performHealthCheck();
    }, this.config.checkInterval * 1000);
  }

  public stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isMonitoring = false;
    logger.info('Health monitoring stopped');
  }

  private async performHealthCheck(): Promise<void> {
    try {
      logger.debug('Performing health check...');

      const response = await axios.get(this.config.healthCheckUrl, {
        timeout: this.config.timeout,
        validateStatus: (status) => status < 500 // Accept any status < 500 as success
      });

      this.lastHealthCheck = new Date();

      if (response.status === 200 && response.data?.healthy !== false) {
        // Health check passed
        if (this.consecutiveFailures > 0) {
          logger.info(`Health check recovered after ${this.consecutiveFailures} failures`);
          this.consecutiveFailures = 0;
        } else {
          logger.debug('Health check passed');
        }
      } else {
        // Health check failed (unhealthy response)
        this.handleHealthCheckFailure(`Unhealthy response: ${response.status} - ${JSON.stringify(response.data)}`);
      }

    } catch (error) {
      // Health check failed (network error, timeout, etc.)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.handleHealthCheckFailure(`Health check request failed: ${errorMessage}`);
    }
  }

  private handleHealthCheckFailure(reason: string): void {
    this.consecutiveFailures++;
    logger.warn(`Health check failed (${this.consecutiveFailures}/${this.config.failureThreshold}): ${reason}`);

    if (this.consecutiveFailures >= this.config.failureThreshold) {
      logger.error(`Health check failed ${this.consecutiveFailures} consecutive times. Threshold reached.`);

      if (this.config.restartEnabled) {
        logger.error('Triggering automatic restart...');
        this.triggerRestart();
      } else {
        logger.error('Automatic restart is disabled. Manual intervention required.');
      }
    }
  }

  private triggerRestart(): void {
    // Stop monitoring to prevent multiple restart attempts
    this.stop();

    // Trigger restart through AutoRestartManager
    this.autoRestartManager.initiateRestart('Health check failures exceeded threshold');
  }

  public getStatus() {
    return {
      enabled: this.config.enabled,
      monitoring: this.isMonitoring,
      consecutiveFailures: this.consecutiveFailures,
      lastHealthCheck: this.lastHealthCheck,
      config: this.config
    };
  }

  public updateConfig(newConfig: Partial<HealthMonitorConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('Health monitor config updated:', this.config);

    // Restart monitoring with new config if currently running
    if (this.isMonitoring) {
      this.stop();
      this.start();
    }
  }
}
