import { expect } from 'chai';
import { sanitizeFilename } from '../../src/service/lib/upload-file-utils';

describe('filename-sanitize-tests', () => {
  it('path with underscore', async () => {
    const f1 = 'file_name';
    expect(sanitizeFilename(f1)).equals('file_name');
  });
  it('path with  öüä', async () => {
    const f1 = 'öüä';
    expect(sanitizeFilename(f1)).equals('öüä');
  });
  it('path with  c-a', async () => {
    const f1 = 'c-a';
    expect(sanitizeFilename(f1)).equals('c-a');
  });
  it('path with  c- a', async () => {
    const f1 = 'c- a';
    expect(sanitizeFilename(f1)).equals('c- a');
  });
});
