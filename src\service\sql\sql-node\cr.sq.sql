-- 
-- SERVICE_ID = cr_br_details
--

select q.question_tid, pkg_crypto.decrypt_varchar( q.nme_enc ) nme,
case when q.details_enc is null then null else
pkg_crypto.decrypt_cmnt(q.details_enc) end details,
q.due, q.options, q.option_type, q.question
,(select agenda_tid from esp.t_mp_agenda where meeting_tid = :in_meeting_tid
 and item_type = 'REP'
) agenda_tid
from esp.t_mp_agenda a
join esp.t_mp_meeting tmm on a.meeting_tid = tmm.meeting_tid and tmm.status_tid <> '1'
join esp.v_cr_read_access ac on tmm.meeting_tid = ac.meeting_tid 
inner join esp.t_cr_mp_link l on l.meeting_item_tid = a.agenda_tid
inner join esp.t_cr_question q on q.question_tid = l.question_tid
where (Q.QUESTION_TID = :in_meeting_tid
or
a.agenda_tid = :in_meeting_tid
or
a.meeting_tid = :in_meeting_tid)
;

--
-- SERVICE_ID   = cr_br_answer
--

select v.*
from esp.v_cr_vote_summary v
join esp.v_cr_read_access vcra on vcra.meeting_tid = v.meeting_tid
join esp.t_mp_meeting tmm on v.meeting_tid = tmm.meeting_tid and tmm.status_tid <> '1'
join esp.t_cr_question vq on v.question_tid = vq.question_tid
where v.meeting_tid = :in_meeting_tid and (vcra.r2='admin' or vq.votes_visible = 'Y' )
;

--
-- SERVICE_ID = cr_br_results
--
select dta.question_tid, dta.agenda_tid, dta.meeting_tid, dta.user_id, dta.first_name, dta.last_name,
case when dta.answer is null then 'no response' else dta.answer end as answer, dta.cmt, dta.edited, dta.edited_by, dta.edited_first_name, dta.edited_last_name,
       STRING_AGG(cast(dta.file_tid as text), ',' order by last_name, first_name) as file_tid
from
(
select v.* , ae.file_tid
from (
	select vaa.* from (SELECT
    va.*,
    ROW_NUMBER() OVER(PARTITION BY va.user_id  ORDER BY va.edited DESC) AS rn
  FROM esp.v_cr_vote_all va 
  where meeting_tid = :in_meeting_tid 
  and ((answer is not null and answer <> 'Comment without voting') or answer is null)
  ) vaa where vaa.rn = 1
) v
join esp.t_cr_question vq on v.question_tid = vq.question_tid
join esp.v_cr_read_access ac on v.meeting_tid = ac.meeting_tid
left outer join esp.t_cr_answer_evidence ae on (AE.QUESTION_TID = v.question_Tid and ae.user_id = v.user_id)
where v.agenda_tid =
(
select agenda_tid from esp.t_mp_agenda where meeting_tid = :in_meeting_tid
and item_type = 'CR'
)
and (ac.r2='admin' or vq.votes_visible = 'Y' or v.edited_by=esp.user_code() or (v.edited_by is null and v.user_id=esp.user_code()))
) dta
group by dta.question_tid, dta.agenda_tid, dta.meeting_tid, dta.user_id, dta.first_name, dta.last_name,
dta.answer, dta.cmt, dta.edited, dta.edited_by, dta.edited_first_name, dta.edited_last_name
order by last_name, first_name;


--
-- SERVICE_ID = cr_br_results_full
--
select dta.question_tid, dta.agenda_tid, dta.meeting_tid, dta.user_id, dta.first_name, dta.last_name,
dta.answer, dta.cmt, dta.edited, dta.edited_by, dta.edited_first_name, dta.edited_last_name,
STRING_AGG(cast(dta.file_tid as text), ',' order by last_name, first_name) as file_tid
from
(
select v.* , ae.file_tid
from esp.v_cr_votes_full v
join esp.t_cr_question vq on v.question_tid = vq.question_tid
join esp.t_mp_meeting tmm on v.meeting_tid = tmm.meeting_tid and tmm.status_tid <> '1'
join esp.v_cr_read_access ac on tmm.meeting_tid = ac.meeting_tid
left outer join esp.t_cr_answer_evidence ae on (AE.QUESTION_TID = v.question_Tid and ae.user_id = v.user_id)
where v.agenda_tid =
(
select agenda_tid from esp.t_mp_agenda where meeting_tid = :in_meeting_tid
and item_type = 'CR'
)
and (ac.r2='admin' or vq.votes_visible = 'Y' or v.edited_by=esp.user_code() or (v.edited_by is null and v.user_id=esp.user_code()))
) dta
group by dta.question_tid, dta.agenda_tid, dta.meeting_tid, dta.user_id, dta.first_name, dta.last_name,
dta.answer, dta.cmt, dta.edited, dta.edited_by, dta.edited_first_name, dta.edited_last_name
order by edited desc
;

--
-- SERVICE_ID = cr_results_byagenda_agg
--

select dta.question_tid, dta.agenda_tid, dta.meeting_tid, dta.user_id, dta.first_name, dta.last_name,
dta.answer, dta.cmt, dta.edited, dta.edited_by, dta.edited_first_name, dta.edited_last_name,
STRING_AGG(cast(dta.file_tid as text), ',' order by last_name, first_name) as file_tid
from
(
select v.* , ae.file_tid
from esp.v_cr_votes_full v
join esp.t_mp_meeting tmm on v.meeting_tid = tmm.meeting_tid
join esp.v_cr_read_access ac on tmm.meeting_tid = ac.meeting_tid
join esp.t_cr_question vq on v.question_tid = vq.question_tid
left outer join esp.t_cr_answer_evidence ae on (AE.QUESTION_TID = v.question_Tid and ae.user_id = v.user_id and ae.answer_tid = v.answer_tid)
where v.agenda_tid =
(
select agenda_tid from esp.t_mp_agenda where meeting_tid = (
select meeting_tid from esp.v_cr_read_access raa where meeting_tid = (
    select meeting_tid from esp.t_mp_agenda where agenda_tid = :in_agenda_tid
)) and item_type = 'CR' 
)
and (ac.r2='admin' or vq.votes_visible = 'Y' or (v.edited_by=esp.user_code() or (v.edited_by is null and v.user_id=esp.user_code())))
) dta
group by dta.question_tid, dta.agenda_tid, dta.meeting_tid, dta.user_id, dta.first_name, dta.last_name,
dta.answer, dta.cmt, dta.edited, dta.edited_by, dta.edited_first_name, dta.edited_last_name
order by edited desc

-- SERVICE_ID         = cr_answer_report_byagenda

select su.* from esp.v_cr_vote_summary su
join esp.v_cr_read_access raa on su.meeting_tid = raa.meeting_tid and raa.r2='admin' and raa.readonly = 'false' 
where su.meeting_tid=:in_meeting_tid;

-- SERVICE_ID         = cr_answer_report_byagenda_all

select su.* from esp.v_cr_vote_summary su
join esp.v_cr_read_access raa on su.meeting_tid = raa.meeting_tid and raa.r2='admin' and raa.readonly = 'false'
;

-- SERVICE_ID 		= cr_readonly_create

WITH deleted AS (
    DELETE FROM esp.t_cr_readonly r 
    WHERE r.user_id = :in_user_id
    AND r.meeting_tid = :in_meeting_tid
    AND EXISTS (
        SELECT 1 FROM esp.v_cr_read_access raa 
        WHERE raa.meeting_tid = r.meeting_tid 
        AND raa.r2='admin' 
        AND raa.readonly = 'false'
    )
    RETURNING *
)
INSERT INTO esp.t_cr_readonly (meeting_tid, user_id)
SELECT :in_meeting_tid, :in_user_id
WHERE EXISTS (
    SELECT 1 FROM esp.v_cr_read_access raa 
    WHERE raa.meeting_tid = :in_meeting_tid
    AND raa.r2='admin' 
);

-- SERVICE_ID 		= cr_readonly_delete

delete from esp.t_cr_readonly r 
where 
r.user_id = :in_user_id
and r.meeting_tid = :in_meeting_tid
AND EXISTS (
    SELECT 1 FROM esp.v_cr_read_access raa 
    WHERE raa.meeting_tid = r.meeting_tid 
    AND raa.r2='admin' 
);

-- SERVICE_ID 		= cr_get_question_simple_all

select * from esp.v_iesp_cr_question
;

-- SERVICE_ID 		= cr_read_access_admin

WITH UserList AS (
    SELECT 
        TYPE.user_id,
        MAX(u.last_name) as last_name,
        MAX(u.first_name) as first_name,
        CASE (MIN (TYPE.access_right_tid_min))
            WHEN 4 THEN 'read' ELSE '' END R1,
        CASE (MAX (TYPE.access_right_tid))
            WHEN 6 THEN 'admin' ELSE '' END R2,
        MAX (CASE WHEN det.access_right_tid = 1 THEN 1
                  WHEN det.access_right_tid = 0 THEN 0
                  WHEN type.valid_from <= m.updated_stat_firstpub OR m.updated_stat_firstpub IS NULL THEN 1
                  ELSE 0 END) access_ok,
        MAX (CASE WHEN type.user_id=esp.user_code() 
        or type.user_id IN (select distinct user_id from esp.v_cr_answer_all vcaa where vcaa.meeting_tid = m.meeting_tid and answer_tid is not null) 
        THEN 0 ELSE 1 END) enabled,
        MAX(CASE WHEN ro.user_id IS NULL THEN 'false' ELSE 'true' END) readonly,
        MAX(u.email_address) external_mail
    FROM esp.v_mp_access_type_all TYPE
        INNER JOIN esp.t_mp_meeting m ON m.type_tid = TYPE.type_tid
        INNER JOIN esp.V_SR_USER u ON u.user_id= TYPE.user_id
        LEFT OUTER JOIN esp.t_cr_readonly ro ON ro.meeting_tid = m.meeting_tid AND ro.user_id = type.user_id
        LEFT JOIN esp.t_mp_access_detail det ON det.meeting_item_tid = m.meeting_tid AND det.user_id = TYPE.user_id
        LEFT JOIN esp.t_mp_access_detail detAll ON detAll.meeting_item_tid = m.meeting_tid
            AND detAll.update_date < TYPE.valid_from
            AND detAll.access_right_tid = 0
    WHERE m.meeting_tid= CAST(:in_meeting_item_tid AS numeric)
    GROUP BY type.user_id, m.meeting_tid
)
SELECT * FROM UserList
WHERE EXISTS (
    SELECT 1 FROM UserList WHERE user_id = esp.user_code()
)
ORDER BY last_name, first_name;

-- SERVICE_ID 		= agenda_read_access_admin

WITH UserList AS (
    SELECT at.user_id,
       MAX(u.last_name)  as                                        last_name,
       MAX(u.first_name) as                                        first_name,
       case (MIN(at.access_right_tid_min))
           when 4 then 'read'
           else '' end                                             R1,
       case (MAX(at.access_right_tid))
           when 6 then 'admin'
           else '' end                                             R2,
       case
           when max(det.access_right_tid) is null
               and min(detAll.update_date) is not null
               and min(detAll.update_date) < min(at.valid_from) then 0
           when min(det.access_right_tid) = 0 then 0
           else 1
           end                                                     access_ok,
       max(case when at.user_id = esp.user_code() then 0 else 1 end) enabled,
       MAX(u.email_address)                                   external_mail
	FROM esp.t_mp_agenda a
         inner join esp.t_mp_meeting m
                    on m.meeting_tid = a.meeting_tid
         inner join esp.v_mp_access_type_all at
                    on at.type_tid = m.type_tid
         INNER JOIN esp.V_SR_USER u
                    on u.user_id = at.user_id
         LEFT JOIN esp.t_mp_access_detail det
                   ON det.meeting_item_tid = a.agenda_tid
                       AND det.user_id = at.user_id
         LEFT JOIN esp.t_mp_access_detail detAll
                   ON detAll.meeting_item_tid = a.agenda_tid
                       AND detAll.update_date < at.valid_from
                       AND detAll.access_right_tid = 0
	where a.agenda_tid = :in_meeting_item_tid
	GROUP BY at.user_id, a.agenda_tid
)
SELECT * FROM UserList
WHERE EXISTS (
    SELECT 1 FROM UserList WHERE user_id = esp.user_code() and r2='admin'
)
ORDER BY last_name, first_name;

-- SERVICE_ID 		= folder_read_access_admin

WITH UserList AS (
SELECT at.user_id,
       MAX(u.last_name)  as                                        last_name,
       MAX(u.first_name) as                                        first_name,
       case (MIN(at.access_right_tid_min))
           when 4 then 'read'
           else '' end                                             R1,
       case (MAX(at.access_right_tid))
           when 6 then 'admin'
           else '' end                                             R2,
       case
           when max(det.access_right_tid) is null
               and min(detAll.update_date) is not null
               and min(detAll.update_date) < min(at.valid_from) then 0
           when min(det.access_right_tid) = 0 then 0
           else 1
           end                                                     access_ok,
       max(case when at.user_id = esp.user_code() then 0 else 1 end) enabled,
       MAX(u.email_address)                                   external_mail
FROM esp.t_mp_meeting m
         inner join esp.v_mp_access_type_all at
                    on at.type_tid = m.type_tid
         INNER JOIN esp.V_SR_USER u
                    on u.user_id = at.user_id
         LEFT JOIN esp.t_mp_access_detail det
                   ON det.meeting_item_tid = m.meeting_tid
                       AND det.user_id = at.user_id
         LEFT JOIN esp.t_mp_access_detail detAll
                   ON detAll.meeting_item_tid = m.meeting_tid
                       AND detAll.update_date < at.valid_from
                       AND detAll.access_right_tid = 0
WHERE m.meeting_tid = :in_meeting_item_tid
GROUP BY at.user_id, m.meeting_tid
)
SELECT * FROM UserList
WHERE EXISTS (
    SELECT 1 FROM UserList WHERE user_id = esp.user_code() and r2='admin'
)
ORDER BY last_name, first_name;

-- SERVICE_ID 		= resolution_evidence_documents_download

WITH DocumentList AS (
	select
		ae.FILE_TID, 'evidence_for_' ||  v.LAST_NAME  || '_' || v.FIRST_NAME as FOLDER, f.FILENAME
	from
		ESP.V_CR_VOTES_FULL v
	left join
		ESP.T_CR_ANSWER_EVIDENCE ae  on (AE.QUESTION_TID = V.QUESTION_TID and AE.USER_ID = V.USER_ID and AE.ANSWER_TID = V.ANSWER_TID)
	left join
		ESP.T_FILE f on f.FILE_TID = ae.FILE_TID
	where ae.FILE_TID > 0 and v.MEETING_TID = :meetingTid
)
SELECT * FROM DocumentList
WHERE EXISTS (
	SELECT 
		1
	FROM esp.v_mp_access_type_all TYPE
		INNER JOIN esp.t_mp_meeting m 
			ON m.type_tid = TYPE.type_tid
	    INNER JOIN esp.V_SR_USER u 
	    	on u.user_id= TYPE.user_id
	    left outer join esp.t_cr_readonly ro 
	    	on ro.meeting_tid = m.meeting_tid and ro.user_id = type.user_id
	    LEFT JOIN esp.t_mp_access_detail det
	    	ON det.meeting_item_tid = m.meeting_tid
	           AND det.user_id = TYPE.user_id
	    LEFT JOIN esp.t_mp_access_detail detAll
	        ON     detAll.meeting_item_tid = m.meeting_tid
	           AND detAll.update_date < TYPE.valid_from
	           AND detAll.access_right_tid = 0
	WHERE 
		m.meeting_tid= cast(:meetingTid as numeric) and type.user_id = esp.user_code() and type.access_right_tid=6
);
