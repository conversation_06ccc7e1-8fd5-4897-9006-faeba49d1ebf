import { expect } from 'chai';
import { canUploadFiles } from '../../src/service/service-utils';

describe('can-upload-file-test', () => {
  it(`test-1`, async () => {
    const roles = ['TEST'];
    expect(canUploadFiles(roles)).false;
  });
  it(`test-2`, async () => {
    const roles = ['TEST', 'ESP_ADMIN'];
    expect(canUploadFiles(roles)).true;
  });
  it(`test-3`, async () => {
    const roles = ['TEST', 'ESP_PROVIDER'];
    expect(canUploadFiles(roles)).true;
  });
  it(`test-4`, async () => {
    const roles = ['TEST', 'ESP_USER'];
    expect(canUploadFiles(roles)).false;
  });
});
