import { isSqExceptionResult, SqContext, SqRequest, SqResult } from '../../../serviceQuery';
import ServiceCenter, { newTid } from '../../ServiceCenter';
import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';
import { getDbFile, getDbFileByPath } from '../upload-file-utils';
import { DbFile, ReportMailParam, AttachedFile } from './types';
import { checkFileAccess } from '../check-file-access';
import { createAnnotationDirectory } from './DBFileHelper';
import { getUserData } from './UserInfo';
import { ANNOTATED_MAILED, ORIGINAL_MAILED } from './DBFileRelSupport';
import { readFileSync } from 'fs';
import { Compile, parse } from 'velocityjs';

const logger = LoggerCenter.getLogger(path.basename(__filename));

const EMAIL_TEMPLATE = path.join(__dirname, '../../../templates/DocumentAnnotationMailTemplate.html');

const MAIL_addAttachment = 'MAIL.addAttachment';
const MAIL_insertMail = 'MAIL.insertMail';
export const ReportMailMA = async({ request }: { request: SqRequest; context: SqContext }): Promise<SqResult> => {
  const sc = ServiceCenter.getInstance();

  const { userId: userIdFromRequest, userid: useridFromRequest, parameters: params, roles } = request;
  logger.info('ReportMailMA: request: ', request, params, roles);
  const userId = params?.userid.toString() || userIdFromRequest || useridFromRequest;
  //
  //
  // request parameters
  //

  logger.warn(`ReportMailMA: start for user: ${userId}`);

  const comment = (params.comment || '').toString();
  const reportMailParams = (params.reportMailParams || '').toString();

  const reportMailParamsArray: ReportMailParam[] = JSON.parse(reportMailParams);
  if (!reportMailParams) {
    const exception = 'No ReportMailParam available. Abort ReportMailMA';
    logger.error(exception);
    return { exception };
  }

  const { attachments, reportFiles } = await getAttachments(reportMailParamsArray, userId, roles);
  if (attachments.length === 0) {
    return { exception: 'No attachment -> no mail' };
  }

  const ud = await getUserData({ serviceId: 'userData.get', userId, parameters: params });
  if (isSqExceptionResult(ud)) {
    return ud;
  }

  const body = await createDocumentAnnotationMail(
    ud != null ? `${ud.firstName.toString()} ${ud.lastName.toString()} ` : '',
    reportFiles,
    comment
  );

  const mailTid = await newTid();
  const queryParams = {
    mailTid,
    recipient: userId,
    subject: 'iESP Documents',
    text: body,
    type: 'ReportMail',
    encryption: 'Y'
  };

  await sendMail(sc, queryParams, attachments);

  return {
    processInfo: {
      state: 'OK',
      lines: []
    }
  };

};

async function getAttachments(reportMailParamsArray: ReportMailParam[], userId: string, roles: string[]): Promise<{ attachments: DbFile[], reportFiles: DbFile[] }> {
  const attachments: DbFile[] = [];
  const reportFiles: DbFile[] = [];

  for (const { fileTid, withAnnotation } of reportMailParamsArray) {
    logger.info('ReportMailMA - userId: ' + userId + ', fileTid: ' + fileTid);
    const reportFile = await getDbFile(fileTid);
    if (!reportFile) {
      logger.error('file ' + fileTid + ' does not exists.', logger);
      continue;
    }
    if (!(await checkFileAccess(reportFile, { userId, roles }))) {
      const msg = `Permission denied for user: ${userId} to access the file: ${reportFile.directory}/${reportFile.filename}`;
      logger.warn(msg);
      continue;
    }
    reportFiles.push(reportFile);
    let attachedFile: AttachedFile = { ...reportFile, fileRel: 0 };

    if (withAnnotation === true || withAnnotation.toString() === 'true') {
      const annotationDir = createAnnotationDirectory(fileTid, userId);
      const annotatedFile = await getDbFileByPath(annotationDir, reportFile.filename);
      if (annotatedFile) {
        attachedFile = { ...annotatedFile, fileRel: ANNOTATED_MAILED };
        logger.info(` - userId: ${userId}, fileTid: ${fileTid} replaced by annotated file: ${annotatedFile.fileTid}`);
      } else {
        logger.info(` : no annotation found for ${reportFile}` + reportFile, logger);
      }
    } else {
      attachedFile.fileRel = ORIGINAL_MAILED;
    }
    attachments.push(attachedFile);
  }

  return { attachments, reportFiles };
}

async function sendMail(sc: ServiceCenter, queryParams: any, attachments: DbFile[]) {
  await sc.runSystemSq({ serviceId: MAIL_insertMail, parameters: queryParams });
  let indx = 1;
  for (const { fileTid } of attachments) {
    await sc.runSystemSq({ serviceId: MAIL_addAttachment, parameters: { ...queryParams, fileTid, indx } });
    indx++;
  }
}

async function createDocumentAnnotationMail(userName: string, files: DbFile[], comment: string) {
  const content = await getHtmlTemplateWithVariables(EMAIL_TEMPLATE, {
    userName: userName,
    mutlipleFiles: files?.length > 1,
    comment: comment,
  });

  return content;
}

async function getHtmlTemplateWithVariables(templatePath: string, variables: {[key: string]: any}): Promise<string> {
  console.log(process.cwd())
  let template = readFileSync(templatePath, 'utf-8');
  var asts = parse(template);
  const content = new Compile(asts).render(variables);
  return content;
}