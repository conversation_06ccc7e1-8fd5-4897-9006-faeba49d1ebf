import { test } from 'mocha';
import path from 'path';
import { localUrlNode, testdataDir } from '../common';
import axios from 'axios';
import { readFileSync } from 'fs';
import { expect } from 'chai';

const url = `${localUrlNode}serviceQuery/MeetingDocumentUpload`;

const meetingItemTid = '141298377';
const ord = '1';

const zurichsee = path.join(testdataDir, 'Zürichsee.pdf');
const testname = `(webtests) meeting-document-upload-test`;
test(testname, async () => {
  console.log(`Start ${testname}!`);
  const content = readFileSync(zurichsee);
  const blob = new Blob([content]);

  const headers = {
    'Content-Type': 'multipart/form-data; charset=utf-8'
  };
  const formData = new FormData();
  formData.append('documentFile', blob, 'Zürichsee.pdf'); // Replace with your file path
  formData.append('meetingItemTid', meetingItemTid);
  formData.append('ord', ord);

  try {
    const start = Date.now();
    const response = await axios.post(url, formData, { headers });
    console.log(`${testname} successful, time used: ${Date.now() - start}ms`);
    const exceptionMsg = response.data.exceptionMsg;
    if (exceptionMsg) {
      expect.fail(`Exception in ${testname}; ${exceptionMsg}`);
    }
    expect(!!response.data).true;
    expect(!!response.data.header).false;
  } catch (error) {
    console.error(error);
    expect.fail(`Error received from annotation upload ${error}`);
  }
});
