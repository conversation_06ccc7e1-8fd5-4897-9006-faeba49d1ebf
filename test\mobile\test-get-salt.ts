import { test } from 'mocha';
import { expect } from 'chai';

import { getTestSc } from '../init-local';
import { getUserSalt } from '../../src/service/lib/mobile/mobile-utils';
import { isSqExceptionResult } from '../../src/serviceQuery';

const testname = `(webtests) annotation-upload-test`;
test(testname, async () => {
  await getTestSc();
  const res = await getUserSalt('ABC001');
  if (isSqExceptionResult(res)) {
    expect.fail(`${res.exception}`);
    return;
  }
  expect(res.salt.length).gt(6);
});
