# This manifest configures the application deployment, for more information, visit the wiki:
# https://dev.azure.com/swissre/STRATUM/_wiki/wikis/Azure%20Application%20Hosting%20Platform/122960/Application-Manifest
apiVersion: deployment.stratum.swissre.com/v1alpha1
kind: ApplicationDeployment
metadata:
  name: "{{ .APMID }}-{{ .Stage }}-{{ .Component }}"
spec:
  # define where you application binaries are located.
  applicationImage: "{{ .Registry }}/{{ .APMID }}/{{ .Component }}{{ if .Tag }}:{{ .Tag }}{{ end }}{{ if .Digest }}@{{ .Digest }}{{ end }}"

  # swiss re application specific information
  swissre:
    apmid: "{{ .APMID }}"
    stage: "{{ .Stage }}"

  # define the number of instances deployed
  instances: 2

  # define the resource requirements
  resources:
    requests: # minimum (ensured)
      memory: "3Gi"
      cpu: "800m" # 0.8 CPU units
    limits: # maximum (allowed)
      memory: "6Gi"
      cpu: "1" # 1 CPU unit
    # define special hardware profile (currently only memory-optimized profile is supported!)
    # optional
    # default: mem
    profile: mem

  # define the health check and readiness endpoints
  # optional
  # healthCheck:
  #   livenessProbe:
  #     path: ":3000/"
  #   readinessProbe:
  #     path: ":3000/"

  # defines the HTTP port your app is listening on
  port: 8080

  # define the context root of your app
  # only supports one level
  # path: /{{ .Component }}
  path: /api

  # define application access properties
  # optional, defaults to aad-login
  gateway:
    accessPolicy: non-identity
    publicAccessAllowed: true

  # define environment variables that are passed to the app containers
  # optional
  env:
    - name: DB_TRX_USER
      secretRef: db-trx-username
    - name: DB_TRX_PASSWORD
      secretRef: db-trx-password
    - name: DB_HOST
      secretRef: db-host
    - name: DB_DATABASE
      secretRef: db-database
    - name: DB_PORT
      value: "5432"
    - name: SENDGRID_API_SECRET
      secretRef: sendgrid-api-secret
    - name: PORT
      value: "8080"
    - name: LOAD_SQ_SQL_FILES_AT_STARTUP
      value: "true"
    - name: CORS_WHITE_LIST
      value: https://esp.apps.swissre.com
    - name: LOG_LEVEL_INFO
      value: warning
    - name: OKTA_ISSUER
      value: https://identity.swissre.com/oauth2/ausdk5d7abHuKeRxr0i7
    - name: OKTA_AUDIENCE
      value: RtZUT6k9ciFJuNpoEAwv327Hrh8sjxyQgzG
    - name: TRUST_PROXY_NUMBER
      value: "1"
    - name: FROM_EMAIL_ADDRESS
      value: <EMAIL>

  # define command line arguments that are passed to the app containers
  # optional
  # args:
  #   # - myvalues # literal values
  #   # - $(TEST_ENV_1) # will be replaced by resp. environment value

  keyvault:
    # optional
    secrets:
      # secret name in the key vault
      - db-trx-password
      - db-trx-username
      - db-jdbc-url
      - db-host
      - db-database
      - sendgrid-api-secret
    # optional
    # certificates:
    #   # certificate name in the key vault
    # - test-certificate

  # define egress traffic allowing rules
  # optional
  egress:
    - matchPattern: "psp0001059.postgres.database.azure.com"
    - matchPattern: "identity.swissre.com"
    - matchPattern: "smtp.sendgrid.net"
    - matchPattern: "api.sendgrid.com"
    - matchPattern: "*.apps.swissre.com"
    - matchPattern: "proxy.we.swissre.com"
