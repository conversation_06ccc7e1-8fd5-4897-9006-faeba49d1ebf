
delete from ESP.T_Servicequeries  where key = 'FacetsBuid';

delete from ESP.T_Servicequeries  where key = 'FacetsBuid2';

--
-- LuceneSearch
--

delete from ESP.T_Servicequeries  where key = 'LuceneSearch';


delete from ESP.T_Servicequeries  where key = 'ESP.Lucene.Datalist';


delete from ESP.T_Servicequeries  where key = 'Lucene.getReportList';

--
--
-- ANNOTATION ENTRY FOR T_FILE_REL
--
--

delete from ESP.T_Servicequeries where key = 'select_missing_file_rel_annotation';
insert into ESP.T_Servicequeries 
(KEY, QUERY, TAGS,BU_ID,ACCESS_GROUPS,PARAM_TYPES) 
    values 
('select_missing_file_rel_annotation', 
'
select 
f.FILE_TID, d.FILE_TID as "TARGET_TID", d.DIRECTORY  as "TARGET_DIRECTORY", d.FILENAME as "TARGET_FILENAME"
from 
ESP.T_FILE d , ESP.T_FILE f  
where 
f.file_TID not in (select r.FILE_TID from ESP.T_FILE_REL r where r.TARGET_TID = d.FILE_TID)
and
d.directory like ''/.derived/%/%/'' 
and 
d.directory like  ''/.derived/'' || f.FILE_TID || ''/%''
',
'','','SYSTEM',NULL);

--
--
-- TT3 USER PREF
--
--

delete from ESP.T_Servicequeries where key = 'selectUserPref';
insert into ESP.T_Servicequeries 
(KEY, QUERY, TAGS,BU_ID,ACCESS_GROUPS,PARAM_TYPES) 
    values 
('selectUserPref', 
'set-if-empty:userId=%,userName=%,prefName=%;select 
p.USER_ID, 
coalesce(u.LAST_NAME, ''-na-'') || '', '' || coalesce(u.FIRST_NAME, ''-na-'') as USER_NAME
, 
p.PREF_NAME, p.PREF_VALUE
from ESP.T_USER_PREF p
left join ESP.V_SR_USER_ALL u on (p.USER_ID = u.USER_ID)
where upper(p.USER_ID) like upper(:userId)
AND upper(p.PREF_NAME) like upper(:prefName)
AND
(upper(u.FIRST_NAME) like upper(:userName) OR
upper(u.LAST_NAME) like upper(:userName) 
OR 
(u.FIRST_NAME is null and :userName = ''%''))
ORDER 
BY u.LAST_NAME, u.FIRST_NAME',
'','','ESP_ADMIN', NULL);


--
-- ENTRY FOR APP_PROPERTIES
--
                 
delete from ESP.T_APP_PROPERTIES
where
name = 'START_TIME';

insert into ESP.T_APP_PROPERTIES
(name, value)
values ('START_TIME', to_char(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI'));

delete from  ESP.T_APP_PROPERTIES
where name  = 'PDF_PREVIEW_RENDERER'
;


delete from  ESP.T_APP_PROPERTIES
where name  = 'IESP_USER_BASED_SERVICES'
;
insert into ESP.T_APP_PROPERTIES
(name, value)
values ('IESP_USER_BASED_SERVICES','v_iesp_prereadings_tree_documents,v_iesp_prereadings_tree_meetings,
v_iesp_prereadings_tree_reports,v_iesp_overview,v_iesp_exceptions');


delete from  ESP.T_APP_PROPERTIES
where name  = 'IESP_USER_BASED_SERVICES_REPORTS_ONLY'
;
insert into ESP.T_APP_PROPERTIES
(name, value)
values ('IESP_USER_BASED_SERVICES_REPORTS_ONLY','v_iesp_prereadings_tree_documents,v_iesp_prereadings_tree_meetings,
v_iesp_prereadings_tree_reports,v_iesp_overview,v_iesp_exceptions');


delete from  ESP.T_APP_PROPERTIES
where name  = 'meeting.disable'
;
insert into ESP.T_APP_PROPERTIES
(name, value)
values ('meeting.disable', 'test');
