delete from ESP.T_SERVICEQUERIES where key = 'serviceQueries.updateCacheMinutes';
insert into ESP.T_SERVICEQUERIES 
(KEY, 
QUERY, 
ACCESS_GROUPS) 
  values 
(
'serviceQueries.updateCacheMinutes', 
'set-if-empty:CACHE_MINUTES = 0;update ESP.T_SERVICEQUERIES set CACHE_MINUTES = :CACHE_MINUTES where KEY = :KEY', 
'SYSTEM'
);



delete from ESP.T_SERVICEQUERIES where key = 'SaveServicequery';
insert into ESP.T_SERVICEQUERIES 
(KEY, 
QUERY, 
ACCESS_GROUPS) 
  values 
(
'SaveServicequery', 
'delete from ESP.T_SERVICEQUERIES where KEY = :KEY;insert into ESP.T_SERVICEQUERIES (KEY, QUERY, TAGS, ACCESS_GROUPS) values (:KEY, :QUERY, :TAGS, :ACCESS_GROUPS);insert into ESP.T_SERVICEQUERIES_COUNTER select KEY, 0, null, null from ESP.T_SERVICEQUERIES where (KEY = :KEY ) and <PERSON>E<PERSON> not in (select i.KEY from ESP.T_SERVICEQUERIES_COUNTER i);update ESP.T_SERVICEQUERIES_COUNTER set LAST_LOAD = :$CURRENTMILLIS where KEY = :KEY;serviceId:serviceQueries.updateCacheMinutes',
'SYSTEM'
);



delete from ESP.T_SERVICEQUERIES where key = 'DeleteServicequery';
insert into ESP.T_SERVICEQUERIES 
(KEY, QUERY, ACCESS_GROUPS) 
  values 
('DeleteServicequery', 'delete from ESP.T_SERVICEQUERIES where KEY = :KEY', 'SYSTEM');



delete from ESP.T_SERVICEQUERIES where key = 'LoadSqSqlFiles';
insert into ESP.T_SERVICEQUERIES 
(KEY, QUERY, ACCESS_GROUPS) 
  values 
(
'LoadSqSqlFiles', 'java:com.swissre.serviceQuery.lib.LoadSqSqlFiles', 'ESP_ADMIN'
);

