--
-- KEY           = esp.fcr.sourceUpload
-- ACCESS_GROUPS = ESP_FCR_ADMIN,ESP_ADMIN
--

set:processServiceId=esp.fcr.unzip
;
java:com.swissre.serviceQuery.lib.UploadFile
;


--
-- KEY           = esp.fcr.sourceUpload.removeOldFiles
-- ACCESS_GROUPS = SYSTEM
--

delete from  ESP.T_FILE
where
DIRECTORY like '/web/fcrUpload/' || :dirPart
;


--
-- KEY           = esp.fcr.unzip
-- ACCESS_GROUPS = SYSTEM
--

set:directoryPrefix=/web/fcrUpload/
;
java:com.swissre.serviceQuery.lib.UnZip



--
-- KEY           = esp.fcr.select
-- xACCESS_GROUPS = ESP_FCR_READER,ESP_FCR_ADMIN
--

select 
FILE_TID, FILENAME, DIRECTORY 
from ESP.T_FILE 
where
(TAGS <> '_READY_FOR_DELETION_' or TAGS is null)
and
FILENAME = :filename 
and 
DIRECTORY = '/web/fcrUpload' || :directory



--
-- KEY           = esp.webSite.files
-- ACCESS_GROUPS = SYSTEM
--

select
    FILE_TID,
    CREATION_TIMESTAMP,
    OWNER,
    DIRECTORY,
    FILENAME,
    TAGS,
    LENGTH (DOCUMENT) as "LENGTH"
FROM ESP.T_FILE f1
where
DIRECTORY like '/web/fcrUpload/%'
;

--
-- KEY           = esp.support.accessFiles
-- ACCESS_GROUPS = ESP_SUPPORT
--

select 
FILE_TID, 
CREATION_TIMESTAMP, 
OWNER, 
DIRECTORY,
FILENAME, 
TAGS,
LENGTH (DOCUMENT) as "LENGTH"
FROM ESP.T_FILE
where
DIRECTORY like '/var/m.crashlog/%'


