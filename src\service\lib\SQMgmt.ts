import ServiceCenter from '../ServiceCenter';
import { ServiceFunction, toFirst } from '../../serviceQuery';
import { encodeToBase64 } from 'pdf-lib';
import { toSqRecord } from '../../serviceQuery/utils';

type SQEntry = {
  key: string;
  query: string;
};
export const SQGetProtected: ServiceFunction = async({ request }) => {
  const sc = ServiceCenter.getInstance();
  const res = await sc.runSystemSq({ serviceId: 'SQ.get', parameters: { key: request.parameters.key } });
  const sq = toFirst<SQEntry>(res);
  sq.query = encodeToBase64(Buffer.from(sq.query, 'utf8'));
  return toSqRecord([sq]);
};
