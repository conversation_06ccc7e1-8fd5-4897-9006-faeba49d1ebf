/* eslint-disable no-console,space-before-function-paren */
import { test } from 'mocha';
import { localUrlNode } from '../common';
import axios from 'axios';
import { expect } from 'chai';

const setReadFlagUrl = `${localUrlNode}serviceQuery/SetReadFlag`;

const DOCUMENT_FILE_TID = 141941373;

const testname = '(webtests) annotation-upload-test';
test(testname, async () => {
  return Promise.all([doTest(), doTest()]);
});

async function doTest() {
  const headers = {
    'Content-Type': 'multipart/form-data'
  };
  const formData = new FormData();
  formData.append('setUnread', 'true');
  formData.append('documentFileTidList', DOCUMENT_FILE_TID.toString());

  const start = Date.now();
  const response = await axios.post(setReadFlagUrl, formData, { headers });
  console.log(`${testname} successful, time used: ${Date.now() - start}ms`);
  console.debug(`try to set unread ${DOCUMENT_FILE_TID} with ${setReadFlagUrl}`);
  expect(!!response.data).true;
  expect(!!response.data.name).true;
  expect(response.data.name).equals('SetReadFlag');
}
