--
-- KEY           = m.UserDeviceStatus.select
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

set-if-empty:userId=%
;
select USER_ID,
       STATUS,
       MESSAGE
from ESP.T_USER_DEVICE_STATUS
where USER_ID like :userId
   or USER_ID is null
order by USER_ID
;


--
-- KEY           = m.UserDeviceStatus.insert
-- ACCESS_GROUPS = ESP_ADMIN
--

insert into ESP.T_USER_DEVICE_STATUS
( 
 USER_ID, 
 STATUS, 
 MESSAGE)
values
(
:userId, 
  :status, 
  :message)


  
--
-- KEY           = m.UserDeviceStatus.get
-- ACCESS_GROUPS = ESP_ADMIN
--

select 
 USER_ID, 
 STATUS, 
 MESSAGE  
from ESP.T_USER_DEVICE_STATUS
where 
USER_ID = :userId



--
-- KEY           = m.UserDeviceStatus.delete
-- ACCESS_GROUPS = ESP_ADMIN
--

delete from ESP.T_USER_DEVICE_STATUS
where 
USER_ID = :userId
  


--
-- KEY           = m.UserDeviceStatus.update
-- ACCESS_GROUPS = ESP_ADMIN
--

update ESP.T_USER_DEVICE_STATUS
set
 USER_ID = :userId, 
 STATUS = :status, 
 MESSAGE = :message
where 
USER_ID = :userId
