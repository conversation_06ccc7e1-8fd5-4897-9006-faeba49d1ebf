import OktaJwtVerifier from '@okta/jwt-verifier';

const oktaJwtVerifier = new OktaJwtVerifier({
    issuer: 'https://identity-np.swissre.com/oauth2/aus231pr469i7v1PD0h8'
  });
const oktaAudience = 'fnwuQ6VcgH9kxejT5BCp4hGLsWMK7i';

test('should correctly resolve the token from the Authorization header', async() => {
  const token = 'eyJraWQiOiJkWnoxbHFNMkZMMGE1OWVqbVpIWnY1UjI4eTBrU21kb1A5bzVzbGhPN0lJIiwiYWxnIjoiUlMyNTYifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dVEdBLJN7y_PLIv5_7yFtIm_xkFaa5lUXue9pAORZAG2uR44aoe13PmlLoEyiTfPLes2jOwVRx6fDuG_obbWOHnegHaeUJEvdRWOSddcPt5cUaDbX4h4VdVOLlZR7BdswEidYmVounI8oaItuXOO_BHk8vuzpcs5OGdv2qb8F9A_ug6ZO0iMuKS3-Ve3maP3I5TxWO3UzYttiYupiP4bJmM8DHZznmX740DR5obC_IPYFUNb0SXle1Q2Qgl6PD8aRKXwQ-wt-X03gbMxb_iZk92kwgDNPMvEkPH379knJU96gDvEsAYVc2hjdGR6Ek75vmtA-WNoyls_FSCSs1igWw';
  const oktaJwt = await oktaJwtVerifier.verifyAccessToken(token, oktaAudience);
  // console.info(oktaJwt);
  expect(oktaJwt.claims['swissreuid']).toBe('S6XPPC');
});