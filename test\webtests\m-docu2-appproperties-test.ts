import { test } from 'mocha';
import { localUrlJava, localUrlNode } from '../common';
import { runGetBlob } from './getkeys2-utils';
import { expect } from 'chai';
import { expectBufferEquals } from '../test-utils';
import { decrypt } from '../../src/service/lib/mobile/crypto-utils';

const docu2UrlNode = `${localUrlNode}mobile/docu2/`;
const docu2UrlJava = `${localUrlJava}mobile/docu2/`;
const fileTid = 141745973;

const testname = `(webtests) m-docu2-single-test`;
test(testname, async () => {
  console.log(`Start ${testname} get: ${fileTid}!`);

  const docu2UrlFileTidListNode = `${docu2UrlNode}?fileTidList=${fileTid}`;
  const docu2ResultNode = await runGetBlob(docu2UrlFileTidListNode, '');
  const lenNode = docu2ResultNode.length;
  console.debug(`len node: ${lenNode}`);
  expect(lenNode).gt(10, `Node failed for ${fileTid}!`);

  const docu2UrlFileTidListJava = `${docu2UrlJava}?fileTidList=${fileTid}`;
  const docu2ResultJava = await runGetBlob(docu2UrlFileTidListJava, '');
  const lenJava = docu2ResultJava.length;
  console.debug(`len java: ${lenJava}`);
  expect(lenJava).gt(10, `Java failed for ${fileTid}!`);

  console.debug(`len node: ${lenNode}`);
  expect(lenNode).gt(10, `Node failed for ${fileTid}!`);

  expect(lenNode).equals(lenJava, `Length of ${fileTid} result is not the same!`);

  expectBufferEquals(docu2ResultNode, docu2ResultJava);

  pp(docu2ResultNode, 'node');
  pp(docu2ResultJava, 'java');
});

function pp(b: Buffer, label = 'node') {
  const en =
    'ZUs2GTnaFilMKmdENMZaFqqexITduUOx9VGAJWT5lHBNJyUg0ApfvCaBUwLfCAEBvdQUNiVpUqNX17aj8OwlyzyExgM9HxKLmYy1draqa6aH2o86hXLf1/GyRFy2Ss4DKyviLEy1TZQdFHzimd7ghw5CZe1pMzCGAsM/Z4JKHxlwjU0vYUudbrTLUhYXwxuSUsZt3MIGqQT46ApxgrauqSODolGTbEccPNAV4eXvivz+euAa3YLsmvQ2ZHAGbpysuBHJAh7Mv9pYhlXwIdkJZxEcgmw1aY//19FzRL1HZm4n9qDfijGcte1kQ4m4jLY8Dif1/7xHypgzo6SBNX2eS5rRW8raa7egBUkyWxLCQm6HHTTqBKOGmG2xCqZUcQtLP7d8BpC01Sd+/IJ1x2zAEYvrwu20Fwohvfd8pn3nakX32323FcyMYhlDreyky2gjic0KRRPDeKXn5ENrekK8XfEh43ECCb+TGf12cuUJcTkWYqLnTg4paA1bzv3KGNEl';
  const key = 'f4d201a82c9bcf9d0140f8a84b11c6f9';

  const buff = Buffer.from(en, 'base64');
  const decbuff2 = decrypt(key, buff);

  const scontent = b.toString('utf8');
  console.log(`starting with ${label} : ` + scontent.substring(0, 30));
  const endMarker = ',-1:-1,';
  const markerIndex = scontent.indexOf(endMarker);
  console.log('markerIndex: ' + markerIndex);
  console.log('delimiter', scontent.substring(markerIndex, markerIndex + endMarker.length));
  console.log('delimiter', scontent.substring(markerIndex, markerIndex + endMarker.length));
  const fileContent = b.subarray(markerIndex + endMarker.length);
  console.log('file content length: ' + fileContent.length);
  const len = +scontent.substring(0, markerIndex).split(':')[1];
  console.log(`len ${len}`);
  // const encContent = b.subarray(endIndex + endMarker.length, endIndex + endMarker.length + +len);

  const startb = markerIndex + endMarker.length;
  const endb = markerIndex + endMarker.length + len;
  console.log(`startb: ${startb}, endb: ${endb}`);
  const encContent = b.subarray(startb, endb);
  console.log(encContent.length);

  expect(buff.length).equals(encContent.length);
  expectBufferEquals(encContent, buff);

  const decbuff = decrypt('f4d201a82c9bcf9d0140f8a84b11c6f9', encContent);

  expect(decbuff[1]).equals(decbuff2[1]);

  console.log(decbuff.toString('utf8'));
}
