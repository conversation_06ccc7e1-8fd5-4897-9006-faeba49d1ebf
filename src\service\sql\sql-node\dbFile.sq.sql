--
-- SERVICE_ID = dbFile.insert
-- ROLES      = SYSTEM
--

insert into esp.t_file
(file_tid,
 creation_timestamp,
 owner,
 directory,
 filename,
 tags)
values (:fileTid,
        current_timestamp,
        :owner,
        :directory,
        :filename,
        :tags)
;

--
-- SERVICE_ID = dbFile.update
-- ROLES      = SYSTEM
--

update esp.t_file
set filename           = :filename,
    directory          = :directory,
    creation_timestamp = current_timestamp,
    owner              = :owner,
    tags               = :tags
where file_tid = :fileTid
;

--
-- SERVICE_ID = dbFile.get
-- ROLES      = ESP_ADMIN,SYSTEM
--

select file_tid
     , filename
     , directory
     , owner
     , to_char(creation_timestamp, 'yyyy-mm-dd hh24:mi') as creation_timestamp
     , to_char(deletion_timestamp, 'yyyy-mm-dd hh24:mi') as deletion_timestamp
     , length(document)                                  as length
     , tags
     , real_length
from esp.t_file
where file_tid = :fileTid
;


--
-- SERVICE_ID = dbFile.getByDirectoryAndFilename
-- ROLES      = SYSTEM
--

select max(file_tid)
from esp.t_file
where directory=:directory and filename=:filename
;


--
-- SERVICE_ID = dbFile.listAllFiles
-- ROLES      = SYSTEM
--

select file_tid, creation_timestamp, owner, directory, filename, tags, length(document)
from esp.t_file
where directory like :directory
order by directory, filename
;


--
-- SERVICE_ID = dbFile.listFilesDeep
-- ROLES      = SYSTEM
--

select f.file_tid, f.creation_timestamp, f.owner, f.directory, f.filename, f.tags, length(f.document)
from esp.t_file f
where file_tid
          in
      (select max(i.file_tid)
       from esp.t_file i
       where i.directory like :directory
       group by i.filename, i.directory)
;



--
-- SERVICE_ID = dbFile.selectMissingRealLength
-- ROLES      = SYSTEM
--

select FILE_TID, FILENAME, DIRECTORY from ESP.T_FILE
where
    REAL_LENGTH is null
  and directory not like '/_temp_/%'
;


--
-- SERVICE_ID = dbFile.updateRealLength
-- ROLES      = SYSTEM
--

update ESP.T_FILE
set
    REAL_LENGTH = :realLength
where
    FILE_TID = :fileTid
;

--
--
-- SERVICE_ID = dbFile.updateRealLength.dbside
-- ROLES      = SYSTEM,ESP_ADMIN
--

update ESP.T_FILE
set
    REAL_LENGTH = LENGTH(pkg_crypto.decrypt_blob(DOCUMENT))
where
    FILE_TID = :fileTid
;


--
--
-- SERVICE_ID = dbFile.getRealLength
-- ROLES      = SYSTEM
--

select REAL_LENGTH from ESP.T_FILE where FILE_TID = :fileTid
;


--
--
-- SERVICE_ID = dbFile.selectMissingPreview
-- ROLES      = SYSTEM
--

select f.file_tid, f.filename, f.directory
from esp.t_file f
         left join esp.t_file_rel r on r.file_tid = f.file_tid and r.rel_tid = -103
         left join esp.t_file im on im.file_tid = r.target_tid
where (f.directory like '/mp/%' or f.directory like '/.derived/%' or f.directory like '/home/<USER>')
  and (f.filename like '%.pdf' or f.filename like '*.PDF')
  and (r.file_tid is null or im.file_tid is null)
;

--
-- SERVICE_ID   = dbFile.setDeletionDate
-- ROLES        = SYSTEM
--

UPDATE ESP.T_FILE
SET DELETION_TIMESTAMP = current_date + cast(:days as integer)
WHERE FILE_TID = :fileTid
;
