--
-- SERVICE_ID = task.add
-- ROLES      = SYSTEM
--

insert into esp.t_mp_task (file_tid, user_id, meeting_item_tid, type, status, msg)
values (:fileTid, :userId, :meetingItemTid, :type, 'progressing', :msg)
;

--
-- SERVICE_ID = task.update
-- ROLES      = SYSTEM
--

update esp.t_mp_task
set progress            = :progress
    , status            = :status
    , msg               = :msg
where file_tid = :fileTid
;

--
-- SERVICE_ID = task.check
--

select ta.*, tf.filename  from esp.t_mp_task ta
join esp.t_file tf on ta.file_tid = tf.file_tid 
where user_id = :$USERID

--
-- SERVICE_ID = task.refresh
--

WITH updated AS (
  UPDATE esp.t_mp_task
  SET status = 'finished'
  WHERE user_id = :$USERID AND status = 'progressing' AND progress = 100
  RETURNING *
)
SELECT ta.*, tf.filename
FROM updated ta
JOIN esp.t_file tf ON ta.file_tid = tf.file_tid;