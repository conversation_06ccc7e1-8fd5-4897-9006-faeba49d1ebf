import { SqContext, sqExceptionResult, SqRequest, SqResult } from '../../../serviceQuery';
import ServiceCenter from '../../ServiceCenter';
import { getDbFile } from '../upload-file-utils';
import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';
import { SYSTEM } from '../../constants';
import { DbFile } from './types';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export async function UploadFile({
  request: { parameters, userId, roles }
}: {
  request: SqRequest;
  context: SqContext;
}): Promise<SqResult> {
  const sq = ServiceCenter.getInstance().getSq();
  const header: string[] = ['filename', 'fileTid', 'location'];
  const table: string[][] = [];

  roles = roles || [];

  const fileTids = extractFileTids(parameters);
  const processServiceId = (parameters['processServiceId'] || '').toString();

  if (fileTids.length === 0) {
    return sqExceptionResult(`No upload file provided! ${JSON.stringify(parameters)}`);
  }

  for (const fileTid of fileTids) {
    const dbFile = await getDbFile(fileTid.toString());
    if (!dbFile) {
      logger.error(`File not found for ${fileTid} - serious error!`);
      continue;
    }

    await processFile(sq, processServiceId, parameters, dbFile, userId, roles);
    addFileToTable(table, dbFile, fileTid);
  }

  return { header, table };
}

const extractFileTids = (parameters: Record<string, any>): number[] => {
  const fileTids: number[] = [];
  const fileTidString = +parameters['fileTid'];

  Object.keys(parameters).forEach((name) => {
    if (/^UPLOADFILE\d+$/.test(name)) {
      const fileTid: number = +parameters[name];
      if (fileTid) {
        fileTids.push(fileTid);
      }
    }
  });

  if (fileTidString && fileTids.length === 0) {
    fileTids.push(fileTidString);
  }

  return fileTids;
};

const processFile = async (
  sq: any,
  processServiceId: string,
  parameters: Record<string, any>,
  dbFile: DbFile,
  userId: string,
  roles: string[]
) => {
  if (processServiceId) {
    await sq.run({
      serviceId: processServiceId,
      parameters: { ...parameters, ...dbFile },
      userId,
      roles: [...roles, SYSTEM]
    });
  }
};

const addFileToTable = (table: string[][], dbFile: DbFile, fileTid: number) => {
  const filename = dbFile.filename || '';
  const location = `dbfile\\${filename}`;
  table.push([filename, fileTid.toString(), location]);
};