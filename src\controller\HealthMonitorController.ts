import * as express from 'express';
import { Request, Response } from 'express';
import { Controller } from '../types';
import { HealthMonitorService } from '../service/lib/HealthMonitorService';
import { AutoRestartManager } from '../service/lib/AutoRestartManager';
import LoggerCenter from '../logger/LoggerCenter';
import path from 'path';

const CONTROLLER_NAME = 'health-monitor';
const logger = LoggerCenter.getLogger(path.basename(__filename));

export default class HealthMonitorController implements Controller {
  public readonly name = CONTROLLER_NAME;
  public readonly paths = [`/api/v1/${CONTROLLER_NAME}`];
  public readonly router = express.Router();

  constructor() {
    this.router.get('/status', this.getStatus);
    this.router.post('/start', this.startMonitoring);
    this.router.post('/stop', this.stopMonitoring);
    this.router.post('/restart', this.triggerRestart);
    this.router.post('/reset-attempts', this.resetRestartAttempts);
    this.router.put('/config', this.updateConfig);
  }

  private readonly getStatus = (_req: Request, res: Response) => {
    try {
      const healthMonitor = HealthMonitorService.getInstance();
      const autoRestart = AutoRestartManager.getInstance();
      
      const status = {
        healthMonitor: healthMonitor.getStatus(),
        autoRestart: autoRestart.getStatus(),
        timestamp: new Date().toISOString()
      };
      
      res.json(status);
    } catch (error) {
      logger.error('Error getting health monitor status:', error);
      res.status(500).json({ error: 'Failed to get status' });
    }
  };

  private readonly startMonitoring = (_req: Request, res: Response) => {
    try {
      const healthMonitor = HealthMonitorService.getInstance();
      healthMonitor.start();
      
      logger.info('Health monitoring started via API');
      res.json({ message: 'Health monitoring started', timestamp: new Date().toISOString() });
    } catch (error) {
      logger.error('Error starting health monitoring:', error);
      res.status(500).json({ error: 'Failed to start monitoring' });
    }
  };

  private readonly stopMonitoring = (_req: Request, res: Response) => {
    try {
      const healthMonitor = HealthMonitorService.getInstance();
      healthMonitor.stop();
      
      logger.info('Health monitoring stopped via API');
      res.json({ message: 'Health monitoring stopped', timestamp: new Date().toISOString() });
    } catch (error) {
      logger.error('Error stopping health monitoring:', error);
      res.status(500).json({ error: 'Failed to stop monitoring' });
    }
  };

  private readonly triggerRestart = async (req: Request, res: Response) => {
    try {
      const { reason } = req.body;
      const restartReason = reason || 'Manual restart via API';
      
      const autoRestart = AutoRestartManager.getInstance();
      
      logger.warn(`Manual restart triggered via API. Reason: ${restartReason}`);
      res.json({ 
        message: 'Restart initiated', 
        reason: restartReason,
        timestamp: new Date().toISOString() 
      });
      
      // Initiate restart after sending response
      setTimeout(() => {
        autoRestart.initiateRestart(restartReason);
      }, 1000);
      
    } catch (error) {
      logger.error('Error triggering restart:', error);
      res.status(500).json({ error: 'Failed to trigger restart' });
    }
  };

  private readonly resetRestartAttempts = (_req: Request, res: Response) => {
    try {
      const autoRestart = AutoRestartManager.getInstance();
      autoRestart.resetRestartAttempts();
      
      logger.info('Restart attempts counter reset via API');
      res.json({ message: 'Restart attempts counter reset', timestamp: new Date().toISOString() });
    } catch (error) {
      logger.error('Error resetting restart attempts:', error);
      res.status(500).json({ error: 'Failed to reset restart attempts' });
    }
  };

  private readonly updateConfig = (req: Request, res: Response) => {
    try {
      const { config } = req.body;
      
      if (!config || typeof config !== 'object') {
        return res.status(400).json({ error: 'Invalid config provided' });
      }
      
      const healthMonitor = HealthMonitorService.getInstance();
      healthMonitor.updateConfig(config);
      
      logger.info('Health monitor config updated via API:', config);
      res.json({ 
        message: 'Configuration updated', 
        config,
        timestamp: new Date().toISOString() 
      });
    } catch (error) {
      logger.error('Error updating config:', error);
      res.status(500).json({ error: 'Failed to update configuration' });
    }
  };
}
