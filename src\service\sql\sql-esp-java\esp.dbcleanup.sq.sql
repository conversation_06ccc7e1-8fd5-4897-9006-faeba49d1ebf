--
-- KEY           = esp.dbcleanup.all
-- ACCESS_GROUPS = ESP_ADMIN_MGMT
--

include:esp.dbcleanup.oldstuff
;
include:esp.dbcleanup.orphans
;


--
-- KEY           = esp.dbcleanup.orphans
-- ACCESS_GROUPS = ESP_ADMIN_MGMT
--

set-if-empty:directory=derived
;
delete
from esp.t_file
where file_tid in
      (select f1.file_tid
       from (SELECT file_tid,
                    creation_timestamp,
                    filename,
                    directory,
                    cast(SUBSTRING(directory FROM '/\.' || :directory || '/(\d+)/*') as INTEGER) as id
             FROM esp.t_file
             where directory like '/.' || :directory || '/%') f1
                left join esp.t_file f2 on f2.file_tid = f1.id
       where f2.file_tid is null);




--
-- KEY           = esp.dbcleanup.oldstuff
-- ACCESS_GROUPS = ESP_ADMIN_MGMT
-- Cleanup Log from https://esp-prod.apps.swissre.com
--

delete
from esp.t_file
where directory like '/_temp_/%'
   or directory like '/temp/%'
;
delete
from esp.t_file
where directory like '/ESP.T_UPLOAD_INSTANCE/%'
;
delete
from esp.t_file
where directory like '/TRASH/%'
;
delete
from esp.t_file
where directory = '/var/mail/spooler/'
;
delete
from esp.t_file
where directory like '/.trash/%'
;
delete
from esp.t_file
where directory like '/var/crashlog/%'
;
delete
from esp.t_file
where directory like '/hot/libs/%'
   or directory like '/hot/js/%'
   or directory like '/hot/css/%'
   or directory like '/hot/img/%'
;
delete
from esp.t_file
where directory like '/data/%'
;
delete
from esp.t_file
where directory like '/getReport/%'
;
delete
from esp.t_file
where directory like '/var/uidefs/%'
   or directory like '/var/lucene/%'
   or directory like '/var/admin/%'
   or directory like '/var/processes/%'
;
delete
from esp.t_file
where directory like '/var/definitions/%'
   or directory like '/esp/la/definitions/%'
   or directory like '/processInfoTemp/%';
select distinct directory
from esp.t_file
where not directory like '/mp/%'
  and not directory like '/.derived/%'
  and not directory like '/_transfer/%'
  and not directory like '/home/<USER>'
  and not directory like '/var/m.crashlog/%'
  and not directory like '/var/misc/%'
  and not directory like '/m.transfer/%'
  and not directory like '/hot/%'
  and not directory = '/web/images/'
;
-- now only return now /daily_temp/ !



--
-- KEY           = esp.dbcleanup.stats
-- ACCESS_GROUPS = ESP_ADMIN_MGMT,ESP_ADMIN
--
select *
from (select count(*), '/mp/%' where_filter
      from esp.t_file
      where directory like '/mp/%'
      union

      select count(*), '/.derived/%' where_filter
      from esp.t_file
      where directory like '/.derived/%'
      union

      select count(*), '/home/<USER>' where_filter
      from esp.t_file
      where directory like '/home/<USER>'
      union

      select count(*), '/var/m.crashlog/%' where_filter
      from esp.t_file
      where directory like '/var/m.crashlog/%'
      union

      select count(*), '/_transfer/%' where_filter
      from esp.t_file
      where directory like '/_transfer/%'
      union

      select count(*), '/m.transfer/%' where_filter
      from esp.t_file
      where directory like '/m.transfer/%'
      union

      select count(*), '/hot/%' where_filter
      from esp.t_file
      where directory like '/hot/%'
      union


      select count(*), '/var/misc/%' where_filter
      from esp.t_file
      where directory like '/var/misc/%'
      union


      select count(*), '/web/images/' where_filter
      from esp.t_file
      where directory like '/web/images/'
      union

      select count(*), '%' where_filter
      from esp.t_file) t
order by 1 desc
;



-- count,where_filter
-- 93034,%
-- 84597,/mp/%
-- 4123,/.derived/%
-- 3498,/_transfer/%
-- 572,/m.transfer/%
-- 214,/var/m.crashlog/%
-- 16,/hot/%
-- 13,/web/images/
-- 0,/var/misc/%
-- 0,/home/<USER>


-- count,where_filter
-- 93036,%
-- 84598,/mp/%
-- 4124,/.derived/%
-- 3498,/_transfer/%
-- 572,/m.transfer/%
-- 214,/var/m.crashlog/%
-- 16,/hot/%
-- 13,/web/images/
-- 0,/var/misc/%
-- 0,/home/<USER>

--


-- select * from esp.t_file where directory like '%/_transfer/%' or  directory like '%/m.transfer/%'     order by file_tid desc ;

;
