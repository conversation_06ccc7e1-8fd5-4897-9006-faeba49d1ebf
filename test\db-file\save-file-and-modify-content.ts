import { endTest, getTestSc, getTestSq } from '../init-local';
import { expect } from 'chai';
import LoggerCenter from '../../src/logger/LoggerCenter';
import path from 'path';
import { PSQLDriverExtension } from '../../src/serviceQuery/PSQLDriver';
import { getDbFile, getDbFileContent, saveDbFile } from '../../src/service/lib/upload-file-utils';
import { testdataDir } from '../common';
import { readFileSync } from 'fs';
import { isSqExceptionResult, singleValue } from '../../src/serviceQuery';
import moment from 'moment';
import { before } from 'mocha';

const logger = LoggerCenter.getLogger(path.basename(__filename));
const cabcomFile = path.join(testdataDir, 'cabcom.zip');

const DESCR = 'save-file-to-db 02 - Update Content';

const mContent = 'Hello World';
const mTags = 'PROTECTED';
const mFilename = 'cabcom.json';
const mDirectory = '/daily_temp/';
const mOwner = 'Nobody';

describe(DESCR, async () => {
  logger.info(`Start ${DESCR}`);
  let fileTid = '';

  before(() => {
    getTestSc();
  });

  after(async () => {
    const pgDriver = getTestSq().driver as PSQLDriverExtension;
    if (fileTid) {
      await pgDriver.processSql('delete from esp.t_file where file_tid = :fileTid', { fileTid });
    }
    endTest();
  });

  it(`save new file`, async () => {
    const zipContent = readFileSync(cabcomFile);
    const newFile = { filename: 'cabcom.zip', directory: '/daily_temp/', owner: 'TESTER' };

    const res = await saveDbFile({ ...newFile, data: zipContent });
    if (isSqExceptionResult(res)) {
      expect.fail(`Saving new file failed: ${res.exception}`);
    } else {
      fileTid = res.fileTid;
      expect(fileTid).is.not.empty;
    }
  });

  it(`update content file`, async () => {
    const dbFile = {
      fileTid,
      filename: mFilename,
      directory: mDirectory,
      owner: mOwner,
      tags: mTags,
      data: Buffer.from(mContent)
    };
    const res = await saveDbFile(dbFile);
    if (isSqExceptionResult(res)) {
      expect.fail(`Updating file failed: ${res.exception}`);
    } else {
      fileTid = res.fileTid;
      expect(fileTid).is.not.empty;
    }
  });

  it(`check changed content`, async () => {
    const res = await getDbFileContent(fileTid);
    if (isSqExceptionResult(res)) {
      expect.fail(`Getting file content failed: ${res.exception}`);
    } else {
      const s = res.toString();
      expect(s).is.equals(mContent);
    }
  });

  it(`check changed attributes`, async () => {
    const res = await getDbFile(fileTid);
    if (isSqExceptionResult(res)) {
      expect.fail(`Getting file content failed: ${res.exception}`);
    } else {
      expect(res.filename).is.equals(mFilename);
      expect(res.directory).is.equals(mDirectory);
      expect(res.owner).is.equals(mOwner);
      expect(res.tags).is.equals(mTags);
    }
  });

  it('test-insert-dummy-file', async () => {
    const newFile = { filename: 'test-dummy', directory: '/daily_temp/', owner: 'TESTER' };

    const res = await saveDbFile({ ...newFile, data: 'test' });
    if (isSqExceptionResult(res)) {
      expect.fail(`Saving new file failed: ${res.exception}`);
    } else {
      fileTid = res.fileTid;
      expect(fileTid).is.not.empty;
    }
    const pgDriver = getTestSq().driver as PSQLDriverExtension;
    if (fileTid) {
      const r = await pgDriver.processSql('select creation_timestamp from esp.t_file where file_tid = :fileTid', {
        fileTid
      });
      const dateStr = singleValue(r);
      console.log(`timestamp from db  : ${dateStr}`);
      console.log(`timestamp new Date : ${new Date(dateStr)}`);
      const m = moment(new Date(dateStr));
      console.log(`timestamp moment   : ${m.format('YYYY-MM-DD HH:mm')}`);

      const r2 = await pgDriver.processSql(
        'select EXTRACT(EPOCH FROM creation_timestamp) * 1000 AS unix_milliseconds from esp.t_file where file_tid = :fileTid',
        {
          fileTid
        }
      );
      const dateStr2 = singleValue(r2);
      const now = Date.now();
      console.log(`diff seconds 1     : ${now - new Date(dateStr).getTime()}`);
      console.log(`diff seconds 2     : ${now - +dateStr2}`);
    }
  });
});
