/* eslint-disable @typescript-eslint/no-explicit-any,@typescript-eslint/explicit-module-boundary-types */

import {
  AddServiceArgs,
  isError,
  isSqExceptionR<PERSON>ult,
  SqCommandsType,
  SqContext,
  SqDriver,
  sqExceptionR<PERSON>ult,
  SqExceptionR<PERSON>ult,
  SqFunction<PERSON>rg,
  SqNodeFunction,
  SqRequest,
  SqResult,
  SqServiceEntry,
  SqStatementNode,
  toFirst
} from './serviceQuery-common';
import { deepClone, identCommand, isEmpty, resolveValue, texting, tokenize } from './utils';
import LoggerCenter from '../logger/LoggerCenter';
import path from 'path';
import { cacheSQList, enableSqCache, refreshCachesList } from './cacheUtils';

// eslint-disable-next-line @typescript-eslint/no-var-requires
const NodeCache = require('node-cache');

const nodeCache = new NodeCache({ stdTTL: 60, checkperiod: 120 });

const ANONYMOUS = 'ANONYMOUS';
const MAX_RECURSION = 40;
const MAX_INCLUDES = 100;
let CONTEXT_COUNTER = 1;

type StatementPreprocessor = (statements: string) => string;
const logger = LoggerCenter.getLogger(path.basename(__filename));

export interface IServiceQuery {
  addService: (serviceEntry: AddServiceArgs) => void;

  setStatementsPreprocessor: (statementPreprocessor: StatementPreprocessor) => void;

  processStatements(sqlStatements: string[]): Promise<SqResult[]>;

  registerNode: (name: string, fun: SqNodeFunction) => void;
  registerCommand: (name: string, fun: SqNodeFunction) => void;

  driver: SqDriver;

  run: (request: SqRequest) => Promise<SqResult>;
}

export class ServiceQuery implements IServiceQuery {
  public driver: SqDriver;

  private statementsPreprocessor: StatementPreprocessor = (s) => s;
  private ignoredErrors: string[] = [];

  private directServices: Record<string, SqServiceEntry> = {};

  private commands: SqCommandsType = {
    StartBlock: {
      if: true,
      'if-empty': true
    },
    EndBlock: {},
    Registry: {},
    Node: {}
  };

  constructor(driver: SqDriver) {
    this.driver = driver;
    this.commands.Registry.serviceRoot = this.serviceRootCommand;
    this.commands.Registry.sql = this.sqlCommand;
    this.commands.Registry.set = this.setCommand;
    this.commands.Registry['set-if-empty'] = this.setCommand;
    this.commands.Registry.copy = this.setCommand;
    this.commands.Registry['copy-if-empty'] = this.setCommand;
    this.commands.Registry.serviceId = this.serviceIdCommand;
    this.commands.Registry.parameters = this.parametersCommand;
    this.commands.Registry['parameters-if-empty'] = this.parametersCommand;
    this.commands.Registry['if-empty'] = this.ifCommand;
    this.commands.Registry.comment = this.commentCommand;
    this.commands.Registry.node = this.nodeCommand;
    this.commands.Registry.java = this.nodeCommand;
    this.commands.Registry.default = identCommand;
    this.commands.Registry.include = identCommand;
  }

  public registerNode(name: string, fun: SqNodeFunction) {
    this.commands.Node[name] = fun;
  }

  public registerCommand(name: string, fun: SqNodeFunction) {
    this.commands.Registry[name] = fun;
  }

  public addService({ serviceId, statements = '', serviceFunction, roles = [], tags = new Set() }: AddServiceArgs) {
    this.directServices[serviceId] = {
      serviceId,
      statements,
      serviceFunction,
      roles,
      tags
    };
  }

  public async getServiceEntry(serviceId: string): Promise<SqServiceEntry | SqExceptionResult> {
    let serviceEntry: SqServiceEntry = this.directServices[serviceId];
    if (!serviceEntry) {
      const r = await this.driver.getServiceEntry(serviceId);
      if (isSqExceptionResult(r)) {
        return r;
      }
      serviceEntry = r;
    }

    if (this.statementsPreprocessor) {
      serviceEntry.statements = this.statementsPreprocessor(serviceEntry.statements);
    }
    return serviceEntry;
  }

  setStatementsPreprocessor(preprocessor: StatementPreprocessor) {
    this.statementsPreprocessor = preprocessor;
  }

  async runIntern(request: SqRequest, context: SqContext): Promise<SqResult | undefined> {
    const userId = request.userId || request.userid;
    if (!userId) {
      logger.warn(`Request has no userId set. Process continues with userId: ${ANONYMOUS} (${request.serviceId})`);
      request.userId = ANONYMOUS;
    }
    request.userId = userId;
    request.parameters = request.parameters || {};
    context.recursion++;

    //
    // GET SERVICE
    //

    const serviceEntry = await this.getServiceEntry(request.serviceId);
    if (isSqExceptionResult(serviceEntry)) {
      const exception = serviceEntry.exception;
      logger.warn(exception);
      return serviceEntry;
    }

    //
    // CHECK ACCESS
    //
    let hasAccess = false;
    const roles = request.roles || [];
    if (serviceEntry.roles.length === 0) {
      hasAccess = true;
    } else {
      for (const role of roles) {
        if (serviceEntry.roles.includes(role)) {
          hasAccess = true;
          break;
        }
      }
    }
    if (hasAccess) {
      logger.info(`Access to ${request.serviceId} for user ${userId} :ok`);
      if ('meeting.deleteItem' === request.serviceId) {
        // CRI log pdf handling event
        logger.warn(`>>>CRI event log:  ${userId} deleted file ${request.parameters['in_meeting_item_tid']}`);
      }
    } else {
      logger.info(
        `No access to ${request.serviceId} for ${userId} (service roles: ${serviceEntry.roles}, request roles ${request.roles})`
      );
      context.statusCode = 403;
      return { exception: 'no access' };
    }

    //
    // START PROCESSING STATEMENTS
    //
    logger.info(`Service ${serviceEntry.serviceId} found for ${userId}`);
    context.serviceEntry = serviceEntry;
    logger.info('typeof serviceEntry.serviceFunction: ' + typeof serviceEntry.serviceFunction);
    if (typeof serviceEntry.serviceFunction === 'function') {
      return serviceEntry.serviceFunction({ request, context });
    }
    const statementNode = await this.prepareCommandBlock(serviceEntry, context);
    return this.processCommandBlock({ statementNode, request, currentResult: {}, serviceEntry, context });
  }

  async run(request: SqRequest): Promise<SqResult> {
    const context: SqContext = {
      recursion: 0,
      contextId: CONTEXT_COUNTER++,
      rowsAffectedList: [],
      userMessages: [],
      systemMessages: [],
      statusCode: -1,
      includes: {}
    };

    const result = await this.runIntern(request, context);

    const res = result || (sqExceptionResult('Unexpected empty result!') as SqResult);
    if (res.exception) {
      logger.warn(`serviceId: ${request.serviceId} -> exception: ${res.exception}`);
    } else {
      logger.info(
        `serviceId: ${request.serviceId} -> rows: ${res.table?.length || 0}${
          res.rowsAffected > 0 ? ', rowsAffected: ' + res.rowsAffected : ''
        }`
      );
    }
    return res;
  }

  buildCommandBlockTree(root: SqStatementNode, statementList: string[], pointer: number) {
    while (pointer < statementList.length) {
      const statementNode = this.parseStatement(statementList[pointer]);
      pointer = pointer + 1;
      if (!statementNode) {
        continue;
      }

      root.children = root.children || [];
      root.children.push(statementNode);

      if (this.commands.EndBlock[statementNode.cmd]) {
        return pointer;
      }

      if (this.commands.StartBlock[statementNode.cmd]) {
        pointer = this.buildCommandBlockTree(statementNode, statementList, pointer);
      }
    }
    return pointer;
  }

  async resolveIncludes(serviceEntry: SqServiceEntry, context: SqContext) {
    const statements = serviceEntry.statements;

    const statementList = tokenize(statements);
    const resolvedList: string[] = [];

    for (const s0 of statementList) {
      const stmt = s0.trim();
      await this.resolveIncludes4Statement(stmt, resolvedList, context);
    }
    return resolvedList;
  }

  async resolveIncludes4Statement(stmt: string, resolvedList: string[], context: SqContext) {
    const _inc = 'include';
    if (stmt.substring(0, _inc.length) === _inc) {
      let serviceId = '';
      const parsed = this.parseStatement(stmt);
      if (parsed === null) {
        return;
      }
      serviceId = parsed.parameter;
      const se = await this.getServiceEntry(serviceId);
      if (isSqExceptionResult(se)) {
        this.filteredError(`Did not find service for include: ${serviceId}`);
      } else {
        let counter = context.includes[se.serviceId] || 0;
        counter += 1;
        if (counter < MAX_INCLUDES) {
          context.includes[se.serviceId] = counter;
          const resolvedList2 = await this.resolveIncludes(se, context);

          resolvedList2.forEach((s) => {
            resolvedList.push(s);
          });
        } else {
          this.filteredError('include command overflow:' + se.serviceId);
        }
      }
    } else {
      resolvedList.push(stmt);
    }
  }

  async prepareCommandBlock(se: SqServiceEntry, context: SqContext) {
    const statementList = await this.resolveIncludes(se, context);
    const statementNode: SqStatementNode = {
      cmd: 'serviceRoot',
      parameter: se.serviceId,
      statement: '',
      children: []
    };
    this.buildCommandBlockTree(statementNode, statementList, 0);
    return statementNode;
  }

  async processCommandBlock({
    request,
    currentResult,
    statementNode,
    serviceEntry,
    context
  }: SqFunctionArg): Promise<SqResult | undefined> {
    context.recursion++;

    if (context.recursion > MAX_RECURSION) {
      const msg = 'recursion limit reached with: ' + MAX_RECURSION + '. stop processing.';
      logger.error(msg);
      return { exception: msg };
    }

    logger.debug(`cmd: ${statementNode.cmd}`);
    const fun = this.commands.Registry[statementNode.cmd];
    if (typeof fun === 'function') {
      try {
        return await fun.bind(this)({ request, currentResult, statementNode, serviceEntry, context });
      } catch (e) {
        return sqExceptionResult(e);
      } finally {
        context.recursion--;
      }
    } else {
      logger.error(`unknown command: ${statementNode.cmd} in statement: ${statementNode.statement}`);
      return { exception: 'no command' };
    }
  }

  async serviceRootCommand({
    request,
    currentResult,
    statementNode,
    serviceEntry,
    context
  }: SqFunctionArg): Promise<SqResult | undefined> {
    try {
      for (const snChild of statementNode.children || []) {
        const r = await this.processCommandBlock({
          statementNode: snChild,
          request,
          currentResult,
          serviceEntry,
          context
        });
        currentResult = r || currentResult;
      }
      return currentResult;
    } catch (e) {
      if (isError(e)) {
        logger.error(e.message);
      }
    }
  }

  async sqlCommand({ request, statementNode, serviceEntry, context }: SqFunctionArg): Promise<SqResult> {
    // console.info('sqlCommand', request, statementNode, serviceEntry, context);
    // const params = { ...request.parameters };  request.body  request.query
    // delete params['$CURRENTMILLIS'];
    // delete params['USERAGENT'];
    // delete params['$USERID'];
    // const paramsKeys = Object.values(params).join('.');
    const cacheKey = request.userId + '.' + serviceEntry.serviceId;
    const needCache = enableSqCache && cacheSQList.includes(serviceEntry.serviceId);
    const needRefreshCacheSQList = refreshCachesList[serviceEntry.serviceId];
    needRefreshCacheSQList?.forEach((refreshCacheSQ) => {
      const removeCacheKey = request.userId + '.' + refreshCacheSQ;
      nodeCache.del(removeCacheKey);
    });
    let cacheValue = needCache ? nodeCache.get(cacheKey) : null;
    if (!cacheValue) {
      logger.info('sqlCommand retrive data from db, key: ' + cacheKey);
      cacheValue = await this.driver.processSql(statementNode.parameter, request.parameters, {
        ...context,
        serviceEntry
      });
      if (needCache) {
        logger.info('sqlCommand cached data to key: ' + cacheKey);
        nodeCache.set(cacheKey, cacheValue);
      }
    } else {
      logger.info('sqlCommand return cached data for key: ' + cacheKey);
    }

    return cacheValue;
  }

  async setCommand({ request, currentResult, statementNode }: SqFunctionArg) {
    const overwrite = statementNode.cmd === 'set' || statementNode.cmd === 'copy';
    const nv = statementNode.parameter.split(/=/);
    const n = nv[0].trim();
    let v = nv.length > 1 ? nv[1] : '';

    v = resolveValue(v, request).toString();
    const requestValue = request.parameters[n] || '';

    if (overwrite || isEmpty(requestValue.toString())) {
      request.parameters[n] = v;
    }
    return currentResult;
  }

  async serviceIdCommand({ request, statementNode, context }: SqFunctionArg) {
    const iRequest = deepClone(request);
    iRequest.serviceId = statementNode.parameter;
    return this.runIntern(iRequest, context);
  }

  async parametersCommand({
    request,
    currentResult,
    statementNode,
    serviceEntry,
    context
  }: SqFunctionArg): Promise<SqResult> {
    const overwrite = statementNode.cmd === 'parameters';

    const statementNode1 = this.parseStatement(statementNode.parameter);
    if (statementNode1 === null) {
      return currentResult;
    }

    const result = await this.processCommandBlock({
      statementNode: statementNode1,
      request,
      currentResult,
      serviceEntry,
      context
    });
    if (!result || !result.header || result.header.length === 0) {
      return currentResult;
    }
    if (overwrite) {
      for (const head of result.header) {
        request.parameters[head] = '';
      }
    }
    const firstRow = toFirst(result);
    if (firstRow) {
      for (const [name, value] of Object.entries(firstRow)) {
        if (!request.parameters[name] || overwrite) {
          request.parameters[name] = value;
        }
      }
    }
    return currentResult;
  }

  async ifCommand({ statementNode, request, currentResult, serviceEntry, context }: SqFunctionArg) {
    const ifEmpty = statementNode.cmd === 'if-empty';

    const condition = resolveValue(statementNode.parameter, request);
    let isThen = !!condition;

    isThen = ifEmpty ? !isThen : isThen;

    for (const cbChild of statementNode.children || []) {
      if ('else' === cbChild.cmd) {
        isThen = !isThen;
        continue;
      }
      if (isThen) {
        const r = await this.processCommandBlock({
          statementNode: cbChild,
          request,
          currentResult,
          serviceEntry,
          context
        });
        currentResult = r || currentResult;
      }
    }

    return currentResult;
  }

  async commentCommand({ statementNode, request, currentResult }: SqFunctionArg) {
    if (!statementNode.parameter) {
      const comment = texting(statementNode.parameter, request.parameters);
      logger.info(comment);
    }
    return currentResult;
  }

  async nodeCommand({ request, currentResult, statementNode, serviceEntry, context }: SqFunctionArg) {
    const fun = this.commands.Node[statementNode.parameter];
    if (!fun) {
      logger.error(`No Commands.Registry.node entry found for ${statementNode.parameter}`);
      return currentResult;
    }
    const result = await fun({ request, currentResult, statementNode, serviceEntry, context });
    return result || currentResult;
  }

  parseStatement(statement: string): SqStatementNode | null {
    statement = statement.trim();
    if (isEmpty(statement)) {
      return null;
    }
    let firstWhiteSpace = statement.length;
    for (let i = 0; i < statement.length; i++) {
      const ch = statement[i];
      if (/\s/.test(ch)) {
        firstWhiteSpace = i;
        break;
      }
    }
    const cmd = statement.substring(0, firstWhiteSpace).trim();
    let parameters = '';
    if (firstWhiteSpace !== statement.length) {
      parameters = statement.substring(firstWhiteSpace).trim();
    }
    if (this.isCmd(cmd)) {
      return { cmd, parameter: parameters, statement };
    }
    return { cmd: 'sql', parameter: statement, statement };
  }

  isCmd(cmd: string) {
    return this.commands.StartBlock[cmd] || this.commands.EndBlock[cmd] || this.commands.Registry[cmd];
  }

  // CORE -end-

  async processStatements(sqlStatements: string[]): Promise<SqResult[]> {
    const results: SqResult[] = [];
    for (const sqlStatement of sqlStatements) {
      const result = await this.driver.processSql(sqlStatement, {}, { maxRows: 50000 });
      results.push(result);
    }
    return results;
  }

  filteredError(_error: unknown) {
    let finalError = '';
    if (_error) {
      if (typeof _error === 'string') {
        finalError = _error;
      } else if (isError(_error)) {
        finalError = _error.message;
      } else if (typeof _error === 'object') {
        finalError = _error.toString();
      }
      if (finalError && !this.isFilteredOut(this.ignoredErrors, finalError)) {
        logger.error(finalError);
      }
    }
  }

  isFilteredOut(ignoredErrors: string[], errorString: string) {
    return (ignoredErrors || []).reduce((a, e) => errorString.includes(e) || a, false);
  }
}
