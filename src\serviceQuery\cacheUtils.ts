export const enableSqCache = false;

export const cacheSQList = [
    'meeting_list_published_by_date',
    'meeting_list_published_by_type',
    'meeting_list_archived_by_date',
    'meeting_list_archived_by_type',
    'meeting_list_archived_by_type_admin',
];

interface RefreshCachesList {
    [key: string]: string[];
  }

export const refreshCachesList: RefreshCachesList = {
    'meeting_insert_meeting': [
        'meeting_list_published_by_date',
        'meeting_list_published_by_type',
        'meeting_list_archived_by_type_admin',
        'meeting_list_current_by_type',
        'meeting_list_current_by_type',
        'meeting_list_current_by_date',
        'meeting_list_agenda_asc',
    ],
    'meeting_insert_agenda': [
        'meeting_list_published_by_date',
        'meeting_list_published_by_type',
        'meeting_list_archived_by_type_admin',
        'meeting_list_current_by_type',
        'meeting_list_current_by_date',
        'meeting_list_agenda_asc',
    ],
    'cr_answer_edit': [
        'meeting_list_published_by_date',
        'meeting_list_published_by_type',
        'meeting_list_archived_by_type_admin',
        'meeting_list_current_by_type',
        'meeting_list_current_by_date',
        'meeting_list_agenda_asc',
    ],
    'meeting_update_meeting_status': [
        'meeting_list_published_by_date',
        'meeting_list_published_by_type',
    ],
};