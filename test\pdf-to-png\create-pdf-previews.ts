/* eslint-disable space-before-function-paren */
import { getPDFPreviewInPNG } from '../../src/service/lib/esp/getPDFPreviewInPNG';
/* eslint-disable no-console */
import 'mocha';
import { testLocalMpSrc } from '../common';
import fs from 'fs';
import { getTestSc } from '../init-local';
import { getDbFileContent } from '../../src/service/lib/upload-file-utils';
import { isSqExceptionResult } from '../../src/serviceQuery';
import { processPdfDerivedFiles } from '../../src/service/lib/esp/PdfUtilities';
import { expect } from 'chai';

describe(`testing-preview-generation ${testLocalMpSrc}`, () => {
  const fileTid = '142126782';
  let document: Buffer;
  before(() => {
    getTestSc();
  });

  it('get document', async () => {
    const res = await getDbFileContent(fileTid);
    if (isSqExceptionResult(res)) {
      expect.fail(res.exception);
    } else {
      document = res;
    }
  });

  it('create preview', async () => {
    const res = await processPdfDerivedFiles({ fileTid, filename: 'just-test.pdf' });
    if (isSqExceptionResult(res)) {
      expect.fail(res.exception);
    }
  });
  it('create preview', async () => {
    const buffer = await getPDFPreviewInPNG('test.png', document);
    if (buffer) {
      fs.writeFileSync('test.png', buffer);
    } else {
      expect.fail('Could not create preview');
    }
  });
});
