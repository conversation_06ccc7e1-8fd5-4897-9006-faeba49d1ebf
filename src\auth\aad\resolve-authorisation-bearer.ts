import { Request } from 'express';
import LoggerCenter from '../../logger/LoggerCenter';
import path from 'path';
import jwt from 'jsonwebtoken';
import { SessionCache } from '../../service/SessionCache';
import ServiceCenter from '../../service/ServiceCenter';
import { SYSTEM } from '../../service/constants';
import { singleValue } from '../../serviceQuery';

const SECURITY_TOKEN_NAME = 'Authorization';
const logger = LoggerCenter.getLogger(path.basename(__filename));

function getCookie(name: string, req: Request) {
  let cookieValue = '';
  if (req.cookies) {
    cookieValue = req.cookies[name];
    //logger.info('cookie: '+cookieValue);
  }
  return cookieValue;
}

function getSecurityCookieToken(req: Request) {
  return getCookie(SECURITY_TOKEN_NAME, req);
}

const getTokenFromAuthorizationHeader = (req: Request): string | null => {
  const authHeader = req.header('Authorization') || req.header('authorization');
  logger.info('Authorization header: ' + authHeader);
  if (authHeader) {
    return (authHeader || ' ').split(' ')[1].trim();
  } else {
    logger.error(`No Authorization header found: ${req.originalUrl}`);
    logger.error(`Authorization header: ${authHeader}`);
    return null;
  }
};

const getUserIdFromToken = async (token: string): Promise<string> => {
  let userId = SessionCache.getInstance().getUserId(token);
  if (userId) {
    return userId;
  }

  try {
    const decodedToken = jwt.decode(token) as { [key: string]: any };
    const userEmail = decodedToken.preferred_username || '';
    const sc = ServiceCenter.getInstance();
    const sq = sc.getSq();
    const mRes = await sq.run({
      serviceId: 'user.get',
      parameters: {
        userEmail,
      },
      userId: SYSTEM,
      roles: [SYSTEM]
    });
    const userId = singleValue(mRes)
    if (!userId) {
      logger.warn('No oid claim found!');
      return '';
    }
    
    SessionCache.getInstance().addToken(token, userId, userEmail);
    return userId;
  } catch (error) {
    handleTokenVerificationError(error);
    return '';
  }
};

const handleTokenVerificationError = (error: any) => {
  if (error.name === 'ClientAuthError' && error.errorCode === 'token_expired') {
    logger.info(error.message);
  } else {
    logger.warn(error.message);
  }
};

export const resolveUidFromAuthorizationBearer = async (req: Request) => {
  let token = getSecurityCookieToken(req);
  if (!token) {
    token = getTokenFromAuthorizationHeader(req);
    if (!token) {
      return '';
    }
  }

  return await getUserIdFromToken(token);
};