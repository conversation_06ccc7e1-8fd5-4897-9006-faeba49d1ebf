import { SqNodeFunction, SqResult } from '../../../serviceQuery';
import { stringValue } from '../../service-utils';
import ServiceCenter from '../../ServiceCenter';

const cutDef: Record<string, number> = {
  iespVersion: 64,
  iosVersion: 64,
  deviceType: 64,
  logType: 64,
  logTimestamp: 64,
  logMessage: 4000
};

export const LogMobileInsert: SqNodeFunction = async function({ request }): Promise<SqResult> {
  const parameters = { ...request.parameters };
  Object.keys(cutDef).forEach(
    (name) => (parameters[name] = stringValue(request.parameters, name).substring(0, cutDef[name]))
  );
  return ServiceCenter.getInstance().runSystemSq({ serviceId: 'm.logMobile.insert.sql', parameters });
};
