import { sqExceptionResult, SqN<PERSON><PERSON>unction, SqFunctionArg, SqRequest, SqResult } from '../../../serviceQuery';
import ServiceCenter from '../../ServiceCenter';

export const RequestArrayProcessor: SqNodeFunction = async({ request }: SqFunctionArg): Promise<SqResult> => {
  const sc = ServiceCenter.getInstance();
  const name = 'PagingResultJson';
  const userId = request.userId;
  const roles = request.roles;
  const header = [name];
  const table: string[][] = [];
  try {
    const requestArrayString = request.parameters['requestArray'];

    if (!requestArrayString) {
      return sqExceptionResult('Missing parameter: requestArray');
    }

    const requestData = JSON.parse(requestArrayString.toString());
    if (!Array.isArray(requestData)) {
      return sqExceptionResult('requestArray is not a JSON array!');
    }

    for (const { serviceId, parameters } of requestData as SqRequest[]) {
      try {
        if (!('$USERID' in parameters)) {
          parameters.$USERID = userId;
        }
        const res = await sc.getSq().run({ serviceId, parameters, userId, roles });
        table.push([JSON.stringify(res)]);
      } catch (e) {
        table.push([JSON.stringify(sqExceptionResult(e))]);
      }
    }
    return { header, table, userId, name: 'PagingResultJson' };
  } catch (e1) {
    return sqExceptionResult(e1);
  }
};
