import * as express from 'express';
import { Request, Response } from 'express';
import { Controller } from '../types';
import LoggerCenter from '../logger/LoggerCenter';
import path from 'path';
import { getUserProfile, toParameters } from './web-utils';
import { getDbFile, getDbFileContent, saveDbFile, useEncryption } from '../service/lib/upload-file-utils';
import { DbFile, DBFileSelect } from '../service/lib/esp/types';
import { createSecureSalt, getUserSalt } from '../service/lib/mobile/mobile-utils';
import { isSqExceptionResult, sqExceptionResult } from '../serviceQuery';
import { createKeyString, encrypt } from '../service/lib/mobile/crypto-utils';
import { _temp_ } from '../service/constants';
import ServiceCenter from '../service/ServiceCenter';
import pako from 'pako';

const logger = LoggerCenter.getLogger(path.basename(__filename));
const CONTROLLER_NAME = 'MDocuServlet2Controller';

export default class MDocuServlet2Controller implements Controller {
  public readonly name = CONTROLLER_NAME;
  public readonly paths = ['/api/mobile/docu2/*', '/api/mobile/docu2'];
  public readonly router = express.Router();

  constructor() {
    this.router.post('/*', processHttpRequest);
    this.router.get('/*', processHttpRequest);
  }
}

async function processHttpRequest(req: Request, res: Response) {
  try {
    const sq = ServiceCenter.getInstance().getSq();
    const userProfile = await getUserProfile(sq, req);
    if (!userProfile) {
      res.json(sqExceptionResult('Could not create user profile!'));
      return;
    }
    const { userId, roles } = userProfile;

    logger.info(`${userId} with roles ${roles.join(',')}`);
    const parameters = toParameters(req, userId);
    const compression = parameters['compression'];
    logger.info(`compression: ${compression}`);

    const encValue = parameters['enc'] ?? '';
    const encOverride = 'off' === encValue.toLowerCase();
    logger.info(`encOverride: ${encOverride}`);

    const fileTidList = parameters['fileTidList'];
    if (!fileTidList) {
      logger.error(`fileTidList is empty. request is from user: ${userId}`);
      res.sendStatus(400);
      return;
    }

    const dbFileList = await getDbFiles(fileTidList, userId);
    if (dbFileList.length === 0) {
      res.sendStatus(200);
      return;
    }

    const userSalt = await getUserSalt(userId);
    if (isSqExceptionResult(userSalt)) {
      logger.error(`Could not get user salt for; ${userId}`);
      res.sendStatus(400);
      return;
    }

    const tmpDir = createTempDir();
    setupResponseHeaders(res, 'MDocuServlet2_DBSide');

    const dataList = await processDbFiles(dbFileList, userSalt, encOverride, compression, tmpDir, res);
    await streamDataList(dataList, res);

    res.end();
    logger.info('Data sequence sent successfully!');
  } catch (e) {
    logger.error(`${CONTROLLER_NAME} ${e}`);
    if (!res.headersSent) {
      res.sendStatus(500);
    }
  }
}

async function getDbFiles(fileTidList: string, userId: string): Promise<DbFile[]> {
  const dbFileList: DbFile[] = [];
  const fileIds = fileTidList.split(',');
  for (const fileTid of fileIds) {
    const dbFile = await getDbFile(fileTid);
    if (dbFile) {
      dbFileList.push(dbFile);
    } else {
      logger.warn(`Could not find file: ${fileTid}`);
    }
  }
  return dbFileList;
}

function createTempDir(): string {
  const random = Math.floor(Math.random() * 100000);
  return `${_temp_}${random}/${createSecureSalt(8)}/`;
}

function setupResponseHeaders(res: Response, downloadFileName: string) {
  res.setHeader('Content-Type', 'application/octet-stream');
  res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodeURIComponent(downloadFileName)}`);
}

async function processDbFiles(
  dbFileList: DbFile[],
  userSalt: { userId: any, salt: any, version: any },
  encOverride: boolean,
  compression: string,
  tmpDir: string,
  res: Response
): Promise<DBFileSelect[]> {
  const dataList: DBFileSelect[] = [];
  for (const dbFile of dbFileList) {
    let encryptionKey = '';
    if (useEncryption(dbFile)) {
      encryptionKey = createKeyString(dbFile.filename, userSalt).keyHex;
    }
    if (encOverride) {
      encryptionKey = '';
    }
    const data = await prepareDocument(dbFile, encryptionKey, compression);
    if (!data) {
      logger.warn(`Could not prepare data for: ${dbFile.fileTid} : ${dbFile.filename} found!`);
    } else {
      const prefix_part = dbFile.fileTid + ':' + data.length + ',';
      res.write(Buffer.from(prefix_part, 'utf8'));

      const tmpFile: DbFile = { fileTid: '', filename: `DOCU2-${dbFile.fileTid}`, directory: tmpDir };
      const savedDbFile = await saveDbFile({ ...tmpFile, data, encryption: false });
      if (isSqExceptionResult(savedDbFile)) {
        logger.warn(`Could not save tmpFile: ${tmpFile.filename} (${tmpFile.directory})`);
      } else {
        dataList.push(savedDbFile);
      }
    }
  }

  const prefix_part = '-1:-1,';
  res.write(Buffer.from(prefix_part, 'utf8'));

  return dataList;
}

async function streamDataList(dataList: DBFileSelect[], res: Response) {
  let sizeKB = 0;
  for (const dbFile of dataList) {
    const data = await getDbFileContent(dbFile.fileTid, false);
    if (!isSqExceptionResult(data)) {
      sizeKB += data.length;
      res.write(data);
    }
  }
  logger.info(`Sum of files sized: sizeKB ${sizeKB}`);
}

async function prepareDocument(dbFile: DbFile, encryptionKey: string, compression: string) {
  let data = await getDbFileContent(dbFile.fileTid);
  if (!data) {
    logger.warn(`Document ${dbFile.fileTid} is empty!`);
    return undefined;
  }
  if (isSqExceptionResult(data)) {
    logger.warn(`Could not get document ${dbFile.fileTid}! Exception: ${data.exception}`);
    return undefined;
  }
  if ('zip' === compression || 'gzip' === compression) {
    // data = zlib.gzipSync(data);
    data = Buffer.from(pako.gzip(data));
  }
  if (!data) {
    logger.warn(`Document ${dbFile.fileTid} is empty (after gzip)!`);
    return undefined;
  }
  if (encryptionKey) {
    data = encrypt(encryptionKey, data);
  }
  return data;
}
