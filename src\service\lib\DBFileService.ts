/* eslint-disable space-before-function-paren */
import { DbFile, IDType } from './esp/types';
import { canonicalPath } from '../service-utils';
import ServiceCenter, { newTid } from '../ServiceCenter';
import { SYSTEM } from '../constants';
import { isSqExceptionResult, toList } from '../../serviceQuery';
import { getDbFileContent } from './upload-file-utils';

export const getDocumentAsString = async (fileTid: string) => {
  const content = await getDbFileContent(fileTid);
  if (isSqExceptionResult(content)) {
    return content.exception;
  }
  return content.toString('utf8');
};

export const rename = async (fileTid: IDType, newFilename: string) => {
  return ServiceCenter.getInstance()
    .getSq()
    .run({
      serviceId: 'dbFile.rename',
      userId: SYSTEM,
      roles: [SYSTEM],
      parameters: { fileTid, newFilename }
    });
};

export const moveFile = async (fileTid: IDType, directory: string) => {
  const targetDirectory = canonicalPath(directory);
  return ServiceCenter.getInstance()
    .getSq()
    .run({
      serviceId: 'dbFile.move',
      userId: SYSTEM,
      roles: [SYSTEM],
      parameters: { fileTid, targetDirectory }
    });
};

export const listAllFiles = async (parentDir: string, includeSubDirs: boolean): Promise<DbFile[]> => {
  const sq = ServiceCenter.getInstance().getSq();
  const serviceId = 'dbFile.listAllFiles';
  const userId = SYSTEM;
  const roles = [SYSTEM];
  const parentDirCan = canonicalPath(parentDir);
  const directory = includeSubDirs ? `${parentDirCan}%` : parentDirCan;
  const res = await sq.run({ serviceId, roles, userId, parameters: { directory } });
  return toList<DbFile>(res);
};

export const createPathIdValue = async (existingRemoteFile: DbFile) => {
  let pathId = '';
  if (existingRemoteFile && !existingRemoteFile.pathId) {
    pathId = await newTid();
  } else {
    pathId = getTagValue('pathId', existingRemoteFile.tags);
  }
  return pathId;
};

const TAGS_DEL = ',';
const TAG_NV_DEL = ':';
export const getTagValue = (name: string, tagString = '') => {
  if (!tagString) {
    return '';
  }
  const pairs = tagString.split(TAGS_DEL);
  for (const pair of pairs) {
    const [n, v] = pair.split(TAG_NV_DEL);
    if (n === name) {
      return v || '';
    }
  }
  return '';
};
