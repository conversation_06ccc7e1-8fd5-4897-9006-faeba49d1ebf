/* eslint-disable no-console */
import { test } from 'mocha';
import { expect } from 'chai';
import * as dotenv from 'dotenv';
import { localUrlNode } from '../common';
import axios from 'axios';

dotenv.config();
test(`(webtests) app-properties-test`, async () => {
  const url = `${localUrlNode}serviceQuery/selectAppProperties`;

  const headers = {
    'Content-Type': 'multipart/form-data'
  };

  try {
    const response = await axios.get(url, { headers });
    console.log('select-app-properties Done');
    expect(!!response.data).true;
    expect(!!response.data.header).true;
    expect(response.data.header.length === 2).true;
  } catch (error) {
    console.error(error);
    expect.fail(`Error received from Health check ${error}`);
  }
});
