--
-- KEY = mpLogAccessChanges.select
--

set-if-empty:typeName=%
;
set-if-empty:meetingName=%
;
set-if-empty:meetingStatus=%
;
set-if-empty:agendaName=%
;
set-if-empty:userId=%
;
set-if-empty:lastName=%
;
set-if-empty:firstName=%
;
set-if-empty:editTime=%
;
set-if-empty:editUser=%
;
set-if-empty:logTid=%
;
select * from esp.v_log_access_changes 
where TYPE_NAME is not null 
and TYPE_NAME like :typeName 
and MEETING_NAME like :meetingName 
and MEETING_STATUS like :meetingStatus 
and AGENDA_NAME like :agendaName 
and USER_ID like :userId 
and LAST_NAME like :lastName 
and FIRST_NAME like :firstName 
and cast(EDIT_TIME as text) like cast(:editTime as text)
and EDIT_USER like :editUser 
and cast(LOG_TID as text) like cast(:logTid as text)