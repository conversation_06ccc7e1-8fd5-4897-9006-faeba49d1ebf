steps:
  #  - task: Bash@3
  #    displayName: Remove package lock
  #    inputs:
  #      targetType: inline
  #      script: |
  #        rm package-lock.json
  - task: NodeTool@0
    inputs:
      versionSpec: "20.x"
      displayName: "Install Node.js"

  - task: Cache@2
    displayName: Cache npm dependencies
    inputs:
      key: 'npm | "$(Agent.OS)" | package-lock.json'
      restoreKeys: |
        npm | "$(Agent.OS)"
      path: $(npm_config_cache)

  - task: Npm@1
    displayName: npm install
    inputs:
      command: "custom"
      customCommand: "install"

  #  - task: Npm@1
  #    displayName: npm package-lock
  #    inputs:
  #      command: 'custom'
  #      customCommand: 'install --package-lock'

  - task: Npm@1
    displayName: Build
    inputs:
      command: "custom"
      customCommand: "run ${{ parameters.npmBuildScript }}"
    env:
      NODE_OPTIONS: --max_old_space_size=8192

  - task: UniversalPackages@0
    inputs:
      command: "download"
      downloadDirectory: "$(Agent.TempDirectory)"
      feedsToUse: "internal"
      vstsFeed: "STRATUM@Release"
      vstsFeedPackage: stratum-cli
      vstsPackageVersion: "*" # latest

  - task: Bash@3
    displayName: Add stratum-cli to PATH
    inputs:
      targetType: inline
      script: |
        chmod +x ${TEMP_PATH}/stratum-cli/linux/stratum
        export PATH=${TEMP_PATH}/stratum-cli/linux:${PATH}
        echo "##vso[task.setvariable variable=PATH;]${PATH}"
        which stratum
        stratum --version
    env:
      TEMP_PATH: $(Agent.TempDirectory)

  - task: Bash@3
    displayName: Install kubelogin version
    inputs:
      targetType: inline
      script: |
        echo kube version ${KUBELOGIN_VERSION}
        curl -LO "https://github.com/Azure/kubelogin/releases/download/v${KUBELOGIN_VERSION}/kubelogin-linux-amd64.zip"
        ls -la
        unzip -j "kubelogin-linux-amd64.zip" -d ${TEMP_PATH}/bin
        export PATH=${TEMP_PATH}/bin:${PATH}
        echo "##vso[task.setvariable variable=PATH;]${PATH}"
        which kubelogin
        kubelogin --version
    env:
      KUBELOGIN_VERSION: ${{ parameters.kubeloginVersion }}
      TEMP_PATH: $(Agent.TempDirectory)

  - task: AzureCLI@2
    displayName: "Package and Deploy backend"
    inputs:
      azureSubscription: ${{ parameters.serviceConnection }}
      scriptType: "bash"
      failOnStderr: true,
      scriptLocation: "inlineScript"
      inlineScript: |
        # setup environment config needed for kubelogin (add to path & kubeconfig env var)
        export KUBECONFIG=/tmp/kubeconfig

        # connect to cluster
        stratum use env ${CLUSTER_ENV}

        #  when in a Swiss Re environment, to overcome the proxy SSL injection, add the following parameter:
        # --buildpacks paketo-buildpacks/ca-certificates,paketo-buildpacks/nginx  (resp. the relevant one for the current setup)
        # cd deployment/$DEPLOYMENT_ENV
        stratum_config=.stratum.yaml
        comp=esp-be
        cd dist/src
        ls -la
        echo "listing docker images"
        docker images
        # cp -R ./* .
        echo "pruning docker system"
        docker system prune -a -f
        echo "listing docker images after prune"
        docker images
        if [[ ${TAG} == dev ]]
        then
          echo 'Copying dev deployment file'
          cp ../../deployment-dev.yaml deployment.yaml
        elif [[ ${TAG} == uat ]]
        then
          echo 'Copying uat deployment file'
          cp ../../deployment-uat.yaml deployment.yaml
        else
          echo 'Copying prod deployment file'
          cp ../../deployment-prod.yaml deployment.yaml
        fi
        cp ../../.stratum.yaml .
        cp ../../Dockerfile .
        ls -la
        final_stage=$STAGE
        static=false
        cat Dockerfile
        cat deployment.yaml
        cat package.json
        rm package-lock.json
        stratum use namespace --apmid ${APM_ID} --stage $final_stage
        stratum delete deployment --component ${comp} --stage $final_stage
        az acr login --name stratumpub
        docker build --tag=${APM_ID}/$comp:${TAG} .
        docker tag ${APM_ID}/$comp:${TAG} stratumpub.azurecr.io/${APM_ID}/$comp:${TAG}
        docker push stratumpub.azurecr.io/${APM_ID}/$comp:${TAG}
        stratum create deployment --package=false --tag ${TAG} --stage $final_stage --apmid ${APM_ID} --component ${comp} --timeout 30m
        kubectl get pods -n ${APM_ID}-$final_stage
        cd ../../..
        echo "final stage $final_stage"
        # only last image is scanned
        IMAGE_NAME=${APM_ID}/${comp}:${TAG}            

        echo "##vso[task.setvariable variable=IMAGE_NAME;]${IMAGE_NAME}"

        # clean up the kubeconfig for security reasons
        rm -f $KUBECONFIG

    env:
      CLUSTER_ENV: ${{ parameters.clusterEnv }}
      APM_ID: ${{ parameters.apmId }}
      STAGE: ${{ parameters.stage }}
      TAG: ${{ parameters.imageTag }}
      DEPLOYMENT_ENV: ${{ parameters.deploymentEnv }}

  - task: Docker@2
    inputs:
      containerRegistry: "VSTS-Docker-Registry"
      command: "login"
#  - task: aquasecScanner@4
#    continueOnError: true
#    displayName: AquaScan
#    inputs:
#      image: "$(IMAGE_NAME)"
#      scanType: 'local'
#      register: false
#      hideBase: false
#      showNegligible: false
#      scanner: 'vstsdockerregistry.azurecr.io/aqua-scanner-public:latest'
#      runOptions: '--privileged --userns=host -e http_proxy=${SR_DEFAULT_UPSTREAM_PROXY}:8080 -e https_proxy=${SR_DEFAULT_UPSTREAM_PROXY}:8080'
#      connection: 'Aqua-Scanner-Public'
#      caCertificates: true
#      customFlags: '--direct-cc'
