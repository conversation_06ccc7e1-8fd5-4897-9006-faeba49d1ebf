import { IDType } from './types';
import ServiceCenter from '../../ServiceCenter';
import { SYSTEM } from '../../constants';
import { toList } from '../../../serviceQuery';

export const NR_OF_PAGES = -100;
export const ANNOTATION_FILE = -101;
export const ANNOTATED = -102;
export const PREVIEW_ICON = -103;
export const ORIGINAL_MAILED = -104;
export const ANNOTATED_MAILED = -105;

type FileRel = {
  fileTid: string;
  relTid: string;
  targetTid: string;
  targetValue: string;
  indx: string;
};
export const saveFileRel = (
  fileTid: IDType,
  derivedFileTid: IDType,
  targetValue: string,
  relTid: number,
  indx: string
) => {
  const sc = ServiceCenter.getInstance();
  const serviceId = 'dbFile.saveFileRel';
  return sc.runSystemSq({
    serviceId,
    parameters: { fileTid, relTid, targetTid: derivedFileTid, targetValue, indx }
  });
};

export const saveFileRelPageNr = (fileTid: IDType, nrOfPages: number) => {
  return saveFileRel(fileTid, '-1', nrOfPages + '', NR_OF_PAGES, '0');
};

export const insertFileRelForAnnotation = (fileTid: IDType, derivedFileTid: IDType, userId: string, relTid: number) => {
  const sq = ServiceCenter.getInstance().getSq();
  const serviceId = 'dbFile.insertFileRel';

  return sq.run({
    serviceId,
    userId: SYSTEM,
    roles: [SYSTEM],
    parameters: { fileTid, relTid, targetTid: derivedFileTid, targetValue: userId, indx: derivedFileTid }
  });
};

export const deleteFileRelForTarget = (targetTid: IDType) => {
  const sq = ServiceCenter.getInstance().getSq();
  const serviceId = 'dbFile.deleteFileRelForTarget';
  return sq.run({
    serviceId,
    userId: SYSTEM,
    roles: [SYSTEM],
    parameters: { targetTid }
  });
};

export const getFileRel = async(fileTid: string, relTid: number, indx: number) => {
  const sq = ServiceCenter.getInstance().getSq();
  const serviceId = 'dbFile.getFileRel';
  const sqResult = await sq.run({
    serviceId,
    userId: SYSTEM,
    roles: [SYSTEM],
    parameters: { fileTid, relTid, indx }
  });
  const list = toList<FileRel>(sqResult);
  if (list.length) {
    return list[0].targetTid;
  }
};
