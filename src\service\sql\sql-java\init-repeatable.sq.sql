-- KEY   = mp.visible.tabs.desktop
select vt.*,cy.backend_url from esp.v_ol_visible_tab_desktop vt
left outer join esp.t_cycle_definition_user cy on cy.name = vt.tab||'_data'
;

-- KEY   = mp.visible.tabs
select * from esp.v_mp_tabs order by ord
;


--
-- KEY           = esp.userSalt.insert
-- ACCESS_GROUPS = SYSTEM
--

insert into ESP.T_IESP_USER_SALT
(USER_ID, SALT, VERSION, CREATED_UT, CREATOR_ID)
values
(:userId,:salt,:version,:createdUt,:$USERID)
;


--
-- KEY           = userSalt.save
-- ACCESS_GROUPS = SYSTEM
--

delete from ESP.T_IESP_USER_SALT where USER_ID = :userId
;
include:esp.userSalt.insert
;



-- KEY           = esp.userSalt.get
-- ACCESS_GROUPS = SYSTEM
--

select * from ESP.T_IESP_USER_SALT where USER_ID = :userId
;




--
-- KEY           = UserSalt.change
-- ACCESS_GROUPS = ESP_ADMIN
-- 

java:com.swissre.esp.service.queryServices.UserSaltChange
;


--
-- KEY   = usage.by.meeting.select
-- ACCESS_GROUPS = ESP_ADMIN
-- 

set-if-empty:group=%
;

  SELECT a.group_name as "GROUP",
         a.type_name as "TYPE",
         b."PUBLISHED_SUM",
         c."PAST_SUM",
         a."TOTAL_SUM",
         a.Meeting as "MEETING_COUNT",
         a.Document as "DOCUMENT_COUNT"
    FROM (  SELECT group_name,
                   type_name,
                   ROUND (SUM (size_sum) / 1048576, 1) AS "TOTAL_SUM",
                   SUM (meeting_count) AS Meeting,
                   SUM (document_count) AS Document
              FROM ESP.v_mp_usage_stat
             WHERE status_tid = 10 OR status_tid = 11
          GROUP BY group_name, type_name) a
         LEFT JOIN
         (  SELECT group_name,
                   type_name,
                   ROUND (SUM (size_sum) / 1048576, 1) AS "PUBLISHED_SUM"
              FROM ESP.v_mp_usage_stat
             WHERE status_tid = 10
          GROUP BY group_name, type_name) b
            ON a.group_name = b.group_name AND a.type_name = b.type_name
         LEFT JOIN
         (  SELECT group_name,
                   type_name,
                   ROUND (SUM (size_sum) / 1048576, 1) AS "PAST_SUM"
              FROM ESP.v_mp_usage_stat
             WHERE status_tid = 11
          GROUP BY group_name, type_name) c
            ON a.group_name = c.group_name AND a.type_name = c.type_name
            where a.group_name like :group
ORDER BY "TOTAL_SUM" DESC;


--
-- KEY   = usage.by.user.select
-- ACCESS_GROUPS = ESP_ADMIN
-- 

set-if-empty:name=%
;
SELECT name,
       PUBLISHED_SUM,
       PAST_SUM,
       TOTAL_SUM
  FROM (SELECT v.*, ROW_NUMBER () OVER (ORDER BY TOTAL_SUM DESC) r
          FROM (SELECT a.first_name || ' ' || a.last_name AS name,
                       ROUND (b.volume / 1048576, 1) AS PUBLISHED_SUM,
                       ROUND (c.volume / 1048576, 1) AS PAST_SUM,
                       ROUND (a.volume / 1048576, 1) AS TOTAL_SUM
                  FROM (  SELECT first_name, last_name, SUM (volume) AS volume
                            FROM ESP.v_mp_usage_user_stat
                           WHERE status_tid = 10 OR status_tid = 11
                        GROUP BY first_name, last_name) a
                       LEFT JOIN
                       (  SELECT first_name, last_name, SUM (volume) AS volume
                            FROM ESP.v_mp_usage_user_stat
                           WHERE status_tid = 10
                        GROUP BY first_name, last_name) b
                          ON     a.first_name = b.first_name
                             AND a.last_name = b.last_name
                       LEFT JOIN
                       (  SELECT first_name, last_name, SUM (volume) AS volume
                            FROM ESP.v_mp_usage_user_stat
                           WHERE status_tid = 11
                        GROUP BY first_name, last_name) c
                          ON     a.first_name = c.first_name
                             AND a.last_name = c.last_name) v) u
 WHERE r <= 300 AND NAME like :name;


--
-- KEY           = selectAccessExclude
-- ACCESS_GROUPS = ESP_ADMIN
--

set-if-empty:name=%
;
set-if-empty:exclude=%
;
set-if-empty:filetypeNme=%
;
set-if-empty:roleName=%
;
select
ae.ROLE_NAME, ae.FILETYPE_NME, mpt.NAME, ae.EXCLUDE
from
ESP.T_ACCESS_EXCLUDE ae
left outer join
ESP.T_MP_TYPE mpt on mpt.type_id = ae.filetype_nme
where
mpt.NAME like :name
and
ae.EXCLUDE like :exclude
and
ae.FILETYPE_NME like :filetypeNme
and
ae.ROLE_NAME like :roleName
order by
mpt.ORD
;


-- KEY=getAccessExclude
-- ACCESS_GROUPS=ESP_ADMIN

select
ae.ROLE_NAME, ae.FILETYPE_NME, mpt.NAME, ae.EXCLUDE
from
ESP.T_ACCESS_EXCLUDE ae
left outer join
ESP.T_MP_TYPE mpt on mpt.type_id = ae.filetype_nme
where
ae.FILETYPE_NME = :filetypeNme
and
ae.ROLE_NAME = :roleName
order by
mpt.ORD


-- KEY=updateAccessExclude
-- ACCESS_GROUPS=ESP_ADMIN


update
ESP.T_ACCESS_EXCLUDE
set EXCLUDE = :exclude
where
FILETYPE_NME = :filetypeNme



-- KEY           = selectMpMtgroup
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT

select NAME from ESP.T_MP_MTGROUP order by NAME



-- KEY           = selectMpMtgroupInput
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT

set-if-empty:mtgroupName=%
;
set-if-empty:typeName=%
;
select mtgi.MTGROUP_NAME, mtgi.TYPE_TID, mtgi.TYPE_NAME,  mtgi.ORD
from
ESP.T_MP_MTGROUP_INPUT mtgi
inner join
ESP.T_MP_MTGROUP mtg on mtg.NAME = mtgi.MTGROUP_NAME
inner join
ESP.T_MP_TYPE mpt on mpt.type_tid = mtgi.type_tid
where
mtgi.MTGROUP_NAME like :mtgroupName
and
mtgi.TYPE_NAME like :typeName
and mpt.item_type = 'MT'
order by mtg.ORD, mtgi.ORD



-- KEY=getMpMtgroupInput
-- ACCESS_GROUPS=ESP_ADMIN,ESP_SUPPORT

select mtgi.MTGROUP_NAME, mtgi.TYPE_TID, mtgi.TYPE_NAME,  mtgi.ORD
from
ESP.T_MP_MTGROUP_INPUT mtgi
inner join
ESP.T_MP_MTGROUP mtg on mtg.NAME = mtgi.MTGROUP_NAME
where
mtgi.TYPE_TID = :typeTid
order by mtg.ORD, mtgi.ORD
;



-- KEY   = updateMpMtgroupInput
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
--

update ESP.T_MP_MTGROUP_INPUT
set MTGROUP_NAME = :mtgroupName,
ORD = :ord
where
TYPE_TID = :typeTid
;
call ESP.SP_MP_PROCESS_MTGROUP_INPUT(:typeTid )
;


--
--
-- CRUD mpMtgroupInput_1
--
--



-- KEY=selectMpMtgroup2
-- ACCESS_GROUPS=ESP_ADMIN,ESP_SUPPORT

set-if-empty:name=%
;
select
MTGROUP_TID, ORD, NAME
from
ESP.T_MP_MTGROUP
where NAME like :name order by ORD, MTGROUP_TID



-- KEY=getMpMtgroup
-- ACCESS_GROUPS=ESP_ADMIN,ESP_SUPPORT

select
MTGROUP_TID, ORD, NAME
from
ESP.T_MP_MTGROUP
where
MTGROUP_TID = :mtgroupTid



-- KEY   = insertMpMtgroup
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT

insert into
ESP.T_MP_MTGROUP
(MTGROUP_TID, ORD, NAME)
values
(nextval('esp.seq_global_tid'), :ord, :name)
;

-- delete from ESP.T_MP_MTGROUP where ORD = -1

--
-- KEY   = updateMpMtgroup
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
--

update ESP.T_MP_MTGROUP_INPUT
set
MTGROUP_NAME = :name
where MTGROUP_NAME = (select NAME from ESP.T_MP_MTGROUP where MTGROUP_TID = :mtgroupTid)
;
update ESP.T_MP_MTGROUP
set NAME = :name,
ORD = :ord
where
MTGROUP_TID = :mtgroupTid
;



--
-- KEY   = call_sp_b
--

call ESP.B_SERVER(:myRoles)
;
include: select_my_roles
;


--
-- KEY   = select_my_roles
--

select user_id
     , role_name
     , valid_from
     , valid_to
     , 'SNOW' as origin

from esp.t_role
where valid_from <= current_date
  and valid_to >= current_date
  and user_id = :$USERID
union
select user_id
     , role_name
     , valid_from
     , valid_to
     , 'Temp' as origin
from esp.t_role_temp
where valid_from <= current_date
  and valid_to >= current_date
  and user_id = :$USERID
;



--
-- KEY   = select_my_roles2
--
select t.role_name,
       case when snow.USER_ID is null then 'N' else 'Y' end SNOW,
       snow.valid_from                                      VALID_FROM_SNOW,
       snow.valid_to                                        VALID_TO_SNOW,

       case when temp.USER_ID is null then 'N' else 'Y' end TEMP,
       temp.valid_from                                      VALID_FROM_TEMP,
       temp.valid_to                                        VALID_TO_TEMP,
       temp.REASON                                          as REASON
from (SELECT DISTINCT ti.role_name
      FROM (SELECT DISTINCT role_name
            FROM esp.t_access_spec_input
            UNION ALL
            SELECT DISTINCT role_name
            FROM ESP.V_ROLE_ALL
            UNION ALL
            SELECT 'ESP_SUPPORT'

            UNION ALL
            SELECT 'ESP_CO_BOD_ADMIN'

            UNION ALL
            SELECT 'ESP_CO_BOD_READER'

            UNION ALL
            SELECT 'ESP_CO_COMPETITOR_READER'

            UNION ALL
            SELECT 'ESP_CO_COMPETITOR_ADMIN'

            UNION ALL
            SELECT 'ESP_CO_HR_ADMIN'

            UNION ALL
            SELECT 'ESP_CO_HR_READER'

            UNION ALL
            SELECT 'ESP_ADMIN_MGMT'

            UNION ALL
            SELECT 'ESP_INTERWES'

            UNION ALL
            SELECT 'ESP_BP_CONSUMER_INTERWES'

            UNION ALL
            SELECT 'ESP_ADMIN_MGMT2') as ti) as t

         left join esp.t_role snow
                   on snow.ROLE_NAME = t.ROLE_NAME
                       and snow.USER_ID =  :$USERID
                       and snow.valid_from <= current_date
                       and snow.valid_to >= current_date


         left join esp.t_role_temp temp
on temp.ROLE_NAME = t.ROLE_NAME
    and temp.USER_ID =  :$USERID
    and temp.valid_from <= current_date
    and temp.valid_to >= current_date
order by 1, 2

;

--
-- KEY   = remove_my_role
-- TAGS  = DEVONLY
--

delete from ESP.T_ROLE where ROLE_NAME = :roleName and USER_ID = :$USERID
;
delete from ESP.T_ROLE_TEMP where ROLE_NAME = :roleName and USER_ID = :$USERID
;
include: select_my_roles2
;


-- USER_ID DEVICE_ID  VERSION DEVICE_TYPE UPDATED



---------------------------------------
--
-- Document Queries for IESP getkeys
--
-----------------------------------------


--
-- KEY     = mob.getkeys.getBPReports
-- ACCESS_GROUPS = SYSTEM
--

select * from ESP.V_IESP_GETKEYS_BPREPORTS
;




--
-- KEY     = mob.getkeys.getDocuments.current
-- ACCESS_GROUPS = SYSTEM
--

select filetype_tid,
       upload_instance_directory,
       upload_instance_tid,
       file_tid,
       to_char(creation_timestamp, 'DD-MON-YYYY HH24:MI') as creation_timestamp,
       directory,
       filename,
       owner,
       tags,
       length,
       path_id,
       prio,
       doc_type,
       load_prio,
       annotation_file_tid,
       read_flag,
       suppl_flag,
       number_of_pages,
       visibility,
       meeting_status,
       item_type
from ESP.V_IESP_GETKEYS_DOCS_CURRENT
;




--
-- KEY     = mob.getkeys.getDocuments.archived
-- ACCESS_GROUPS = SYSTEM,ESP_ADMIN
--

select filetype_tid,
       upload_instance_directory,
       upload_instance_tid,
       file_tid,
       to_char(creation_timestamp, 'DD-MON-YYYY HH24:MI') as creation_timestamp,
       directory,
       filename,
       owner,
       tags,
       length,
       path_id,
       prio,
       doc_type,
       load_prio,
       annotation_file_tid,
       read_flag,
       suppl_flag,
       number_of_pages,
       visibility,
       meeting_status,
       item_type
from ESP.V_IESP_GETKEYS_DOCS_ARCHIVED
;

--
-- KEY     = mob.getkeys.getDocuments.archived1
-- ACCESS_GROUPS = SYSTEM,ESP_ADMIN
--

select filetype_tid,
       upload_instance_directory,
       upload_instance_tid,
       file_tid,
       to_char(creation_timestamp, 'DD-MON-YYYY HH24:MI') as creation_timestamp,
       directory,
       filename,
       owner,
       tags,
       length,
       path_id,
       prio,
       doc_type,
       load_prio,
       annotation_file_tid,
       read_flag,
       suppl_flag,
       number_of_pages,
       visibility,
       meeting_status,
       item_type
from ESP.V_IESP_GETKEYS_DOCS_ARCHIVED
where MOD(FILE_TID, 2) = 0
;

--
-- KEY     = mob.getkeys.getDocuments.archived2
-- ACCESS_GROUPS = SYSTEM,ESP_ADMIN
--

select filetype_tid,
       upload_instance_directory,
       upload_instance_tid,
       file_tid,
       to_char(creation_timestamp, 'DD-MON-YYYY HH24:MI') as creation_timestamp,
       directory,
       filename,
       owner,
       tags,
       length,
       path_id,
       prio,
       doc_type,
       load_prio,
       annotation_file_tid,
       read_flag,
       suppl_flag,
       number_of_pages,
       visibility,
       meeting_status,
       item_type
from ESP.V_IESP_GETKEYS_DOCS_ARCHIVED where MOD (FILE_TID, 2) = 1
;


--
-- KEY     = mob.getkeys.getDocuments.general
-- ACCESS_GROUPS = SYSTEM
--

select
    filetype_tid,
    upload_instance_directory,
    upload_instance_tid,
    file_tid,
    to_char(creation_timestamp, 'DD-MON-YYYY HH24:MI') as creation_timestamp,
    directory,
    filename,
    owner,
    tags,
    length,
    path_id,
    prio,
    doc_type,
    load_prio,
    annotation_file_tid,
    read_flag,
    suppl_flag,
    number_of_pages,
    visibility,
    meeting_status,
    item_type
from ESP.V_IESP_GETKEYS_DOCS_GENERAL
;





--
-- KEY     = mob.getkeys.getDocuments.cr
-- ACCESS_GROUPS = SYSTEM
--

select filetype_tid,
       upload_instance_directory,
       upload_instance_tid,
       file_tid,
       to_char(creation_timestamp, 'DD-MON-YYYY HH24:MI') as creation_timestamp,
       directory,
       filename,
       owner,
       tags,
       length,
       path_id,
       prio,
       doc_type,
       load_prio,
       annotation_file_tid,
       read_flag,
       suppl_flag,
       number_of_pages,
       visibility,
       meeting_status,
       item_type
from ESP.V_IESP_GETKEYS_DOCS_CR;



--
-- KEY     = mob.getkeys.getDocuments.esp
-- ACCESS_GROUPS = SYSTEM
--

select u.FILETYPE_TID                                                                                                 as FILETYPE_TID,
       coalesce(u.DIRECTORY, '/')                                                                                     as UPLOAD_INSTANCE_DIRECTORY,
       u.UPLOAD_INSTANCE_TID,
       f.FILE_TID                                                                                                     as FILE_TID,
       TO_CHAR(f.CREATION_TIMESTAMP, 'YYYY-MM-DD HH24:MI')                                                            as CREATION_TIMESTAMP,
       '/ESP.T_UPLOAD_INSTANCE/' || MAX(f.FILE_TID) OVER (PARTITION BY coalesce(u.DIRECTORY, '/'), f.filename) ||
       '/'                                                                                                            as DIRECTORY,
       f.FILENAME                                                                                                     as FILENAME,
       f.OWNER                                                                                                        as OWNER,
       f.TAGS                                                                                                         as TAGS,
       LENGTH(f.DOCUMENT)                                                                                             as LENGTH
        ,
       MAX(f.FILE_TID)
       OVER (PARTITION BY coalesce(u.DIRECTORY, '/'), f.filename)                                                     AS path_id
        ,
       30                                                                                                             as prio
        ,
       (select max(FILE_TID)
        from ESP.T_FILE
        where directory =
              '/.derived/' || f.FILE_TID || '/' ||
              :$USERID || '/' and lower(filename) not like '%.pdf') as ANNOTATION_FILE_TID
        , (
select count(*)
from ESP.T_USER_REQUEST
where USER_ID = :$USERID
  and REQUEST_ID = 'FILE_TID:' || f.FILE_TID) READ_FLAG
    , coalesce ((select frel.TARGET_VALUE from ESP.T_FILE_REL frel
    where frel.FILE_TID = f.FILE_TID
  and frel.INDX = 0
  and frel.REL_TID = -100)
    , '-1')
    as NUMBER_OF_PAGES
from
    ESP.T_UPLOAD_INSTANCE u, ESP.T_FILE f, ESP.T_UPLOAD_FILETYPE t
where
    u.SESSION_ID = -999999
  and
    u.FILETYPE_TID in (
    select FILETYPE_TID from ESP.V_FILETYPES_CURR_USERID
    where ACCESS_RIGHT_TID = 1
    )
  and
    u.UPLOAD_INSTANCE_TID in (
    select max(UPLOAD_INSTANCE_TID) from ESP.T_UPLOAD_INSTANCE
    where FILETYPE_TID = u.FILETYPE_TID
  and UPLOAD_STATUS_TID = 2
    )
  and
    f.directory = '/ESP.T_UPLOAD_INSTANCE/'
    || to_char(u.UPLOAD_INSTANCE_TID) || '/'
  and
    not
    exists (select * from ESP.T_UPLOAD_FILETYPE_PROPERTIES p
    where
    p.FILETYPE_TID = u.FILETYPE_TID
  and
    p.NAME = 'IESPUpdateMode'
  and
    p.VALUE = 'all'
    )
  and
    u.FILETYPE_TID = t.FILETYPE_TID
  and
    t.valid_from <= current_date
  and
    current_date <= t.valid_to
order by u.FILETYPE_TID
;

-- KEY      = meeting_list_agenda

select *
from (SELECT a.agenda_tid,
             a.meeting_tid,
             a.ord,
             replace(a.name, '{{', '@')              name,
             a.description,
             a.item_type,
             MAX(access_right_tid)                   access_right_tid,
             (SELECT COUNT(*)
              FROM esp.t_mp_file
              WHERE meeting_item_tid = a.agenda_tid) doc_count
              ,
             m.status_tid
      FROM esp.t_mp_agenda a
               INNER JOIN esp.v_mp_access_agenda mia
                          ON mia.user_id = esp.user_code()
                              AND mia.agenda_tid = a.agenda_tid
                              AND mia.access_right_tid <> 0
               INNER JOIN esp.t_mp_meeting m on m.meeting_tid = a.meeting_tid
      GROUP BY a.agenda_tid,
               a.meeting_tid,
               a.ord,
               a.name,
               a.description,
               a.item_type,
               m.status_tid)
where meeting_tid = :in_meeting_tid
  and not (status_tid = 1 and item_type in ('REP', 'RES'))
order by ord desc
;


-- KEY      = meeting_list_agenda_asc
--

select * from (
                  SELECT
                      a.agenda_tid,
                      a.meeting_tid,
                      a.ord,
                      replace(a.name,'{{','@') name,
                      a.description,
                      a.item_type,
                      m.item_type parent_item_type,
                      MAX (access_right_tid) access_right_tid,
                      (SELECT COUNT (*)
                       FROM esp.t_mp_file
                       WHERE meeting_item_tid = a.agenda_tid) doc_count
                          ,m.status_tid
                  FROM esp.t_mp_agenda a
                           INNER JOIN esp.v_mp_access_agenda mia
                                      ON     mia.user_id = esp.user_code()
                                          AND mia.agenda_tid = a.agenda_tid
                                          AND mia.access_right_tid <> 0
                           INNER JOIN esp.t_mp_meeting m on m.meeting_tid = a.meeting_tid
                  GROUP BY a.agenda_tid,
                           a.meeting_tid,
                           a.ord,
                           a.name,
                           a.description,
                           a.item_type,
                           m.item_type,
                           m.status_tid
              ) ma
where meeting_tid = cast(:in_meeting_tid as numeric) and not (status_tid=1 and item_type in ('REP','RES'))
order by ord
;


-- KEY      = meeting_maintain_access

call ESP.sp_mp_access_detail_maintain (:in_meeting_item_id, :in_userid, :in_enable_0_1)



-- KEY      = meeting_maintain_access_bulk

call ESP.sp_mp_access_detail_maint_bulk (:in_meeting_item_tid, :in_user_id, :in_access)




-- KEY      = meeting_read_type_properties

select type_tid, hist_exp, anno_exp, ipad_exp, past_exp, name
from esp.t_mp_type where type_tid = :in_type_tid
;


-- KEY      = meeting_update_type_properties
call ESP.SP_MP_UPDATE_PROPERTIES (:in_fileType, :in_histRet, :in_annotRet, :iPadVsbl, :in_pastStatus, :in_store_oracle, :in_store_sp)




-- KEY      = zz-meeting_insert_meeting

call esp.sp_mp_insert_meeting_only ( :in_meeting_name, :in_parent, :in_preceding_meeting, :in_location_room, :in_location_address, :in_location_city, :in_time_start, null)
;
include:cr_get_lastinserted
;

-- KEY      = meeting_update_meeting

call esp.sp_mp_rename_item ( :in_meeting_item_tid, :in_new_name)




-- KEY      = meeting_update_meeting_status

call esp.sp_mp_update_meeting_status ( :in_meeting_item_tid, :in_new_status_tid)


-- KEY      = meeting_insert_agenda
call esp.sp_mp_insert_agenda ( :in_agenda_name, :in_agenda_description, :in_parent, :in_preceding_agenda); include:meeting.getMaxAgenda




-- KEY      = meeting_moveItem

call esp.sp_mp_item_move ( :in_meeting_item_tid, :in_direction)






--
-- KEY      = meeting_documents
-- 

select mpa.meeting_tid
     , mpa.agenda_tid
     , coalesce(fa.file_tid, f1.file_tid) as                     file_tid
     , f.owner
     , f.filename
     , to_char(f.creation_timestamp, 'DD-MON-YYYY HH24:MI')      created
     , fr.target_value                                           pages
     , mpo.ord
     , case
           when (lead(f.filename, 1) over (order by mpa.meeting_tid,mpa.agenda_tid,mpo.ord,f1.file_tid ) = f.filename
               and
                 lead(mpa.agenda_tid, 1) over (order by mpa.meeting_tid, mpa.agenda_tid, mpo.ord, f1.file_tid ) =
                 mpa.agenda_tid
               ) then 'hist'
           else 'nohist'
    end                                                          history
     , mpa.name                                                  agenda_name
     , mpm.name                                                  meeting_name
     , mpt.name                                                  type_name
     , case when u.user_id is null then 'unread' else 'read' end unread
     , case
           when mpm.status_tid = 11 then 'A'
           when mpm.status_tid = 1 then 'D'
           when mpm.status_tid = 10 then 'P'
           else '?' end                                          status
     , case when fa.file_tid is not null then 'Y' else 'N' end   annotated
     , coalesce(mpfls.suppl_flag, 'N')    as                     suppl_flag
     , mpfl.label
     , (select count(*)
        from esp.t_file_rel frc
        where frc.rel_tid = -102 and frc.file_tid = f1.file_tid) annotation_count
from esp.t_mp_meeting mpm
         inner join esp.t_mp_agenda mpa on mpa.meeting_tid = mpm.meeting_tid
         inner join esp.v_mp_access_agenda vaa on vaa.agenda_tid = mpa.agenda_tid
         inner join esp.t_mp_file f1 on f1.meeting_item_tid = mpa.agenda_tid
         inner join esp.t_file f on f.file_tid = f1.file_tid
         inner join esp.t_mp_type mpt on mpt.type_tid = mpm.type_tid
         inner join esp.t_mp_file_order mpo on MPO.AGENDA_TID = mpa.agenda_tid and mpo.filename = f.filename
         left outer join esp.t_mp_file_label mpfl
                         on mpfl.meeting_item_tid = mpa.agenda_tid and mpfl.file_name = f.filename
         left outer join esp.T_MP_FILE_SUPPL mpfls
                         on mpfls.meeting_item_tid = mpa.agenda_tid and mpfls.file_name = f.filename
         left outer join esp.t_file_rel fr on fr.file_tid = f.file_tid and fr.rel_tid = -100
         left outer join esp.t_file_rel fannot on fannot.file_tid = f1.file_tid and fannot.rel_tid = -102 and
                                                  fannot.target_value = esp.user_code()
         left outer join esp.t_file fa on fa.file_tid = fannot.target_tid
    and current_date < cast(fa.creation_timestamp as date) + mpt.anno_exp
         left outer join esp.t_user_request u on U.REQUEST_ID = 'FILE_TID:' || f.file_tid
    and u.user_id = esp.user_code()
where mpm.meeting_tid = cast(:in_meeting_tid as numeric)
order by mpa.ord, mpo.ord, history, f1.file_tid desc
;

--
-- KEY      = meeting_documents_download
-- 

select
f.FILE_TID,
mpa.NAME as AGENDA_NAME,
mpm.NAME as MEETING_NAME,
mpa.AGENDA_TID,
mpa.MEETING_TID,
f.DIRECTORY,
f.FILENAME
from ESP.T_MP_MEETING mpm
left join ESP.T_MP_AGENDA mpa on mpa.MEETING_TID = mpm.MEETING_TID
left join ESP.T_MP_FILE mpf on mpf.MEETING_ITEM_TID = mpa.AGENDA_TID
left join ESP.T_FILE f on f.FILE_TID = mpf.FILE_TID
where
mpm.MEETING_TID = :meetingTid
and
f.FILE_TID =  (
select max(i.FILE_TID)
from ESP.T_FILE i
left join ESP.T_MP_FILE mpfi on mpfi.FILE_TID = i.FILE_TID
where mpfi.MEETING_ITEM_TID = mpf.MEETING_ITEM_TID
and i.DIRECTORY like '/mp/%'
and i.FILENAME = f.FILENAME
)


and
0 = (
    select count(*)
    from ESP.T_MP_ACCESS_DETAIL i
    where i.USER_ID = ESP.user_code()
    and i.ACCESS_RIGHT_TID = 0
    and i.MEETING_ITEM_TID = mpa.AGENDA_TID
)

order by
mpa.NAME, f.FILENAME
;


--
-- KEY    = agenda_documents
-- 

set-if-empty:in_agenda_tid = -1
;
select mpa.meeting_tid, mpf.meeting_item_tid agenda_tid, coalesce(fa.file_tid, mpf.file_tid) file_tid, f.owner,
f.filename,
to_char(f.creation_timestamp, 'DD-MON-YYYY HH24:MI') created, fr.target_value pages, mpo.ord,
case when ( lead( f.filename, 1) over (order by mpa.meeting_tid, mpf.meeting_item_tid, mpo.ord, mpf.file_tid ) = f.filename
and
lead( mpf.meeting_item_tid, 1) over (order by mpa.meeting_tid, mpf.meeting_item_tid, mpo.ord, mpf.file_tid ) =  mpf.meeting_item_tid
) then 'hist' else 'nohist' end  history
, mpa.name agenda_name
, mpm.name meeting_name
, mpt.name type_name
, case when u.user_id is null then 'unread' else 'read' end unread
, case when mpm.status_tid = 11 then 'A' when mpm.status_tid = 1 then 'D' when mpm.status_tid = 10 then 'P' else '?' end status
, case when fa.file_tid is not null then 'Y' else 'N' end annotated
, coalesce(mpfls.suppl_flag, 'N') suppl_flag
, mpfl.label
, (select count(*) from esp.t_file_rel frc where frc.rel_tid = -102 and frc.file_tid = mpf.file_tid ) annotation_count
from esp.v_mp_file mpf
inner join esp.t_file f on f.file_tid = mpf.file_tid
inner join esp.t_mp_agenda mpa on mpa.agenda_tid = mpf.meeting_item_tid
inner join esp.t_mp_meeting mpm on MPM.MEETING_TID = mpa.meeting_tid
inner join esp.t_mp_type mpt on mpt.type_tid = mpm.type_tid
left outer join esp.t_mp_file_label mpfl on mpfl.meeting_item_tid = mpa.agenda_tid and mpfl.file_name = f.filename
left outer join esp.T_MP_FILE_SUPPL mpfls on mpfls.meeting_item_tid = mpa.agenda_tid and mpfls.file_name = f.filename
left outer join esp.t_file_rel fr on fr.file_tid = f.file_tid and fr.rel_tid = -100
left outer join esp.t_file_rel fannot on fannot.rel_tid = -102
and fannot.target_value = ESP.user_code()
and fannot.file_tid = mpf.file_tid
left outer join esp.t_file fa on fa.file_tid = fannot.target_tid
and current_date < date(fa.creation_timestamp)  + mpt.anno_exp
inner join esp.t_mp_file_order mpo on mpf.meeting_item_tid = MPO.AGENDA_TID and f.filename= mpo.filename
inner join esp.t_mp_meeting mpm2 on mpm2.meeting_tid = mpa.meeting_tid
inner join esp.t_mp_type mpt2 on mpt2.TYPE_TID = mpm2.TYPE_TID
left outer join esp.t_user_request u on U.REQUEST_ID = 'FILE_TID:' ||f.file_tid
and u.user_id = ESP.user_code()
where mpm.meeting_tid = :in_meeting_tid and mpm2.meeting_tid = :in_meeting_tid
  and
(:in_agenda_tid = -1 or mpf.meeting_item_tid =  :in_agenda_tid)
order by mpa.ord, mpo.ord, history, mpf.file_tid desc
;


--
-- KEY         = zz-meeting_agenda_list_for_meeting
-- 

select AGENDA_TID,
       MEETING_TID,
       ORD,
       replace(NAME, '{{', '@') "name",
       CR_ON,
       CMT_ON,
       ACCESS_RIGHT_TID,
       CMT,
       UPDATED,
       ''                       cr_list,
       ANSWER,
       HAS_DOCUMENTS,
       ITEM_TYPE,
       PARENT_ITEM_TYPE,
       MEETING_STATUS_TID,
       UNREAD
from esp.v_mp_agenda_complete ag
where 1 = 1
  and meeting_tid = cast(:in_meeting_tid as numeric)
  and (parent_item_type = 'M'
    or (parent_item_type = 'CR' and (
            item_type = 'CR'
            or
            (item_type = 'DOC' and has_documents = 'Y')
        )
           )
    )
order by ord
;



--
-- KEY         = zz-notification_email
--

select (
           select string_agg(external_mail, ', ' order by external_mail) as adr_list
           from (
                    select external_mail
                    from esp.v_cr_read_access_all
                    where meeting_tid = :in_meeting_tid
                      and R1 = 'read'
                      and access_ok = 1
                      and readOnly = 'false'
                      and external_mail > ' ') m
       )           mail_to,
       (
           select string_agg(external_mail, ', ' order by external_mail) as adr_list
           from (
                    select external_mail
                    from esp.v_cr_read_access_all x
                    where meeting_tid = :in_meeting_tid
                      and ((esp.is_null(R1) and R2 = 'admin' and external_mail > ' ')
                        or (R1 = 'read' and readonly = 'true' and access_ok = 1))) m1
       )           cc,
       'TEST BODY' body
;


--
-- KEY         = meeting_read_type_roles
--

select role_name, access_right, li.link || chr(38) || 'accessRightParam=' || role_name as link
from (
         select distinct role_name, min(access_right) access_right
         from (
                  select role_name, 'admin' access_right
                  from esp.t_access_spec_input
                  where filetype_nme =
                        (
                            select mpt.type_id
                            from esp.t_mp_type mpt
                            where mpt.type_tid = :in_type_tid
                        )
                    and role_name not like 'ESP_BP_ALL%'
                    and access_right in ('admin', 'upload', 'approve')
                  union all
                  select role_name, 'read' access_right
                  from esp.t_access_spec_input
                  where filetype_nme =
                        (
                            select mpt.type_id
                            from esp.t_mp_type mpt
                            where mpt.type_tid = :in_type_tid
                        )
                    and role_name not like 'ESP_BP_ALL%'
                    and access_right in ('read', 'view')
              ) r
         group by role_name
     ) a
         left join esp.t_access_role_link li
                   on li.role = a.role_name
;




--
-- KEY = MP_TYPE.select
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
--

set-if-empty:name=%
;
set-if-empty:typeId=%
;
set-if-empty:roleReader=%
;
set-if-empty:roleAdmin=%
;
set-if-empty:mpType=%
;
select TYPE_TID,
       TYPE_ID,
       NAME,
       ROLE_READER,
       substring(lr.link from 0 for 30) LINK_READER,
       ROLE_ADMIN,
       substring(la.link from 0 for 30) LINK_ADMIN,
       MP_TYPE
from (
         select type_tid,
                type_id,
                name,
                coalesce((select string_agg (role_name, ',' order by role_name)
                          from esp.t_access_spec_input
                          where filetype_nme = mpt.type_id
                            and access_right in ('read', 'reader', 'view')
                            and role_name <> 'ESP_BP_ALL_ADMIN'
                            and role_name like '%READER'
                         ), 'none') as role_reader
                 ,
                coalesce((select string_agg (role_name, ',' order by role_name)
                          from esp.t_access_spec_input
                          where filetype_nme = mpt.type_id
                            and access_right in ('admin', 'approve', 'upload')
                            and role_name <> 'ESP_BP_ALL_ADMIN'
                            and role_name like '%ADMIN'
                         ), 'none') as role_admin
                 ,
                mpt.item_type       as MP_TYPE
         from esp.t_mp_type mpt
         where type_id <> 'PERSONAL_DOC'
     ) t
         left join ESP.T_ACCESS_ROLE_LINK lr on lr.role = ROLE_READER
         left join ESP.T_ACCESS_ROLE_LINK la on la.role = ROLE_ADMIN
where (ROLE_READER like :roleReader or ROLE_READER is null)
  and (ROLE_ADMIN like :roleAdmin or ROLE_ADMIN is null)
  and NAME like :name
  and TYPE_ID like :typeId
  and (MP_TYPE like :mpType or MP_TYPE is null)
order by NAME
;


--
-- KEY = MP_TYPE.get
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
--

select sub.type_tid,
       sub.type_id,
       sub.name,
       sub.role_reader,
       lr.link link_reader,
       sub.role_admin,
       la.link link_admin,
       sub.mp_type
from (select type_tid,
             type_id,
             name,
             (select STRING_AGG(role_name, ',' order by role_name)
              from esp.t_access_spec_input
              where filetype_nme = mpt.type_id
                and access_right in ('read', 'reader', 'view')
                and role_name <> 'ESP_BP_ALL_ADMIN'
                and role_name like '%READER') as role_reader
              ,
             (select STRING_AGG(role_name, ',' order by role_name)
              from esp.t_access_spec_input
              where filetype_nme = mpt.type_id
                and access_right in ('admin', 'approve', 'upload')
                and role_name <> 'ESP_BP_ALL_ADMIN'
                and role_name like '%ADMIN')
                                                 role_admin
              ,
             mpt.item_type                    as mp_type
      from esp.t_mp_type mpt
      where type_id <> 'PERSONAL_DOC') sub
         left join esp.t_access_role_link lr on lr.role = sub.role_reader
         left join esp.t_access_role_link la on la.role = sub.role_admin
where TYPE_TID = :typeTid
;


--
-- KEY = MP_TYPE.log
-- ACCESS_GROUPS = SYSTEM
-- 

create-tid-for:logTid
;
insert into ESP.T_LOG
(LOG_TID,
 USER_ID,
 ERR_SEVERITY,
 ERR_SP_NAME,
 ERR_ORIGIN,
 SESSION_ID,
 ERR_MESSAGE,
 ERR_TIMESTAMP,
 VERSION)
values (:logTid,
        :$USERID,
        'I',
        'MP_TYPE.' || :crudMode,
        :$USERID,
        -47432,
        :crudMode || ' for type id: ' || :typeId || ' with name: ' || :name || ' for roles Reader: ' || :roleReader ||
        ' Admin: ' || :roleAdmin,
        current_timestamp,
        '1')
;


--
-- KEY = MP_TYPE.logSelect
-- ACCESS_GROUPS = ESP_ADMIN
--

select *
from ESP.T_LOG
where SESSION_ID = -47432
order by LOG_TID desc
;


--
-- KEY = MP_TYPE.insert
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
-- 

[
"create-tid-if-empty-for:$REQUESTID"
,
{
    'switch' : "select TYPE_ID as VALUE from ESP.T_MP_TYPE where TYPE_ID = :typeId",
    'default' : {
      'userMessage' : 'Warning:--- Record already exists! ---'
    },
    'null' : "call ESP.SP_MP_TYPE_NEW(:typeId, :name, :roleReader, :linkReader, :roleAdmin, :linkAdmin, :mpType, :$REQUESTID) ; set:crudMode=insert"
},
"serviceId:CreateMPMessages" ]



--
-- KEY = MP_TYPE.update
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
--

create-tid-if-empty-for:$REQUESTID
;
call ESP.SP_MP_TYPE_UPDATE(:typeTid, :name, :roleReader, :linkReader, :roleAdmin, :linkAdmin, :$REQUESTID)
;
set:crudMode=update
;
serviceId:CreateMPMessages
;
--
-- KEY = meeting_item_reorder
--

call ESP.sp_mp_item_reorder( :in_meeting_item_tid, :in_item_list)


--
-- KEY           = MP_TYPE.GD.select
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
--

set-if-empty:name=%
;
select TYPE_TID, NAME, ORD
from ESP.T_MP_TYPE
where
ITEM_TYPE = 'GD'
and
NAME like :name
order by ORD
;


--
-- KEY           = MP_TYPE.GD.get
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
--

set-if-empty:name=%
;
select TYPE_TID, NAME, ORD from ESP.T_MP_TYPE
where
TYPE_TID = :typeTid
order by ORD
;


--
-- KEY           = MP_TYPE.GD.update
-- ACCESS_GROUPS = ESP_ADMIN,ESP_SUPPORT
--

update
ESP.T_MP_TYPE
set ORD = :ord
where
TYPE_TID = :typeTid
;


--
-- KEY   = meeting.getMaxFolder
--

select max(meeting_tid) generalsection_tid_new from esp.t_mp_meeting
;





--
-- KEY           = MP_MESSAGE.get
--

set-if-empty:messageTid=-1
;
select MESSAGE_TID, LOG_TIMESTAMP, USER_ID, MESSAGE_TYPE, MESSAGE
from ESP.T_MP_MESSAGE
where MESSAGE_TID = :messageTid
order by MESSAGE_TID
;


--
-- KEY           = CreateMPMessages
-- 

java:com.swissre.esp.service.queryServices.CreateMPMessages
;


--
--
-- KEY           = MAIL.selectUnsent
-- ACCESS_GROUPS = SYSTEM
--

select MAIL_TID, RECIPIENT, CC_RECIPIENT, SUBJECT, TEXT, 'N' as ENCRYPTION ,SENT from ESP.T_MAIL
where SENT is null
;



--
-- KEY      = admin_get_types
--

select t.mtgroup_tid, mtg.name as mtg_name, t.type_tid, t.name, t.item_type, mtg.ord as mtgroup_ord, mtgi.ord as type_ord
from esp.v_mp_type t
         inner join esp.t_mp_mtgroup mtg on mtg.mtgroup_tid = t.mtgroup_tid
         inner JOIN esp.t_mp_mtgroup_input mtgi
                    ON t.type_tid = mtgi.type_tid
where t.access_right_tid = 6
order by mtg.ord, mtgi.ord
;




--
-- KEY      = meeting_detail
--

select mtg.mtgroup_tid
     , mtg.name                              mtgroup_name
     , mpt.type_tid
     , mpt.name                              "type_name"
     , mpm.meeting_tid
     , MPM.NAME                              meeting_name
     , mpm.status_tid
     , mpm.location_city
     , mpm.location_address
     , location_room
     , to_char(time_start, 'YYYYMMDDHH24MI') "time_start"
     , to_char(time_end, 'YYYYMMDDHH24MI')   "time_end"
     , mpa.access_right_tid
     , a.agenda_count
     , unread.unread
     , mpm.item_type
from esp.t_mp_type mpt
         inner join esp.t_mp_mtgroup mtg on mtg.mtgroup_tid = mpt.mtgroup_tid
         inner join esp.t_mp_meeting mpm on mpm.type_tid = mpt.type_tid
         inner join esp.v_mp_access_type mpa on mpt.type_tid = mpa.type_tid
    and mpa.user_id = esp.user_code()
         LEFT OUTER JOIN (SELECT meeting_tid, COUNT(*) agenda_count
                          FROM esp.t_mp_agenda
                          GROUP BY meeting_tid) a
                         ON a.meeting_tid = mpm.meeting_tid
         left outer join
     (select a.meeting_tid, count(*) unread
      from esp.t_mp_file f
               inner join esp.t_mp_agenda a on F.MEETING_ITEM_TID = a.agenda_tid
               left outer join esp.t_user_request u
                               on (U.REQUEST_ID = 'FILE_TID:' || f.file_tid and u.user_id = esp.user_code())
      where u.user_id is null
      group by a.meeting_tid) unread on unread.meeting_tid = MPM.MEETING_TID
         LEFT OUTER JOIN esp.t_cr_mp_link mpl
                         ON mpl.meeting_item_tid = mpm.meeting_tid
where mpm.meeting_tid = :in_meeting_tid
;


--
-- KEY      = folder_documents
-- 

WITH access_rights AS (
    SELECT type_tid, access_right_tid
    FROM esp.v_mp_access_type_all
    WHERE user_id = esp.user_code()
      AND access_right_tid >= 4
),
user_access AS (
    SELECT access_right_tid
    FROM esp.v_mp_access_type
    WHERE type_tid = :in_folder_tid
      AND user_id = esp.user_code()
)
SELECT mpt.type_tid                              AS meeting_tid,
       mpf.meeting_item_tid                      AS agenda_tid,
       coalesce(fannot.target_tid, mpf.file_tid) AS FILE_TID,
       f.owner,
       f.filename                                AS filename,
       mpfl.label                                AS label,
       to_char(creation_timestamp, 'DD-MON-YYYY HH24:MI') AS created,
       fr.target_value                           AS pages,
       mpo.ord,
       CASE
           WHEN (lead(f.filename, 1) OVER (ORDER BY mpm.meeting_tid, mpf.meeting_item_tid, mpo.ord, mpf.file_tid) = f.filename
                 AND lead(mpf.meeting_item_tid, 1) OVER (ORDER BY mpm.meeting_tid, mpf.meeting_item_tid, mpo.ord, mpf.file_tid) = mpf.meeting_item_tid)
           THEN 'hist'
           ELSE 'nohist'
       END AS history,
       mpm.name                                  AS agenda_name,
       mpt.name                                  AS meeting_name,
       'General Documents'                       AS type_name,
       CASE
           WHEN u.user_id IS NULL THEN 'unread'
           ELSE 'read'
       END AS unread,
       CASE
           WHEN mpm.status_tid = 11 THEN 'A'
           WHEN mpm.status_tid = 1 THEN 'D'
           WHEN mpm.status_tid = 10 THEN 'P'
           ELSE 'P'
       END AS status,
       CASE
           WHEN fannot.target_tid IS NOT NULL THEN 'Y'
           ELSE 'N'
       END AS annotated,
       (SELECT access_right_tid FROM user_access) AS access_tid,
       coalesce(suppl.suppl_flag, 'N')           AS suppl_flag
FROM esp.t_mp_file mpf
         INNER JOIN esp.t_file f ON f.file_tid = mpf.file_tid
         INNER JOIN esp.t_mp_meeting mpm ON mpm.meeting_tid = mpf.meeting_item_tid
         INNER JOIN access_rights ar ON ar.type_tid = mpm.type_tid
         LEFT OUTER JOIN esp.t_mp_file_label mpfl ON mpfl.meeting_item_tid = mpm.meeting_tid AND mpfl.file_name = f.filename
         LEFT OUTER JOIN esp.t_file_rel fr ON fr.file_tid = f.file_tid AND fr.rel_tid = -100
         LEFT OUTER JOIN esp.t_file_rel fannot ON fannot.rel_tid = -102 AND fannot.target_value = ESP.user_code() AND fannot.file_tid = mpf.file_tid
         INNER JOIN esp.t_mp_file_order mpo ON mpf.meeting_item_tid = mpo.agenda_tid AND f.filename = mpo.filename
         LEFT OUTER JOIN esp.t_mp_file_suppl suppl ON suppl.meeting_item_tid = mpo.agenda_tid AND suppl.file_name = mpo.filename
         INNER JOIN esp.t_mp_type mpt ON mpt.type_tid = mpm.type_tid
         LEFT OUTER JOIN esp.t_user_request u ON u.request_id = 'FILE_TID:' || f.file_tid AND u.user_id = ESP.user_code()
WHERE mpt.type_tid = :in_folder_tid
ORDER BY mpo.ord, history, FILE_TID DESC;


--
-- KEY      = folder_documents_download
-- 

select f.FILE_TID,
       mpm.NAME as AGENDA_NAME,
       mpt.NAME as MEETING_NAME,
       f.DIRECTORY,
       f.FILENAME,
       mpm.NAME    AGENDA_NAME,
       mpt.NAME    MEETING_NAME
from ESP.T_MP_FILE mpf
         left join ESP.T_FILE f on f.FILE_TID = mpf.FILE_TID
         left join ESP.T_MP_MEETING mpm on mpm.MEETING_TID = mpf.MEETING_ITEM_TID
         left join ESP.T_MP_TYPE mpt on mpt.TYPE_TID = mpm.TYPE_TID
where mpt.TYPE_TID = :typeTid
  and (select count(*)
       from esp.v_mp_access_type_all i
       where i.type_tid = mpt.type_tid
         and i.access_right_tid >= 4
         and i.user_id = esp.user_code()) > 0
order by mpm.NAME, f.FILE_TID, f.FILENAME
;



--
-- KEY      = subfolder_documents
-- 

select mpt.type_tid meeting_tid,
mpf.meeting_item_tid agenda_tid,
mpf.file_tid, f.owner,
f.filename filename,
mpfl.label label,
to_char(creation_timestamp, 'DD-MON-YYYY HH24:MI') created, fr.target_value pages, mpo.ord,
case when ( lead( f.filename, 1) over (order by mpm.meeting_tid, mpf.meeting_item_tid, mpo.ord, mpf.file_tid ) = f.filename
and
lead( mpf.meeting_item_tid, 1) over (order by mpm.meeting_tid, mpf.meeting_item_tid, mpo.ord, mpf.file_tid ) =  mpf.meeting_item_tid
) then 'hist' else 'nohist' end  history
, mpm.name agenda_name
, mpt.name meeting_name
, 'General Documents' type_name
, case when u.user_id is null then 'unread' else 'read' end unread
, case when mpm.status_tid = 11 then 'A' when mpm.status_tid = 1 then 'D' when mpm.status_tid = 10 then 'P' else 'P' end status
, case when fannot.target_tid is not null then 'Y' else 'N' end annotated
, coalesce(suppl.suppl_flag,'N') suppl_flag
from esp.t_mp_file mpf
inner join esp.t_file f on f.file_tid = mpf.file_tid
inner join esp.t_mp_meeting mpm on mpm.meeting_tid = mpf.meeting_item_tid
left outer join esp.t_mp_file_label mpfl on mpfl.meeting_item_tid = mpm.meeting_tid and mpfl.file_name = f.filename
left outer join esp.t_file_rel fr on fr.file_tid = f.file_tid and fr.rel_tid = -100
left outer join esp.t_file_rel fannot on fannot.rel_tid = -102
and fannot.target_value = ESP.user_code()
and fannot.file_tid = mpf.file_tid
inner join esp.t_mp_file_order mpo on mpf.meeting_item_tid = MPO.AGENDA_TID and f.filename= mpo.filename
left outer join esp.t_mp_file_suppl suppl on suppl.meeting_item_tid = mpo.agenda_tid
and suppl.file_name = mpo.filename
inner join esp.t_mp_type mpt on MPT.TYPE_TID = MPM.TYPE_TID
left outer join esp.t_user_request u on U.REQUEST_ID = 'FILE_TID:' ||f.file_tid
and u.user_id = ESP.user_code()
where  mpm.meeting_tid = :in_subfolder_tid
order by ord, history, file_tid desc


--
-- KEY           = AnnotationDeleteByUserPref
-- ACCESS_GROUPS = SYSTEM
--

delete from ESP.T_FILE where FILE_TID in
                             (
                                 select f.FILE_TID
                                 from ESP.T_USER_PREF u, ESP.T_FILE f, ESP.T_FILE f2
                                 where
                                     f.DIRECTORY = '/.derived/' || f2.FILE_TID || '/'  || u.USER_ID || '/'
                                   and
                                     u.PREF_NAME = 'ANNOTATION_STORE_DURATION'
                                   and
                                     f.CREATION_TIMESTAMP < current_date - cast(coalesce(u.PREF_VALUE, '9999') as integer))
;


--
-- KEY           = AnnotationDownload
--

java:com.swissre.esp.service.queryServices.AnnotationDownload



--
-- KEY           = MAIL.addAttachment
-- ACCESS_GROUPS = SYSTEM
--

insert INTO ESP.T_MAIL_ATTACHMENT (MAIL_TID, FILE_TID, INDX)
values
(:mailTid,:fileTid,:indx)



--
-- KEY           = MAIL.getAttachments
-- ACCESS_GROUPS = SYSTEM
--

select f.FILE_TID, f.FILENAME, f.DIRECTORY, f.OWNER
from
ESP.T_MAIL_ATTACHMENT a,
ESP.T_FILE f
where
a.MAIL_TID = :mailTid
and
a.FILE_TID = f.FILE_TID



--
-- KEY           = MAIL.setAsSent
-- ACCESS_GROUPS = SYSTEM
--

update ESP.T_MAIL set SENT = current_timestamp
where SENT is null and MAIL_TID = :mailTid



--
-- KEY           = MAIL.insertMail
-- ACCESS_GROUPS = SYSTEM
--

insert INTO ESP.T_MAIL
(MAIL_TID, RECIPIENT, CC_RECIPIENT, SUBJECT, TEXT,TYPE, ENCRYPTION, RECORD_CREATED)
values
    (:mailTid,:recipient,:ccRecipient,:subject,:text,:type,:encryption,CURRENT_DATE);

--
-- KEY           = StartBuId
-- ACCESS_GROUPS = SYSTEM
--

SELECT START_BU FROM ESP.V_ACCESS_NAVIGATION_BUTTONS
;


--
-- KEY           = TempDocumentUpload
--

java:com.swissre.esp.service.queryServices.TempDocumentUpload
