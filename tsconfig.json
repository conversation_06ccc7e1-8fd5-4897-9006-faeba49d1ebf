{"compilerOptions": {"skipLibCheck": true, "moduleResolution": "Node", "resolveJsonModule": true, "module": "commonjs", "target": "es2021", "declaration": true, "outDir": "./dist", "baseUrl": "./src", "esModuleInterop": true, "typeRoots": ["./types", "./node_modules/@types"], "noImplicitAny": true, "noUnusedLocals": true, "strict": false, "types": ["node", "jest"]}, "include": ["src/**/*.ts", "test/**/*.test.ts"], "exclude": ["node_modules", "src/__tests__"]}