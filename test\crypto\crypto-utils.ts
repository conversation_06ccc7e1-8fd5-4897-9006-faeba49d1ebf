import fs from 'fs';
import path from 'path';
const inputdir = 'C:\\srdev\\tmp\\java-vs-note-enc';

export type Metadata = { fileId: number; filename: string; encryptionKey: string; sha1hash: string };

export function getNrOfFiles() {
  return fs.readdirSync(inputdir).length;
}

export function getMeta(index: number) {
  const files = fs.readdirSync(inputdir);
  const filename = files.find((e) => e.startsWith(`${index}-meta-`));
  if (!filename) {
    console.error(`No file found ${index}`);
    return;
  }
  const str = fs.readFileSync(path.join(inputdir, filename)).toString('utf8');
  return JSON.parse(str) as Metadata;
}

export function getEncrContent(index: number) {
  const files = fs.readdirSync(inputdir);
  const filename = files.find((e) => e.startsWith(`${index}-encr-`));
  return fs.readFileSync(path.join(inputdir, filename));
}

export function getOrigContent(index: number) {
  const files = fs.readdirSync(inputdir);
  const filename = files.find((e) => e.startsWith(`${index}-orig-`));
  return fs.readFileSync(path.join(inputdir, filename));
}
