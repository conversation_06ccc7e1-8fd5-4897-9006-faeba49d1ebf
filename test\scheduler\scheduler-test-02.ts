import { endTest, getTestSc } from '../init-local';
import { getLock, isLocked, releaseLock } from '../../src/service/lib/SyncService';
import { expect } from 'chai';
import LoggerCenter from '../../src/logger/LoggerCenter';
import path from 'path';
import { stopAllJobs } from '../../src/service/scheduler-utils';

const logger = LoggerCenter.getLogger(path.basename(__filename));

const DESCR = 'scheduler test 02 (locking)';

describe(DESCR, async () => {
  const lock = `TESTLOCK-${Date.now()}`;
  const details = 'Just a test lock...';
  before(() => {
    getTestSc();
  });

  after(async () => {
    await releaseLock(lock);
    logger.info(`after ${DESCR}`);
    await endTest();
  });

  it(`get lock: ${lock}`, async () => {
    let ok = await isLocked(lock);
    expect(ok).false;
    ok = await getLock(lock, details);
    expect(ok).true;
    ok = await getLock(lock, details);
    expect(ok).false;

    ok = await isLocked(lock);
    expect(ok).true;
    await releaseLock(lock);
    ok = await isLocked(lock);
    expect(ok).false;
    console.log(`DONE ${DESCR}`);
    stopAllJobs();
  });
});
