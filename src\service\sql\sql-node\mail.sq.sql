-- 
-- SERVICE_ID = esp.fiveMinuteTask
-- ROLES = SYSTEM
--

call ESP.SP_FIVE_MINUTE_TASK()
;
java:com.swissre.serviceQuery.lib.MailTask
;
delete from ESP.T_APP_PROPERTIES where NAME is null
;
include:esp.saveByLabel
;


--
-- SERVICE_ID = MAIL.getAddress
-- ROLES = SYSTEM
--
set-if-empty:userId=nobody
;
select email_address as email  from ESP.V_SR_USER a where  a.user_id = :userId
;

--
-- SERVICE_ID = MailServiceTester
-- ROLES = ESP_ADMIN
--
java:com.swissre.esp.service.queryServices.MailServiceTester
;