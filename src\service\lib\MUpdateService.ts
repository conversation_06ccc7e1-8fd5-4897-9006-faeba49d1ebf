/* eslint-disable */
import { oldNames } from './mobile/KeyProcessor2';
import { _hot_, _iesp, _temp_, hot_data, MOBILE_APP_ID, var_temp } from '../constants';
import { StringRecord } from '../../types';
import { DbFile } from './esp/types';
import { isSqExceptionResult, SqNodeFunction, SqResult } from '../../serviceQuery';
import LoggerCenter from '../../logger/LoggerCenter';
import path from 'path';
import ServiceCenter, { newTid } from '../ServiceCenter';
import { toSha1 } from './mobile/crypto-utils';
import {
  getDbFile,
  getDbFileByPath,
  getDbFileContent,
  getDbFileTag,
  getSHA1,
  insertSHA1,
  listCurrentFiles,
  saveDbFile,
  toPathString,
  updateRealLength
} from './upload-file-utils';
import { createSecureSalt } from './mobile/mobile-utils';
import fs from 'fs';
import { track } from './esp/DBLogUtils';
import { getLock, releaseLock } from './SyncService';
import { fixEscapeCharForJava } from '../../utils';

const logger = LoggerCenter.getLogger(path.basename(__filename));
const LOCK_NAME = 'M_UPDATE_LOCK';
export const ajustDirectory = (serviceId: string, userId: string) => {
  if (oldNames.includes(serviceId)) {
    return `/home/<USER>/`;
  } else {
    return hot_data;
  }
};

export const processUserServices = async (
  { userId, roles }: { userId: string; roles: string[] },
  parameters: StringRecord,
  serviceNames: string[]
): Promise<DbFile[]> => {
  const sc = ServiceCenter.getInstance();
  const sq = sc.getSq();
  const result: DbFile[] = [];
  logger.info('Start: processData ', logger);
  const directory = `/home/<USER>/`;

  for (const serviceId of serviceNames) {
    const newFile: DbFile = createNewFile(serviceId, directory);
    try {
      const pr0 = await sq.run({ serviceId, parameters: { ...parameters, $USERID: userId }, userId, roles });
      const pr = createSqResult(serviceId, userId, pr0);
      const newData = prepareData(pr);
      const newSHA1 = toSha1(newData);
      const oldFile = await getDbFileByPath(newFile.directory, newFile.filename);
      const updateNeeded = await checkUpdateNeeded(oldFile, newSHA1, newData);

      if (updateNeeded) {
        await handleUpdateNeeded(newFile, newData, oldFile, userId, sc, result);
      } else {
        logger.info(`No update needed (skip): ${toPathString(newFile)}`);
        result.push(oldFile);
      }
    } catch (e) {
      logger.error(`Error: ${toPathString(newFile)} : ${e}`);
    }
  }
  return result;
};

const createNewFile = (serviceId: string, directory: string): DbFile => {
  return {
    fileTid: '',
    directory,
    filename: serviceId + '.json',
    owner: MOBILE_APP_ID,
    creationTimestamp: Date.now().toString()
  };
};

const createSqResult = (serviceId: string, userId: string, pr0: any): SqResult => {
  return {
    name: serviceId,
    type: 'pagingResult',
    username: userId.toLowerCase(),
    size: pr0.table?.length ?? 0,
    totalCount: pr0.table?.length ?? 0,
    hasMore: false,
    table: pr0.table,
    header: pr0.header,
    rowsAffected: -1,
    dbExecutionTime: -1,
    dbFetchingTime: -1
  };
};

const prepareData = (pr: SqResult): Buffer => {
  const newJsonString0 = JSON.stringify(pr);
  const newJsonString = fixEscapeCharForJava(newJsonString0);
  return Buffer.from(newJsonString, 'utf8');
};

const checkUpdateNeeded = async (oldFile: DbFile | null, newSHA1: string, newData: Buffer): Promise<boolean> => {
  if (!oldFile) {
    return true;
  }

  const oldFileTid = oldFile.fileTid;
  let oldSHA1 = await getSHA1(oldFileTid);
  if (newSHA1 === oldSHA1) {
    return false;
  }

  const oldData = await getDbFileContent(oldFile.fileTid);
  if (isSqExceptionResult(oldData)) {
    logger.error(`Could not process file ${oldFile.fileTid} (${oldFile.filename})`);
    return true;
  }

  if (!oldSHA1) {
    oldSHA1 = toSha1(oldData);
    await insertSHA1(oldFile.fileTid, oldSHA1);
  }

  return !oldData.equals(newData);
};

const handleUpdateNeeded = async (
  newFile: DbFile,
  newData: Buffer,
  oldFile: DbFile | null,
  userId: string,
  sc: ServiceCenter,
  result: DbFile[]
) => {
  const pathId = await createPathIdValue(oldFile);
  newFile.tags = `pathId:${pathId}`;
  logger.info(`try to insert : ${toPathString(newFile)}`);
  const res = await saveDbFile({ ...newFile, data: newData });
  if (isSqExceptionResult(res)) {
    logger.error(`Failed saving file ${toPathString(newFile)}!`);
  } else {
    const newFile = await getDbFile(res.fileTid);
    logger.info(`Updated: ${toPathString(newFile)}`);
    result.push(newFile);
  }
  if (oldFile) {
    await sc.runSystemSq({
      serviceId: 'dbFile.deleteTomorrow',
      parameters: { $USERID: userId, fileTid: oldFile.fileTid }
    });
  }
};

export const processUserServices_old = async (
  { userId, roles }: { userId: string; roles: string[] },
  parameters: StringRecord,
  serviceNames: string[]
): Promise<DbFile[]> => {
  const sc = ServiceCenter.getInstance();
  const sq = sc.getSq();

  const result: DbFile[] = [];

  logger.info('Start: processData ', logger);

  const directory = `/home/<USER>/`;

  for (const serviceId of serviceNames) {
    const newFile: DbFile = {
      fileTid: '',
      directory,
      filename: serviceId + '.json',
      owner: MOBILE_APP_ID,
      creationTimestamp: Date.now().toString()
    };

    try {
      const pr0 = await sq.run({ serviceId, parameters: { ...parameters, $USERID: userId }, userId, roles });
      // create a Java compatible sqResult version
      if (serviceId === 'meeting_detail_allcr') {
        logger.info('debug...');
      }
      const pr: SqResult = {
        name: serviceId,
        type: 'pagingResult',
        username: userId.toLowerCase(),
        size: pr0.table?.length ?? 0,
        totalCount: pr0.table?.length ?? 0,
        hasMore: false,
        table: pr0.table,
        header: pr0.header,
        rowsAffected: -1,
        dbExecutionTime: -1,
        dbFetchingTime: -1
      };

      const newJsonString0 = JSON.stringify(pr);
      const newJsonString = fixEscapeCharForJava(newJsonString0);
      // TODO more conversions
      const newData = Buffer.from(newJsonString, 'utf8');
      // TODO compare toSha1 with java: SecurityUtils.sha1
      const newSHA1 = toSha1(newData);
      logger.info('Result of ' + serviceId + ' is of size: ' + newData.length);
      const oldFile = await getDbFileByPath(newFile.directory, newFile.filename);
      let updateNeeded = false;

      if (!oldFile) {
        updateNeeded = true;
      } else {
        const oldFileTid = oldFile.fileTid;

        let oldSHA1 = await getSHA1(oldFileTid);
        if (newSHA1 === oldSHA1) {
          updateNeeded = false;
        } else {
          const oldData = await getDbFileContent(oldFile.fileTid);
          if (isSqExceptionResult(oldData)) {
            logger.error(`Could not process file ${oldFile.fileTid} (${oldFile.filename})`);
            updateNeeded = true;
          } else {
            const oldJsonString = oldData.toString('utf8');
            logger.info(oldJsonString.substring(0, 128));
            if (!oldSHA1) {
              oldSHA1 = toSha1(oldData);
              await insertSHA1(oldFile.fileTid, oldSHA1);
            }
            logger.info('old:' + oldData.length + ' new:' + newData.length);
            updateNeeded = !oldData.equals(newData);
          }
        }
      }
      if (updateNeeded) {
        const pathId = await createPathIdValue(oldFile);
        newFile.tags = `pathId:${pathId}`;
        logger.info(`try to insert : ${toPathString(newFile)}`);

        const res = await saveDbFile({ ...newFile, data: newData });

        if (isSqExceptionResult(res)) {
          logger.error(`Failed saving file ${toPathString(newFile)}!`);
        } else {
          const newFile = await getDbFile(res.fileTid);
          logger.info(`Updated: ${toPathString(newFile)}`);
          result.push(newFile);
        }
        if (oldFile) {
          await sc.runSystemSq({
            serviceId: 'dbFile.deleteTomorrow',
            parameters: { $USERID: userId, fileTid: oldFile.fileTid }
          });
        }
      } else {
        logger.info(`No update needed (skip): ${toPathString(newFile)}`);
        result.push(oldFile);
      }
    } catch (e) {
      logger.error(`Error: ${toPathString(newFile)} : ${e}`);
    }
  }
  return result;
};

async function createPathIdValue(oldFile: DbFile) {
  if (oldFile) {
    const pathId = getDbFileTag(oldFile, 'pathId');
    if (pathId) {
      return pathId;
    }
  }
  return newTid();
}

export type SourceFile = { path: string; data: Buffer };

export async function cleanHotDirectory() {
  const sc = ServiceCenter.getInstance();
  logger.info('Start: cleanHotDirectory ');
  return sc.runSystemSq({ serviceId: 'm.cleanHotDirectory' });
}

export async function processSources(files: SourceFile[], force: boolean) {
  const sc = ServiceCenter.getInstance();
  logger.info('Start: processSources ');
  const randomDirPrefix = _temp_ + createSecureSalt(6);
  // const sourceDir = path.normalize(rootDirName);
  // const files: string[] = [];
  // listFiles(sourceDir, files);
  // logger.info(`Current number of files in the folder :  ${sourceDir} ${files.length} `);

  const currentDBFiles = await listCurrentFiles(_hot_);
  logger.info(
    `origin=MUpdateService; message='Number of files to check: ${files.length}. Current number of files in the DB : ${currentDBFiles.length}`
  );

  if (force) {
    await sc.runSystemSq({
      serviceId: 'dbFile.moveFileToDir',
      parameters: {
        targetDirectory: _temp_,
        prefixLen: _hot_.length + 1,
        prefixWildCard: _hot_ + '%'
      }
    });
  }

  for (const file of files) {
    const filename = path.basename(file.path);
    const directory = '/' + file.path.substring(0, file.path.length - filename.length);
    if (directory.includes('/data/')) {
      // skip
      continue;
    }

    logger.info(`Start compare for: ${directory}${filename}`);

    const oldDBFile = await getDbFileByPath(directory, filename);

    const updateNeeded = !(oldDBFile && (await compareDataToDbFile(file.data, oldDBFile.fileTid)));

    if (updateNeeded) {
      const newDbFile: DbFile = {
        filename,
        directory: randomDirPrefix + directory,
        fileTid: '',
        owner: MOBILE_APP_ID
      };
      newDbFile.tags = `pathId:${oldDBFile ? createPathIdValue(oldDBFile) : await newTid()}`;
      const savedDbFile = await saveDbFile({ ...newDbFile, data: file.data });
      if (isSqExceptionResult(savedDbFile)) {
        logger.warn(`Could not save: ${newDbFile.directory}${filename}!`);
        return;
      }

      logger.info(`New file created ${savedDbFile.directory}${filename}`);

      await updateRealLength(savedDbFile.fileTid);
      logger.info(`Updated done: ${savedDbFile.directory}${filename}`);
      await track(
        'I',
        `File update: ${savedDbFile.fileTid}, ${savedDbFile.directory} ${savedDbFile.filename} `,
        'MUpdateService'
      );
    } else {
      logger.info(`No update needed: ${directory}${filename}!`);
    }
  }
  //logger.info("Moves: " + dbfs.moveFilesToDir(randomDir, "/"), logger);
  //

  const moveRes2 = await sc.runSystemSq({
    serviceId: 'dbFile.moveFileToDir',
    parameters: {
      targetDirectory: _hot_,
      prefixLen: randomDirPrefix.length + 2,
      prefixWildCard: `${randomDirPrefix}%`
    }
  });
  logger.info(`Move to ${_hot_}, nr of files: ${moveRes2.rowsAffected}`);
  logger.info('End: processSources', logger);
}

export function listFiles(dir: string, results: string[]) {
  const files = fs.readdirSync(dir);

  files.forEach((file) => {
    const filePath = path.join(dir, file);
    const isDirectory = fs.statSync(filePath).isDirectory();

    if (isDirectory) {
      listFiles(filePath, results);
    } else {
      results.push(filePath);
    }
  });
}

async function compareDataToDbFile(data: Buffer, fileTid: string) {
  const dataSha1 = toSha1(data);
  let dbDataSha1 = await getSHA1(fileTid);
  if (dataSha1 === dbDataSha1) {
    return true;
  } else {
    const dbData = await getDbFileContent(fileTid);
    if (isSqExceptionResult(dbData)) {
      logger.error(`Could not read data for file. file_tid: ${fileTid} `);
      return false;
    } else {
      if (!dbDataSha1) {
        dbDataSha1 = toSha1(dbData);
        await insertSHA1(fileTid, dbDataSha1);
      }
      return data.equals(dbData);
    }
  }
}

export const MUpdateService: SqNodeFunction = async function ({ request }): Promise<SqResult> {
  const force = (request.parameters['force'] || '').toString();
  const rootDir = (request.parameters['$ROOTDIR'] || '').toString();
  const sourceDir = path.join(rootDir, 'mp');
  if (fs.existsSync(sourceDir)) {
    const exception = `No directory found: ${sourceDir}`;
    logger.error(exception);
    return { exception };
  }

  try {
    logger.info('Start Time : ' + new Date());
    const lock = getLock(LOCK_NAME, '::force: ' + force + '');
    if (lock) {
      await ServiceCenter.getInstance().runSystemSq({
        serviceId: 'dbFile.deleteOldFiles',
        parameters: { directory: var_temp }
      });

      // await processSources(sourceDir, force === 'true');
    } else {
      return { exception: 'M_UPDATE_LOCK not available, please wait' };
    }
  } finally {
    await releaseLock(LOCK_NAME);
  }
  return { header: ['message'], table: [['Everything is ok']] };
};
