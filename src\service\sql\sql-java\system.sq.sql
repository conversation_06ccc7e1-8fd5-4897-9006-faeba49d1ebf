--
-- KEY           = newTid.create
-- ACCESS_GROUPS = SYSTEM
--  

select nextval('esp.seq_global_tid')
;

--
-- KEY           = SendData
--

set:logServiceId=esp.usage.log
;
java:com.swissre.serviceQuery.lib.SendData
;

--
-- 
-- KEY           = sync.select
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

set-if-empty:nme=%
;
select NME, TO_CHAR(TME, 'YYYY-MM-DD HH24:MI') AS "CREATION_TIME", DETAILS
from ESP.T_SYNC
where NME like :nme
;

--
-- 
-- KEY           = sync.get
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

select nme, TO_CHAR(TME, 'YYYY-MM-DD HH24:MI') AS "CREATION_TIME", DETAILS
from ESP.T_SYNC
where NME = :nme
;


--
-- 
-- KEY           = sync.insert
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

insert into ESP.T_SYNC (NME, TME, DETAILS)
values (:nme, current_timestamp, :details)
;




--
-- 
-- KEY           = sync.delete
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

delete from ESP.T_SYNC where NME = :nme
;

--
-- 
-- KEY           = sync.deleteAll
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

delete from ESP.T_SYNC where nme is not null;
;


--
-- 
-- KEY           = Tests
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

java:com.swissre.serviceQuery.lib.Tests



--
-- 
-- KEY           = RefreshCachedData
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

java:com.swissre.serviceQuery.lib.RefreshCachedData
;

--
-- 
-- KEY           = RequestArrayProcessor
--

java:com.swissre.serviceQuery.lib.RequestArrayProcessor
;

--
-- KEY          = JsonToParameters
--

java:com.swissre.serviceQuery.lib.JsonToParameters



--
-- KEY           = SystemProperties
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

java:com.swissre.serviceQuery.lib.SystemProperties


--


-- --

-- create table ESP.T_SERVICEQUERIES_COUNTER (
--      "KEY" varchar(256),
--      "COUNTER" number default 0,
--      "LAST_CALL" number,
--  primary key ("KEY")
-- )
-- 
-- grant all ON ESP.T_SERVICEQUERIES_COUNTER to ESP_TRX;


--
-- KEY           = ServiceQueryCounter.select
-- ACCESS_GROUPS = ESP_ADMIN
--

set-if-empty:key=%
;
set-if-empty:counter=0
;
set-if-empty:query=%
;
set-if-empty:accessGroups=%
;
set-if-empty:sqFile=%
;
select t.KEY, t.COUNTER, t.SQ_FILE, t.LAST_CALL, t.LAST_LOAD, t.QUERY, t.ACCESS_GROUPS from 
(
	select 
	sq.KEY, 
	case coalesce(c.COUNTER , 0) 
		when 0 then 0 
		else c.COUNTER end as COUNTER,
	case coalesce(c.LAST_LOAD , -1)
		when -1 then 'N' 
		else 'Y' end as SQ_FILE,
    to_timestamp(c.LAST_CALL / 1000.0) AT TIME ZONE 'UTC' AS LAST_CALL,
    to_timestamp(c.LAST_LOAD / 1000.0) AT TIME ZONE 'UTC' AS LAST_LOAD,
	sq.QUERY,
	sq.ACCESS_GROUPS,
	sq.TAGS
	from ESP.T_SERVICEQUERIES sq 
	left join ESP.T_SERVICEQUERIES_COUNTER c   on c.KEY = sq.KEY
) t
where 
	t.COUNTER >= :counter
and 
(
	(t.QUERY like :query and t.QUERY is not null)
	or 
	((:query = '%' or :query = '%%') and t.QUERY is null)
)
and 
	t.SQ_FILE like :sqFile
and 
(
	(upper(t.ACCESS_GROUPS) like upper(:accessGroups) and t.ACCESS_GROUPS is not null)
	or 
	((:accessGroups = '%' or :accessGroups = '%%') and t.ACCESS_GROUPS is null)
)
and 
	t.KEY like :key
order by t.KEY
;


--
-- KEY           = ServiceQueryCounter.get
-- ACCESS_GROUPS = ESP_ADMIN
--

select t.KEY, t.COUNTER,  t.LAST_CALL, t.LAST_LOAD, t.QUERY, t.ACCESS_GROUPS from
(
	select 
	sq.KEY,
    c.COUNTER,
	case coalesce(c.COUNTER , 0)
		when 0 then 0 
		else c.COUNTER end as "COUNTER",
	case coalesce(c.LAST_LOAD , -1)
		when -1 then 'N' 
		else 'Y' end as "SQ_FILE",
	c.LAST_CALL, 
	c.LAST_LOAD, 
	sq.QUERY, 
	sq.ACCESS_GROUPS,
	sq.TAGS
	from ESP.T_SERVICEQUERIES sq 
	left join ESP.T_SERVICEQUERIES_COUNTER c   on c.KEY = sq.KEY
) t
where t.KEY = :key
;

--
-- KEY           = ServiceQueryCounter.update2
-- ACCESS_GROUPS = ESP_ADMIN
--

update ESP.T_SERVICEQUERIES_COUNTER
set COUNTER = :counter
where
KEY = :key
;



--
-- KEY           = fiveMinuteTask
-- ACCESS_GROUPS = SYSTEM
--

call ESP.SP_FIVE_MINUTE_TASK()



--
-- KEY           = fourThirtyTask
-- ACCESS_GROUPS = SYSTEM
--

call ESP.SP_FOUR_THIRTY_TASK()
;
serviceId:AnnotationDeleteByUserPref
;
include:esp.saveByLabel
;


--
-- KEY           = fiveThirtyTask
-- ACCESS_GROUPS = SYSTEM
--

call ESP.SP_FIVE_THIRTY_TASK()
;
include:esp.saveByLabel
;

--
-- KEY           = ClearDataCache
-- INFO 		 = Expects a parameter clearForServiceId

set-if-empty:clearForServiceId=dummy
;
java:com.swissre.serviceQuery.lib.ClearDataCache



--
-- KEY           = client_properties
--
 
select "NAME", VALUE from ESP.T_APP_PROPERTIES where "NAME" in ('env','envTitle')
;


--
-- KEY           = ServiceQuery.deleteAll
-- ACCESS_GROUPS = SYSTEM
--

delete from ESP.T_SERVICEQUERIES where TAGS != 'PROTECTED' or TAGS is null
;
