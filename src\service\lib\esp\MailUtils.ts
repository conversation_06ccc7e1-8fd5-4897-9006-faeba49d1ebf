import { Sq<PERSON><PERSON><PERSON>unction, SqResult, toList, toFirst } from '../../../serviceQuery';
import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';
import ServiceCenter from '../../ServiceCenter';
import { SYSTEM } from '../../constants';
import { DbFile, MailUnSent } from './types';
import { sendWithDBFile } from '../EMailService';

const fromEmailAddress = process.env.FROM_EMAIL_ADDRESS;
const logger = LoggerCenter.getLogger(path.basename(__filename));

async function getUnsentMails(): Promise<MailUnSent[] | undefined> {
  const sq = ServiceCenter.getInstance().getSq();
  const res = await sq.run({
    serviceId: 'MAIL.selectUnsent',
    parameters: { },
    userId: SYSTEM,
    roles: [SYSTEM]
  });
  return toList<MailUnSent>(res);
}

async function setMailAsSent(mailTid: string): Promise<void> {
  const sq = ServiceCenter.getInstance().getSq();
  await sq.run({
    serviceId: 'MAIL.setAsSent',
    parameters: { mailTid },
    userId: SYSTEM,
    roles: [SYSTEM]
  });
}

async function getMailAddress(userId: string): Promise<{ email: string}> {
  const sq = ServiceCenter.getInstance().getSq();
  const res = await sq.run({
    serviceId: 'MAIL.getAddress',
    parameters: { userId },
    userId: SYSTEM,
    roles: [SYSTEM]
  });
  return toFirst<{ email: string }>(res);
}

async function getAttachments(mailTid: string): Promise<DbFile[] | undefined> {
  const sq = ServiceCenter.getInstance().getSq();
  const res = await sq.run({
    serviceId: 'MAIL.getAttachments',
    parameters: { mailTid },
    userId: SYSTEM,
    roles: [SYSTEM]
  });
  return toList<DbFile>(res);
}

export const MailTask: SqNodeFunction = async(): Promise<SqResult> => {
  // MailTask not yet implemented!
  logger.info('Sending mail by MailTask ');
  try {
    const unsentMails = await getUnsentMails();
    for (const mail of unsentMails) {
      logger.info(`Sending mail [${mail.mailTid}]: ${mail.subject} to ${mail.recipient} `);
      // await send(recipients, fromEmailAddress, subject.toString(), body.toString(), attachments);
      const attachments = await getAttachments(mail.mailTid.toString());
      const recipients: Array<string> = [];
      const receiver = mail.recipient?.toString();
      if (receiver?.indexOf('@') === -1) {
          const { email } = await getMailAddress(receiver);
          if (!email) {
            throw new Error(`No email found for user ${receiver}`);
          }
          recipients.push(email);
      }
      await sendWithDBFile(recipients, fromEmailAddress, mail.subject.toString(), mail.text.toString(), attachments, mail.text.toString());
      await setMailAsSent(mail.mailTid.toString());
    }
    return {};
  } catch (e) {
    logger.error('Error sending mail by MailTask ');
    logger.error(e);
  }

};

