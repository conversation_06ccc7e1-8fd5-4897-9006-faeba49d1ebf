import { spreadsheet2Requests, SqContext, SqRequest, SqResult } from '../../../serviceQuery';
import { getDbFileContent } from '../upload-file-utils';
import ServiceCenter from '../../ServiceCenter';

export async function XlsxSqProcessing({
  request: { parameters, userId, roles }
}: {
  request: SqRequest;
  context: SqContext;
}): Promise<SqResult> {
  const fileTid = +(parameters?.fileTid || 0);
  if (!fileTid) {
    return { exception: 'Missing or non numeric fileTid parameter!' };
  }
  const content = await getDbFileContent(fileTid.toString());

  const r0 = spreadsheet2Requests(content);
  const r = _postProcessing(r0);
  // test only
  if (parameters?.testOnly === 'true') {
    return { header: ['sheet', 'index', 'serviceId', 'parameters'], table: _toRequestTable(r) };
  }
  const tableHeader = ['sheet', 'index', 'rowsAffected', 'exception'];
  const tableResult: Array<[sheet: string, index: string, rowsAffected: string, exception: string]> = [];
  const sq = ServiceCenter.getInstance().getSq();
  for (const sheet of Object.keys(r)) {
    const sqRequests = r[sheet];
    let index = 0;
    for (const sqRequest of sqRequests) {
      const r = await sq.run({ ...sqRequest, userId, roles });
      tableResult.push([sheet, index.toString(), r.rowsAffected + '', '' + r.exception]);
      index++;
    }
  }

  return { table: tableResult, header: tableHeader };
}

type RequestTable = Array<[name: string, index: string, serviceId: string, parameters: string]>;

function _toRequestTable(r: Record<string, SqRequest[]>): RequestTable {
  const requestTable: RequestTable = [];

  Object.entries(r).forEach(([sheet, sqRequests]) =>
    sqRequests.forEach((sqRequest, index) =>
      requestTable.push([
        sheet,
        index.toString(),
        sqRequest.serviceId || '',
        JSON.stringify(sqRequest.parameters || {})
      ])
    )
  );
  return requestTable;
}

function _postProcessing(r: Record<string, SqRequest[]>): Record<string, SqRequest[]> {
  const result: Record<string, SqRequest[]> = {};

  Object.entries(r).forEach(([sheet, sqRequests]) =>
    sqRequests.forEach((sqRequest) => {
      result[sheet] = result[sheet] || [];
      const serviceId = (
        (sqRequest.parameters || {})['$SQCOMMANDS'] ||
        (sqRequest.parameters || {})['serviceId'] ||
        sqRequest.serviceId
      ).toString();
      result[sheet].push({ ...sqRequest, serviceId });
    })
  );
  return result;
}
