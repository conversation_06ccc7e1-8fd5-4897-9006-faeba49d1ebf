/* eslint-disable no-console,space-before-function-paren */
import ServiceCenter from '../ServiceCenter';
import { singleValue } from '../../serviceQuery';
import LoggerCenter from '../../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export function processSessionMonitor(message: string, userId: string, origin: string) {
  logger.info(`origin=${origin}; userId=${userId}; message=${message}`);
}

export const isAdminFor = async (tid: string, userId: string): Promise<boolean> => {
  const sc = ServiceCenter.getInstance();
  const isAdmin = singleValue(
    await sc.runSystemSq({
      serviceId: 'isAdminFor',
      parameters: { $USERID: userId, tid }
    })
  );
  return +isAdmin >= 1;
};

export const getMeetingStatus = async (meetingTid: string): Promise<string> => {
  const sc = ServiceCenter.getInstance();
  return singleValue(
    await sc.runSystemSq({
      serviceId: 'get_meeting_status',
      parameters: { meetingTid }
    })
  );
};

export const isPublished = async (meetingTid: string): Promise<boolean> => {
  const status = await getMeetingStatus(meetingTid);
  return status === '10' || status === '11';
};
