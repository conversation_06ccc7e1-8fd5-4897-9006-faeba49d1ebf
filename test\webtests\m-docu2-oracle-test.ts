import { test } from 'mocha';
import { localUrlJava, localUrlNode } from '../common';
import { runGetBlob } from './getkeys2-utils';
import { expect } from 'chai';
import { expectBufferEquals } from '../test-utils';

const localUrlNodeGetkeys2Sources = `${localUrlNode}serviceQuery/m.Getkeys2?sourcesOnly=true&name=sources`;

const docu2UrlJavaOracle = `${localUrlJava}mobile/docu2/`;
const docu2UrlNode = `${localUrlNode}mobile/docu2/`;

/*

*********
Important:
*********
So that the test is successful do the following:

Upload the same file to node and java/oracle
copy the file to the 'same' file tid,
  assuming file tid on node: 112233, file tid on oracle: 556677,
  do the following on oracle:
    insert into esp.t_file ... select 112233, filename, ... from esp.t_file where file_tid = 556677
  then use 112233 as fileTid for this test
 */

const fileTid = 141324276;

const testname = `(webtests) m-docu2-oracle-test`;
test(testname, async () => {
  console.log(`Start Node ${testname} get sources...: ${localUrlNodeGetkeys2Sources}!`);
  const docu2UrlFileTidListJava = `${docu2UrlJavaOracle}?fileTidList=${fileTid}`;
  const docu2ResultJava = await runGetBlob(docu2UrlFileTidListJava, '');
  const lenJava = docu2ResultJava.length;

  console.debug(`len java: ${lenJava}`);
  expect(lenJava).gt(10, `Java failed for ${fileTid}!`);

  const docu2UrlFileTidListNode = `${docu2UrlNode}?fileTidList=${fileTid}`;
  const docu2ResultNode = await runGetBlob(docu2UrlFileTidListNode, '');
  const lenNode = docu2ResultNode.length;

  console.debug(`len node: ${lenNode}`);
  expect(lenNode).gt(10, `Node failed for ${fileTid}!`);

  expect(lenNode).equals(lenJava, `Length of ${fileTid} result is not the same!`);

  expectBufferEquals(docu2ResultNode, docu2ResultJava);
});
