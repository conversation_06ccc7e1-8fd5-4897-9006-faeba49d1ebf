--
-- KEY           = dbFile.select2
-- ACCESS_GROUPS = ESP_ADMIN
--

set:$MAXROWS=1000
;
set:$MAXFETCHROWS=1000
;
include:dbFile.select




--
-- KEY           = dbFile.select
-- ACCESS_GROUPS = ESP_ADMIN
--
set-if-empty:timeFormat=YYYY-MM-DD HH24:MI
;
set-if-empty:fileTid=%
;
set-if-empty:directory=%
;
set-if-empty:filename=%
;
set-if-empty:owner=%
;
set-if-empty:creationTimestamp=%
;
select 
FILE_TID, 
FILENAME, 
TO_CHAR(CREATION_TIMESTAMP, :timeFormat) AS CREATION_TIMESTAMP, 
DIRECTORY, 
OWNER, 
length(DOCUMENT) as DOCUMENT_SIZE 
from 
ESP.T_FILE
where 
cast(FILE_TID as text) like :fileTid
and
DIRECTORY like :directory
and
FILENAME like :filename
and
(OWNER like :owner or OWNER is null)
and
TO_CHAR(CREATION_TIMESTAMP, :timeFormat) like :creationTimestamp
order by FILENAME, CREATION_TIMESTAMP desc




--
-- KEY           = zz-dbFile.insert
-- ACCESS_GROUPS = SYSTEM
--

insert into ESP.T_FILE
(
  FILE_TID,
  CREATION_TIMESTAMP,
  OWNER,
  DIRECTORY,
  FILENAME,
  TAGS
) 
values
(
  :fileTid,
  current_timestamp,
  :owner,
  :directory,
  :filename,
  :tags
);



--
-- KEY           = dbFile.move
-- ACCESS_GROUPS = SYSTEM
--

update ESP.T_FILE
set
DIRECTORY = :targetDirectory
where
FILE_TID = :fileTid
or
(DIRECTORY = :directory and FILENAME = :filename)
;


--
-- KEY           = dbFile.rename
-- ACCESS_GROUPS = SYSTEM
-- INFO          = esp-node
--

update ESP.T_FILE
set
    FILENAME = :newFilename
where
    FILE_TID = :fileTid;


--
-- KEY           = dbFile.moveFileToDir
-- ACCESS_GROUPS = SYSTEM
--

update ESP.T_FILE
set DIRECTORY = :targetDirectory || substring(DIRECTORY from cast(:prefixLen as integer))
where
DIRECTORY like :prefixWildCard
;

--
-- KEY           = dbFile.encryptDocument
-- ACCESS_GROUPS = SYSTEM
--

update ESP.T_FILE
set DOCUMENT = pkg_CRYPTO.ENCRYPT_BLOB(DOCUMENT)
where FILE_TID = :fileTid
;


--
-- KEY           = dbFile.delete
-- ACCESS_GROUPS = SYSTEM
--

delete from esp.t_file_sha1 where file_tid = :fileTid
;
delete
from esp.t_file
where file_tid = :fileTid
;


--
-- KEY           = dbFile.deleteByPath
-- ACCESS_GROUPS = SYSTEM
--

set-if-empty:exceptFileTid=-1
;
delete from ESP.T_FILE
where 
DIRECTORY = :directory
and
FILENAME = :filename
and
not FILE_TID <> :exceptFileTid
;


--
-- KEY           = dbFile.saveFileRel
-- ACCESS_GROUPS = SYSTEM,ESP_ADMIN
-- OLD_NAME      = save_file_rel
--

set-if-empty:indx=0
;
set-if-empty:targetTid=-1
;
delete from ESP.T_FILE_REL 
where 
FILE_TID = :fileTid
and
REL_TID = :relTid
and 
INDX = :indx
;
serviceId:dbFile.insertFileRel
;


--
-- KEY           = dbFile.insertFileRel
-- ACCESS_GROUPS = SYSTEM,ESP_ADMIN
--

insert into ESP.T_FILE_REL 
(FILE_TID, REL_TID, TARGET_TID, TARGET_VALUE, INDX) 
values 
(:fileTid, :relTid, :targetTid, :targetValue, :indx)
;


--
--
-- KEY           = dbFile.deleteFileRelForTarget
-- ACCESS_GROUPS = SYSTEM,ESP_ADMIN
-- OLD_NAME      = delete_file_rel_for_target
--

delete from ESP.T_FILE_REL 
where
TARGET_TID = :targetTid


--
-- KEY = zz-dbFile.get
-- ACCESS_GROUPS      = ESP_ADMIN,SYSTEM
--

select file_tid
     , filename
     , directory
     , owner
     , to_char(creation_timestamp, 'yyyy-mm-dd hh24:mi') as creation_timestamp
     , to_char(deletion_timestamp, 'yyyy-mm-dd hh24:mi') as deletion_timestamp
     , length(document)                                  as length
     , tags
     , real_length
from esp.t_file
where file_tid = :fileTid
;


--
--
-- KEY           = dbFile.getFileRel
-- ACCESS_GROUPS = SYSTEM,ESP_ADMIN
--

set-if-empty:indx=0
;
select FILE_TID, REL_TID, TARGET_TID, TARGET_VALUE, INDX from ESP.T_FILE_REL
where
FILE_TID = :fileTid
and
REL_TID = :relTid
and
INDX = :indx






--
--
-- KEY           = dbFile.DBFileUpdateRealLength
-- ACCESS_GROUPS = SYSTEM,ESP_ADMIN
--

java:com.swissre.serviceQuery.lib.DBFileUpdateRealLength
;




--
-- KEY           = dbFile.updateRealLengthIfNeeded
-- ACCESS_GROUPS = SYSTEM,ESP_ADMIN
--

update ESP.T_FILE
set REAL_LENGTH = LENGTH(PKG_CRYPTO.DECRYPT_BLOB(DOCUMENT))
where real_length is null
  and document is not null
  and FILE_TID = :fileTid
;



--
--
-- KEY           = dbFile.cleanupTempByDays
-- ACCESS_GROUPS = SYSTEM,ESP_ADMIN
--

delete from esp.t_file
where   (( directory is not null
    and (directory like '%temp/%' or directory like '%tmp/%')
    and creation_timestamp < (current_timestamp - interval '2 days')
             ) or
         ( deletion_timestamp is not null
             and deletion_timestamp < (current_timestamp - interval '1 day')
             ) or
         ( directory like '/home%'
             and split_part(directory, '/', 3) not in (select user_id from esp.v_user)
             ))
;


--
--
-- KEY           = dbFile2.get
-- ACCESS_GROUPS = SYSTEM
--
set-if-empty:timeFormat=YYYY-MM-DD HH24:MI
;
select
FILE_TID
, FILENAME
, DIRECTORY
, OWNER
, TO_CHAR(CREATION_TIMESTAMP, timeFormat) as "CREATED_ISO"
, TO_CHAR(DELETION_TIMESTAMP, timeFormat) as "DELETION_ISO"
, LENGTH(DOCUMENT) as "LENGTH"
, TAGS
, REAL_LENGTH
from ESP.T_FILE
where
FILE_TID = :fileTid
;


--
-- KEY           = dbFile.getSHA1
-- ACCESS_GROUPS = SYSTEM
--

select SHA1 from ESP.T_FILE_SHA1 where FILE_TID = :fileTid
;


--
-- KEY           = dbFile.getFileTid
-- ACCESS_GROUPS = SYSTEM
--

select FILE_TID from ESP.T_FILE_SHA1 where SHA1 = :sha1



--
-- KEY           = dbFile.insertSHA1
-- ACCESS_GROUPS = SYSTEM
--

insert into ESP.T_FILE_SHA1 (FILE_TID, SHA1) values (:fileTid, :sha1)




--
-- KEY           = dbFile.deleteTomorrow
-- ACCESS_GROUPS = SYSTEM
--

update ESP.T_FILE
set DELETION_TIMESTAMP = current_timestamp+ INTERVAL '1 day'
where
FILE_TID = :fileTid
;


---
--
-- KEY = dbFile.deleteOldFiles
-- ACCESS_GROUPS = SYSTEM
--

delete
from esp.t_file_sha1
where file_tid in (select i.file_tid
                   from esp.t_file i
                   where i.directory like :directory
                     and i.creation_timestamp < current_timestamp - interval '3 days');
delete
from esp.t_file i
where i.directory like :directory
  and i.creation_timestamp < current_timestamp - interval '3 days';
