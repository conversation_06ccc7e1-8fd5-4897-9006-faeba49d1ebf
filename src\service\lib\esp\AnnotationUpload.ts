/* eslint-disable space-before-function-paren */
import {
  errorLine,
  isSqExceptionResult,
  ProcessLine,
  SqFunctionArg,
  SqNodeFunction,
  SqResult,
  SqSimple
} from '../../../serviceQuery';

import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';
import ServiceCenter from '../../ServiceCenter';
import { getUserPref } from './UserService';
import { ANNOTATION_EXPIRATION_DAYS, SYSTEM } from '../../constants';
import { millisPerDay, toBoolean } from './utils';
import { createAnnotationDirectory, getFiles } from './DBFileHelper';
import { getDbFile, getDbFileContent, saveDbFile } from '../upload-file-utils';
import { checkFileAccess } from '../check-file-access';
import decompress from 'decompress';
import { AnnotationType } from '../../pdf-annotation';
import { isAnnotationTypeEmpty } from './annotation-utils';
import { pdfAnnotationHandlerJson } from '../../pdf-annotation/pdf-annotation-handler';
import { ANNOTATED, ANNOTATION_FILE, deleteFileRelForTarget, insertFileRelForAnnotation } from './DBFileRelSupport';
import { processPdfDerivedFiles } from './PdfUtilities';
import { track } from './DBLogUtils';
import { listAllFiles, moveFile } from '../DBFileService';

const logger = LoggerCenter.getLogger(path.basename(__filename));

const runningAnnotationUploads: Set<string> = new Set<string>();

const doLater = async (args: SqFunctionArg, requestId: string): Promise<SqResult> => {
  return new Promise<SqResult>((resolve) => {
    setTimeout(() => {
      logger.info(`Postpone AnnotationUpload for ${requestId}!`);
      AnnotationUpload(args).then(resolve);
    }, 5000);
  });
};

export const AnnotationUpload: SqNodeFunction = async (args): Promise<SqResult> => {
  const { request } = args;
  const sq = ServiceCenter.getInstance().getSq();
  const trackingMessage: string[] = [];
  const { userId = 'ANONYMOUS', roles, parameters } = request;
  const documentFileTid = +parameters.documentFileTid;

  const requestId = `${userId}-${documentFileTid}`;
  if (runningAnnotationUploads.has(requestId)) {
    return doLater(args, requestId);
  }
  runningAnnotationUploads.add(requestId);
  try {
    logger.info(`AnnotationUpload START : user: ${userId}`);
    const lines: ProcessLine[] = [];
    const resultFiles: string[] = [];

    const days = await getDeletionTimestamp(parameters.updateTime, userId);
    const backupOnly = toBoolean((parameters.backupOnly || '').toString());
    const annotationDirectory = createAnnotationDirectory(documentFileTid, userId);
    logger.info(`AnnotationUpload : userId: ${userId}; documentFileTid: ${documentFileTid}`);
    let annotatedFileTid = '';
    trackingMessage.push(`documentFileTid: ${documentFileTid} `);
    //
    // start processing
    //
    const documentFile = await getDbFile(documentFileTid.toString());
    if (!documentFile) {
      const fileNotFound = `AnnotationUpload : no file found for ${documentFileTid}`;
      return handleError(fileNotFound, lines)
    }

    //
    // check access
    //
    if (!(await checkFileAccess(documentFile, { userId, roles }))) {
      const noAccess = `AnnotationUpload : access denied to ${userId} for pdf file (File tid : ${documentFile.fileTid} : ${documentFile.directory}${documentFile.filename} )`;
      return handleError(noAccess, lines)
    }

    //
    // prepare annotation file
    //
    const fileTids: string[] = getFiles(request);
    if (fileTids.length === 0 || !fileTids[0]) {
      const noUpload = 'AnnotationUpload :"No annotation file provided!';
      return handleError(noUpload, lines)
    }
    const annotationFileTid = fileTids[0];
    trackingMessage.push(`annotationFileTid: ${annotationFileTid}`);

    const ufile = await getDbFile(annotationFileTid);
    await track(
      'I',
      `userId: ${userId}; documentFileTid: ${documentFileTid}; annotationFileTid: ${annotationFileTid}`,
      'AnnotationUpload-Start',
      userId
    );
    if (!ufile) {
      const noAnnotFile = `No annotation file could be find for file_tid ${annotatedFileTid}`;
      return handleError(noAnnotFile, lines)
    }
    resultFiles.push(ufile.fileTid);
    if (ufile.filename.toLowerCase().endsWith('zip')) {
      const content0 = await getDbFileContent(ufile.fileTid);
      if (isSqExceptionResult(content0)) {
        return content0;
      }

      const content = await decompress(content0);
      await saveDbFile({ ...ufile, data: content[0].data });
      if (!backupOnly) {
        const annotationFileContent = await getDbFileContent(annotationFileTid);
        const documentFileContent = await getDbFileContent(documentFileTid);

        if (isSqExceptionResult(annotationFileContent)) {
          return annotationFileContent;
        }
        if (annotationFileContent.length === 0) {
          const msg = `Annotation file content is empty for annotationFileTid: ${annotationFileTid}`;
          return handleError(msg, lines)
        }
        const str = annotationFileContent.toString('utf8');
        const annotationFileContentJson = JSON.parse(str) as AnnotationType;
        if (isAnnotationTypeEmpty(annotationFileContentJson)) {
          const msg = `Annotation file has no annotation layers with non empty content: ${str}`;
          return handleError(msg, lines)
        }

        if (isSqExceptionResult(documentFileContent)) {
          return documentFileContent;
        }
        if (documentFileContent.length === 0) {
          const msg = `Document file content is empty for documentFileTid: ${documentFileTid}`;
          logger.error(msg);
          return { processInfo: { lines: [errorLine(msg)] } };
        }

        const annotatedContent = await pdfAnnotationHandlerJson(documentFileContent, annotationFileContentJson);
        if (!annotatedContent || annotatedContent.length === 0) {
          const msg = `Annotated content is empty for documentFileTid: ${documentFileTid}`;
          return handleError(msg, lines)
        }

        const annotatedFile = {
          filename: documentFile.filename,
          directory: `/home/<USER>/`,
          owner: userId
        };

        const annotatedFileSaved = await saveDbFile({ ...annotatedFile, data: Buffer.from(annotatedContent) });
        if (isSqExceptionResult(annotatedFileSaved)) {
          return annotatedFileSaved;
        }
        annotatedFileTid = annotatedFileSaved.fileTid;

        trackingMessage.push(`annotatedFileTid: ${annotatedFileTid}`);

        resultFiles.push(annotatedFileTid);

        //
        // save file_rel for annotated file
        //
        const insertAnnotatedRes = await insertFileRelForAnnotation(
          documentFileTid,
          annotatedFileTid,
          userId,
          ANNOTATED
        );
        if (isSqExceptionResult(insertAnnotatedRes)) {
          return insertAnnotatedRes;
        }
        const processedResult = await processPdfDerivedFiles(annotatedFileSaved);
        if (isSqExceptionResult(processedResult)) {
          return processedResult;
        }
      }

      // set deletion timestamp
      if (days) {
        for (const fileTid in resultFiles) {
          await sq.run({
            serviceId: 'dbFile.setDeletionDate',
            userId: SYSTEM,
            roles: [SYSTEM],
            parameters: { days, fileTid }
          });
        }
        trackingMessage.push(`days: ${days}`);
      }

      //
      // clean out old annotation and annotated files
      //
      const oldAnnotationFiles = await listAllFiles(annotationDirectory, false);
      for (const dbFile of oldAnnotationFiles) {
        trackingMessage.push(`oldAnnotation: ${dbFile.fileTid}`);
        await moveFile(dbFile.fileTid, '/daily_temp/');
        await deleteFileRelForTarget(dbFile.fileTid);
      }
    }
    //
    // save file_rel for annotation file
    //
    await insertFileRelForAnnotation(documentFileTid, ufile.fileTid, userId, ANNOTATION_FILE);
    // move new annotation and annotated files to the right directory
    for (const fileTid of resultFiles) {
      await moveFile(fileTid, annotationDirectory);
    }

    const header = ['annotationfiletid', 'annotatedfiletid'];
    const table = [[annotationFileTid, annotatedFileTid.toString()]];

    await track('I', trackingMessage.toString(), 'AnnotationUpload', userId);

    return { header, table, processInfo: { lines } };
  } finally {
    runningAnnotationUploads.delete(requestId);
  }
};

function handleError(message: string, lines: ProcessLine[]): SqResult {
  lines.push(errorLine(message));
  logger.error(message);
  return { processInfo: { lines } };
}

async function getDeletionTimestamp(updateTime: SqSimple, userId: string) {
  if (!updateTime) {
    return 0;
  }
  try {
    const userPrefMap = await getUserPref(userId);
    const expirationDays = userPrefMap?.[ANNOTATION_EXPIRATION_DAYS];

    if (!expirationDays) {
      return 0;
    }

    const expirationTimestamp =
      new Date(updateTime.toString().substring(0, 10)).getTime() + Number(expirationDays) * millisPerDay;

    return Math.floor((expirationTimestamp - Date.now()) / millisPerDay);
  } catch (e) {
    logger.error(`Error in getDeletionTimestamp ${e.message}`);
    return 0;
  }
}
