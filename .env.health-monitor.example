# Health Monitoring Configuration
# Copy these variables to your .env file and adjust as needed

# Enable/disable health monitoring
HEALTH_MONITOR_ENABLED=true

# Health check interval in seconds (default: 60)
HEALTH_CHECK_INTERVAL=60

# Number of consecutive failures before triggering restart (default: 3)
HEALTH_FAILURE_THRESHOLD=3

# Health check endpoint URL (default: auto-detected based on PORT)
# HEALTH_CHECK_URL=http://localhost:3001/api/v1/health/detailed

# Health check timeout in milliseconds (default: 10000)
HEALTH_CHECK_TIMEOUT=10000

# Enable/disable automatic restart on health check failures
AUTO_RESTART_ENABLED=true

# Graceful shutdown timeout in milliseconds (default: 30000)
GRACEFUL_SHUTDOWN_TIMEOUT=30000

# Delay before restart in milliseconds (default: 5000)
RESTART_DELAY=5000

# Maximum restart attempts before giving up (default: 3)
MAX_RESTART_ATTEMPTS=3

# Cooldown period between restart attempts in milliseconds (default: 300000 = 5 minutes)
RESTART_COOLDOWN=300000
