/* eslint-disable space-before-function-paren */
import { columnAsMap } from '../../../serviceQuery';
import ServiceCenter from '../../ServiceCenter';
import { SYSTEM } from '../../constants';

export const getUserPref = async (userId: string) => {
  const sq = ServiceCenter.getInstance().getSq();
  const res = await sq.run({
    serviceId: 'userPref.select',
    userId: SYSTEM,
    roles: [SYSTEM],
    parameters: { userId }
  });
  return columnAsMap(res, 0, 1);
};

export const saveUserRequest = async (userId: string, requestId: string) =>
  ServiceCenter.getInstance().runSystemSq({
    serviceId: 'userRequest.save',
    parameters: { userId, requestId }
  });

export const deleteUserRequest = async (userId: string, requestId: string) =>
  ServiceCenter.getInstance().runSystemSq({
    serviceId: 'userRequest.delete',
    parameters: { userId, requestId }
  });
