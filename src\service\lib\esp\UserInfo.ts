import {
  isSqExceptionResult,
  SqContext,
  SqExceptionResult,
  sqExceptionResult,
  SqRequest,
  SqResult,
  toFirst
} from '../../../serviceQuery';
import ServiceCenter from '../../ServiceCenter';
import { App } from '../../../app';
import AppPropertiesService from '../AppPropertiesService';
import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));

type UserData = {
  userId: string;
  firstName: string;
  lastName: string;
  emailAddress: string;
  emailNotification: string;
  accessVisibility: string;
  defaultTeamRoomTid: string;
};

export async function UserInfo({ request }: { request: SqRequest; context: SqContext }): Promise<SqResult> {
  const header: string[] = [];
  const table: string[][] = [[]];
  const userId: string = request.userId || 'ANONYMOUS';
  const roles = request.roles || [];
  addColumn('userId', userId);

  let ud: UserData;
  const res0 = await getUserData(request);

  if (isSqExceptionResult(res0)) {
    ud = {
      userId,
      firstName: 'na',
      lastName: 'na',
      emailAddress: 'na',
      emailNotification: 'Off',
      accessVisibility: 'On',
      defaultTeamRoomTid: ''
    };
  } else {
    ud = res0;
  }

  const appProperties = AppPropertiesService.getInstance();
  const sc = ServiceCenter.getInstance();
  addColumn('firstName', ud.firstName);
  addColumn('lastName', ud.lastName);
  addColumn('emailAddress', ud.emailAddress);
  addColumn('emailNotification', ud.emailNotification);
  addColumn('accessVisibility', ud.accessVisibility);
  addColumn('isAdmin', '' + roles.includes(App.ADMIN_ROLE));
  addColumn('rolenames', roles.join(','));
  addColumn('appId', App.APP_ID);
  addColumn('env', (await appProperties.get('env', '?')) + (sc.isLocal() ? '-local' : ''));
  addColumn('envTitle', await appProperties.get('envTitle'));
  addColumn('startupTime', sc.getStartupTime().toISOString());

  return { header, table };

  function addColumn(name: string, value: string) {
    header.push(name);
    table[0].push(value);
  }
}

export async function getUserData(request: SqRequest): Promise<UserData | SqExceptionResult> {
  const sc = ServiceCenter.getInstance();
  const ISLOCAL = sc.isLocal();
  const userId: string = request.userId || 'ANONYMOUS';
  const serviceId = 'userData.get';
  logger.log({
    level: 'info',
    message: 'getUserData',
    userId,
    ISLOCAL,
    parameters: request.parameters
  });
  const res0: SqResult = await sc.runSystemSq({
    serviceId: 'userData.get',
    parameters: { userId, ISLOCAL, ...request.parameters }
  });

  const ud: UserData | undefined = toFirst<UserData>(res0);
  logger.info({
    level: 'info',
    message: 'getUserData res',
    result: ud
  });
  if (!ud) {
    const ex = 'Could not get user data by service: ' + serviceId;
    logger.warn(ex);
    return sqExceptionResult(ex);
  }
  return ud;
}
