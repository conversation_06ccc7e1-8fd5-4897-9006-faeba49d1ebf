import * as express from 'express';
import { Request, Response } from 'express';
import { isSqExceptionResult, sqExceptionResult } from '../serviceQuery';
import ServiceCenter from '../service/ServiceCenter';
import { getDbFile, getDbFileContent } from '../service/lib/upload-file-utils';
import { sendFileBuffer } from '../utils';
import { getUserProfile, toParameters } from './web-utils';
import { checkFileAccess } from '../service/lib/check-file-access';
import { Controller } from '../types';
import { UserProfile } from '../service/types';
import { getFileRel, PREVIEW_ICON } from '../service/lib/esp/DBFileRelSupport';
import LoggerCenter from '../logger/LoggerCenter';
import path from 'path';
import { saveUserRequest } from '../service/lib/esp/UserService';
import { processPdfDerivedFiles } from '../service/lib/esp/PdfUtilities';

const logger = LoggerCenter.getLogger(path.basename(__filename));
const CONTROLLER_NAME = 'docu';

export default class DocuController implements Controller {
  public readonly name = CONTROLLER_NAME;
  public readonly paths = [`/api/${CONTROLLER_NAME}`, '/api/docremote', '/api/admin/docu'];
  public readonly router = express.Router();

  constructor() {
    this.router.post('/*', processHttpRequest);
    this.router.get('/*', processHttpRequest);
  }
}

async function processHttpRequest(req: Request, res: Response) {
  try {
    const sq = ServiceCenter.getInstance().getSq();
    const userProfile = await getUserProfile(sq, req);
    if (!userProfile) {
      res.json(sqExceptionResult('Could not create user profile!'));
      return;
    }
    const { userId } = userProfile;

    const parameters = toParameters(req, userId);

    const { opid, subid } = parameters;

    let message = null;
    const uriParts = req.path.split('/');
    let docuId: string = '';
    if ('getFile' === opid) {
      const arr = subid.split(',');
      docuId = arr[0];
    }

    if (opid === 'getIconFor') {
      resolveGetIconFor(subid, userProfile, res);
      return;
    }

    if (!docuId) {
      docuId = uriParts[uriParts.length - 2];
    }

    if (!+docuId) {
      message = 'Not a number';
      logger.warn(message);
      res.sendStatus(404);
      return;
    }

    const f = await getDbFile(docuId);
    if (!f) {
      message = 'Not found';
      logger.warn(message);
      res.sendStatus(404);
      return;
    }

    if (f.fileTid && f.filename && (await checkFileAccess(f, userProfile))) {
      saveUserRequest(userId, 'FILE_TID:' + docuId);
      const buff = await getDbFileContent(f.fileTid);
      if (isSqExceptionResult(buff)) {
        res.json(buff);
        return;
      }

      sendFileBuffer(f.filename, buff, res, {
        headers: {
          'Content-Disposition': `attachment; filename*=UTF-8''${encodeURIComponent(f.filename)}`,
          //'Content-Disposition': 'attachment; filename="' + sanitizeFilename(f.filename) + '"',
          'X-Content-Type-Options': 'nosniff'
        }
      });

      // CRI log pdf handling event
      logger.warn('>>>CRI event log: '+ userId + ' opened file '+ docuId)

      return;
    } else {
      message = `Could not read file ${f.fileTid}`;
    }
    if (message) {
      logger.warn(message);
    }
    res.sendStatus(500);
  } catch (e) {
    logger.error(`${CONTROLLER_NAME} ${e}`);
    res.sendStatus(500);
  }
}

async function resolveGetIconFor(pdfFileTid: string, userProfile: UserProfile, res: Response) {
  const f = await getDbFile(pdfFileTid);
  if (!f) {
    res.sendStatus(404);
    return;
  }
  const ok = await checkFileAccess(f, userProfile);
  if (ok) {
    const iconFileTid = await getFileRel(pdfFileTid, PREVIEW_ICON, -1);
    const r = await getDbFile(iconFileTid);
    if (!iconFileTid || !r) {
      logger.info(`No data for preview icon found for file with fileTid: ${pdfFileTid}`);
      const pdf = await getDbFile(pdfFileTid);
      const processResult = await processPdfDerivedFiles(pdf);
      if (!isSqExceptionResult(processResult)) {
        const iconFileTid2 = await getFileRel(pdfFileTid, PREVIEW_ICON, -1);
        await returnPreview(iconFileTid2, res);
        return;
      }
      res.sendStatus(404);
      return;
    } else {
      await returnPreview(iconFileTid, res);
      return;
    }
  }
  res.sendStatus(500);
}

async function returnPreview(iconFileTid: string, res: Response) {
  const r = await getDbFile(iconFileTid);
  if (!r) {
    logger.error(`No preview icon found for file with fileTid: ${iconFileTid}`);
    res.sendStatus(404);
    return;
  }
  const buff = await getDbFileContent(r.fileTid);
  if (isSqExceptionResult(buff)) {
    logger.error(`DBFile not found:${r.fileTid}, ${r.filename}`);
    res.sendStatus(404);
    return;
  }
  sendFileBuffer(r.filename, buff, res);
}
