--
-- KEY           = esp.appPropertiesSwitch.select
-- ACCESS_GROUPS = ESP_ADMIN
--

select NAME, VALUE from ESP.T_APP_PROPERTIES
;


--
-- KEY           = esp.appPropertiesSwitch.toTrue
-- ACCESS_GROUPS = ESP_ADMIN
--

delete from ESP.T_APP_PROPERTIES where NAME = :name
;
insert into ESP.T_APP_PROPERTIES (NAME, VALUE) values (:name, 'true')
;

--
-- KEY           = esp.appPropertiesSwitch.toFalse
-- ACCESS_GROUPS = ESP_ADMIN
--


delete from ESP.T_APP_PROPERTIES where NAME = :name
;
insert into ESP.T_APP_PROPERTIES (NAME, VALUE) values (:name, 'false')
;

