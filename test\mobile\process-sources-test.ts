/* eslint-disable no-console */
import 'mocha';
import { expect } from 'chai';
import { testLocalAdminSrc, testLocalMpSrc } from '../common';
import fs from 'fs';
import { getTestSc } from '../init-local';

describe(`testing-process-sources ${testLocalMpSrc}`, () => {
  before(() => {
    getTestSc();
  });

  it('checking config', () => {
    expect(testLocalMpSrc.length).gt(10);
    expect(testLocalAdminSrc.length).gt(10);
  });

  it('checking source dir', () => {
    const list = fs.readdirSync(testLocalAdminSrc);
    console.debug(`Found ${list.length} entries!`);
    expect(list.length).gt(3);
  });
});
