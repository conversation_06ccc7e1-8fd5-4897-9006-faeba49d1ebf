import { DbFile } from './types';
import { SqRequest, SqSimple } from '../../../serviceQuery';
import { _derived } from '../../constants';

export const createDerivedPreviewFile = (fileId: string): DbFile => ({
  fileTid: '',
  filename: 'preview.png',
  directory: `${_derived}${fileId}`
});

export const createAnnotationDirectory = (documentFileTid: SqSimple, userId: string) =>
  `${_derived}${documentFileTid}/${userId.toUpperCase()}/`;
export const getFiles = ({ parameters }: SqRequest) => {
  const fileTids: string[] = [];
  Object.keys(parameters).forEach((name) => {
    if (/^UPLOADFILE\d+$/.test(name)) {
      const fileTid: number = +parameters[name];
      if (fileTid) {
        fileTids.push(fileTid.toString());
      }
    }
  });
  return fileTids;
};
