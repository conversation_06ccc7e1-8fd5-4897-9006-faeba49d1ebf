import * as express from 'express';
import { Request, Response } from 'express';
import { Controller } from '../types';

const CONTROLLER_NAME = 'health';

export default class HealthCheckController implements Controller {
  public readonly name = CONTROLLER_NAME;
  public readonly paths = [`/api/v1/${CONTROLLER_NAME}`];
  public readonly router = express.Router();

  constructor() {
    this.router.use('/*', (_req: Request, res: Response) => res.send('Ok'));
  }
}
