--
-- KEY           = esp.saveAppPropertiesReleaseWarning
-- ACCESS_GROUPS = ESP_ADMIN_MGMT
--

set:name=RELEASE_WARNING_MESSAGE
;
delete from ESP.T_APP_PROPERTIES where NAME = :name
;
insert into ESP.T_APP_PROPERTIES (NAME, VALUE) VALUES (:name, :value)



--
-- KEY           = esp.selectAppPropertiesReleaseWarning
--

set:name=RELEASE_WARNING_MESSAGE
;
select VALUE from ESP.T_APP_PROPERTIES where NAME = :name


--
-- KEY           = esp.deleteAppPropertiesReleaseWarning
-- ACCESS_GROUPS = ESP_ADMIN_MGMT
--

set:name=RELEASE_WARNING_MESSAGE
;
delete from ESP.T_APP_PROPERTIES where NAME = :name
