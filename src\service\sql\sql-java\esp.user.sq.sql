--
-- KEY           = roleNames.select
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM,ESP_CO_BOD_ADMIN 
-- 

select ROLE_NAME
from ESP.T_ROLE
where VALID_TO >= CURRENT_DATE
and USER_ID = :userId
and ROLE_NAME not in (select i.ROLE_NAME from ESP.T_ROLE_TEMP i where i.VALID_TO >= CURRENT_DATE and i.USER_ID = :userId)

union all

select ROLE_NAME
from ESP.T_ROLE_TEMP
where VALID_TO >= CURRENT_DATE
and USER_ID = :userId

order by ROLE_NAME
;