import 'mocha';
import { expect } from 'chai';
import { SqResult } from '../src/serviceQuery';
import { compareResult } from './test-utils';

describe('testing-test', () => {
  it('just-print-something', () => {
    console.log('SOMETHING');
    expect(!!'this is true').true;
  });
});

describe('testing-compareResult', () => {
  it('empty header', () => {
    const actualResult: SqResult = {};
    const expectedResult: SqResult = {};
    compareResult(actualResult, expectedResult);
  });

  it('one empty header', () => {
    const actualResult: SqResult = { header: [] };
    const expectedResult: SqResult = {};
    try {
      compareResult(actualResult, expectedResult);
    } catch (e) {
      return;
    }
    expect.fail('One header is an empty array. The other is undefined');
  });
  it('empty table', () => {
    const actualResult: SqResult = { header: [] };
    const expectedResult: SqResult = { header: [] };
    compareResult(actualResult, expectedResult);
  });
  it('one empty table', () => {
    const actualResult: SqResult = { header: [] };
    const expectedResult: SqResult = { header: [], table: [] };
    try {
      compareResult(actualResult, expectedResult);
    } catch (e) {
      return;
    }
    expect.fail('One header is an empty array. The other is undefined');
  });

  it('header content', () => {
    const actualResult: SqResult = { header: ['name', 'update'] };
    const expectedResult: SqResult = { header: ['name', 'update'] };
    compareResult(actualResult, expectedResult);
  });

  it('header content 2', () => {
    const actualResult: SqResult = { header: ['name', 'update'] };
    const expectedResult: SqResult = { header: ['update', 'name'] };
    try {
      compareResult(actualResult, expectedResult);
    } catch (e) {
      return;
    }
    expect.fail('Different header expected!');
  });

  it('with content 1', () => {
    const actualResult: SqResult = { header: ['name', 'update'], table: [[]] };
    const expectedResult: SqResult = { header: ['name', 'update'], table: [[]] };
    compareResult(actualResult, expectedResult);
  });

  it('with content 2', () => {
    const actualResult: SqResult = { header: ['name', 'update'], table: [] };
    const expectedResult: SqResult = { header: ['name', 'update'], table: [[]] };
    try {
      compareResult(actualResult, expectedResult);
    } catch (e) {
      return;
    }
    expect.fail('Expected: Different table length!');
  });

  it('with content 3', () => {
    const actualResult: SqResult = { header: ['name', 'update'], table: [['Hans', '3']] };
    const expectedResult: SqResult = { header: ['name', 'update'], table: [['Hans', '2']] };
    try {
      compareResult(actualResult, expectedResult);
    } catch (e) {
      return;
    }
    expect.fail('Expected: Different column value!');
  });
});
