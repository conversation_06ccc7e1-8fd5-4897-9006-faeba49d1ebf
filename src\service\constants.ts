export const ANONYMOUS = 'ANONYMOUS';
export const SYSTEM = 'SYSTEM';
export const $REQUESTID = '$REQUESTID';
export const PATH_SEPARATOR = '/';

export const ANNOTATION_EXPIRATION_DAYS = 'ANNOTATION_EXPIRATION_DAYS';
export const _hot_ = '/hot/';
export const hot_data = '/hot/data/';
export const hot_definitions = '/hot/definitions/';
export const _iesp = '/.iesp/';

export const SUCCESS = 'SUCCESS';
export const FAILURE = 'FAILURE';

export const WITH_ENCRYPTION = ['/data/', '/hot/data/', '/home/', '/ESP.T_UPLOAD_INSTANCE/', '/mp/', '/web/fcrUpload/'];
export const MOBILE_APP_ID = 'IESP';
export const var_temp = '/var/temp/';
export const _temp_ = '/_temp_/';
export const DEV = 'DEV';
export const TRAIN = 'TRAIN';

export const dbFileProcessServiceId = 'selectDownloadFile';

export enum PARAMs {
  meetingItemTid = 'meetingItemTid',
  ord = 'ord'
}

export const _derived = '/.derived/';

export const JavaLogManager_level = 'JavaLogManager_level';
export const JavaLogManager_filter = 'JavaLogManager_filter';
