import { isError, SqFunctionArg, SqNodeFunction, SqResult } from '../../../serviceQuery';
import ServiceCenter from '../../ServiceCenter';
import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';
import { SYSTEM } from '../../constants';

const logger = LoggerCenter.getLogger(path.basename(__filename));
type SendDataEntry = { key: string; value: string; source: string; sysdate?: string };
export const SendData: SqNodeFunction = async ({ request }: SqFunctionArg): Promise<SqResult> => {
  const sq = ServiceCenter.getInstance().getSq();
  const { userId = 'ANONYMOUS' } = request;
  const { data = '', logServiceId = '' } = request.parameters;
  const lines = data.toString().split('\n');

  const serviceId = (logServiceId || 'co.usage.log').toString();

  let exception = '';

  let counter = 0;
  for (const line of lines) {
    logger.info(`line ${counter} : ${line}`);
    try {
      const { key, value, source, sysdate = '-no sysdate-' }: SendDataEntry = JSON.parse(line);
      if (key && value) {
        await sq.run({
          serviceId,
          userId,
          roles: [SYSTEM],
          parameters: { key, value, source, sysdate, userId }
        });
      }
    } catch (e) {
      if (isError(e)) {
        exception = e.message;
      }
    }
    counter++;
  }
  return { name: 'SendData', exception };
};
