/* tslint:disable:no-string-literal */
/* eslint-disable @typescript-eslint/no-explicit-any,@typescript-eslint/explicit-module-boundary-types */

import LoggerCenter from '../logger/LoggerCenter';
import path from 'path';

export const toString = (a: SqSimple): string => (a ?? '').toString();
export type SqSimple = string | number | boolean;
export type SqRecord = Record<string, SqSimple>;

export type SqRequest = {
  userId?: string;
  userid?: string;
  roles?: string[];
  serviceId: string;
  parameters: SqRecord;
};

export type SqContext = {
  recursion: number;
  contextId: number;
  rowsAffectedList: number[];
  userMessages: string[];
  systemMessages: string[];
  statusCode: number;
  includes: Record<string, number>;
  maxRows?: number;
  serviceEntry?: SqServiceEntry;
  txId?: string;
};

interface ServiceFunctionArgs {
  request: SqRequest;
  context: SqContext;
}

export type ServiceFunction = (serviceFunctionArgs: ServiceFunctionArgs) => Promise<SqResult>;

export type SqServiceEntry = {
  serviceId: string;
  statements: string;
  serviceFunction?: ServiceFunction;
  roles: string[];
  tags: Set<string>;
};

export type AddServiceArgs = {
  serviceId: string;
  statements?: string;
  serviceFunction?: ServiceFunction;
  roles?: string[];
  tags?: Set<string>;
};

export type SqStatementNode = {
  cmd: string;
  statement: string;
  parameter: string;
  children?: SqStatementNode[];
};

export interface SqExceptionResult {
  exception: string;
  stack?: string;
  processInfo?: ProcessInfoType;
}

export interface ProcessLine {
  msg: string;
  time: number;
  state: 'Error' | 'Warning' | 'System';
}

export const warnLine = (msg: string): ProcessLine => ({ msg, time: Date.now(), state: 'Warning' });
export const errorLine = (msg: string): ProcessLine => ({ msg, time: Date.now(), state: 'Error' });
export const systemLine = (msg: string): ProcessLine => ({ msg, time: Date.now(), state: 'System' });

export interface ProcessInfoType {
  lines: ProcessLine[];
  state?: string;
}

export interface SqResult extends Partial<SqExceptionResult> {
  name?: string;
  userId?: string;
  types?: string[];
  headerSql?: string[];
  header?: string[];
  table?: string[][];
  rowsAffected?: number;
  from?: number;
  hasMore?: boolean;
  dbFetchingTime?: number;
  dbExecutionTime?: number;
  processInfo?: ProcessInfoType;
  // only for compatibility to Java (esp)
  type?: string;
  username?: string;
  size?: number;
  totalCount?: number;
}

export type StartBlockType = 'if' | 'if-else' | 'switch' | 'while' | 'foreach' | string;
export type EndBlockType = 'fi' | 'done' | 'end' | string;
export type SqRegistryType = 'node' | 'sql' | string;

export type SqCommandsType = {
  StartBlock: Record<StartBlockType, true>;
  EndBlock: Record<EndBlockType, true>;
  Registry: Record<SqRegistryType, SqNodeFunction>;
  Node: Record<string, SqNodeFunction>;
};

export interface SqFunctionArg {
  request: SqRequest;
  context: SqContext;
  currentResult: SqResult;
  statementNode: SqStatementNode;
  serviceEntry: SqServiceEntry;
}

export type SqNodeFunction = (sqFunctionArg: SqFunctionArg) => Promise<SqResult | undefined>;

export type LoggerLevel = 'debug' | 'info' | 'warn' | 'error';
export type LoggerFun = (msg: string) => void;
export type SqLogger = Record<LoggerLevel, LoggerFun>;


const logger = LoggerCenter.getLogger(path.basename(__filename));

export const isError = (error: any): error is Error => {
  return typeof error.message === 'string' && typeof error.name === 'string';
};

export type ProcessSql = (sql: string, parameters?: SqRecord, context?: Partial<SqContext>) => Promise<SqResult>;
export type ProcessSqlDirect = (sql: string, values: any[], maxRows?: number) => Promise<SqResult>;
export type GetServiceEntry = (serviceId: string) => Promise<SqServiceEntry | SqExceptionResult>;
export type HasServiceEntry = (serviceId: string) => Promise<boolean>;

export interface SqDriver {
  processSql: ProcessSql;
  processSqlDirect: ProcessSqlDirect;
  getServiceEntry: GetServiceEntry;
  hasServiceEntry: HasServiceEntry;
  startTransaction: () => Promise<string>;
  commitTransaction: (txId: string) => Promise<void>;
  rollbackTransaction: (txId: string) => Promise<void>;
  clearCache: () => void;
}

export function sqExceptionResult(e: string | Error | unknown): SqExceptionResult {
  if (isError(e)) {
    logger.error(`sqExceptionResult error message: ${e.message}\n, stack: ${e.stack}`);
    return { exception: e.message };
  } else if (typeof e === 'string') {
    return { exception: e };
  }
  return { exception: 'Unknown' };
}

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const isSqExceptionResult = (data: any): data is SqExceptionResult => {
  return data && typeof data.exception === 'string';
};

export function toFirst<R extends Record<string, string | undefined> = Record<string, string>>(
  serviceData: SqResult
): R {
  return toList<R>(serviceData)[0];
}

export function toList<R extends Record<string, string | undefined> = Record<string, string>>(
  serviceData: SqResult
): R[] {
  if (Array.isArray(serviceData)) {
    return serviceData;
  }
  const list: R[] = [];
  if (serviceData.table && serviceData.header) {
    const header = serviceData.header;
    const table = serviceData.table;

    table.forEach((row) => {
      const obj: any = {};
      list.push(obj);
      for (let j = 0; j < header.length; j++) {
        const head = header[j];
        obj[head] = row[j];
      }
    });
  }
  return list;
}

export const columnAsMap = (sqResult: SqResult, keyIndex: number, valueIndex: number): Record<string, string> => {
  const map: Record<string, string> = {};
  if (sqResult?.table?.length) {
    sqResult.table.forEach((row) => (map[row[keyIndex]] = row[valueIndex]));
  }
  return map;
};

export function trim(str: string): string {
  if (!str) {
    return '';
  }
  return str.trim();
}

export function toArr(ro: string | undefined): string[] {
  return (ro ? ro.split(/\s*,\s*/) : []).map((s) => s.trim());
}

export function singleValue(sqResult: SqResult): string {
  return (sqResult && sqResult.table && sqResult.table[0] ? sqResult.table[0][0] : '') ?? '';
}
