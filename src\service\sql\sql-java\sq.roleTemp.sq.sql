--
-- KEY           = roleTemp.select
-- ACCESS_GROUPS = ESP_ADMIN
--

set-if-empty:roleName=%
;
set-if-empty:userId=%
;
select 
 USER_ID, 
 ROLE_NAME, 
TO_CHAR(VALID_FROM, 'YYYY-MM-DD') as VALID_FROM, 
TO_CHAR(VALID_TO, 'YYYY-MM-DD') as VALID_TO, 
 REASON  
from ESP.T_ROLE_TEMP
where 
((upper(ROLE_NAME) like upper(:roleName) and ROLE_NAME is not null) or (:roleName = '%' and ROLE_NAME is null))
and
((upper(USER_ID) like upper(:userId) and USER_ID is not null) or (:userId = '%' and USER_ID is null))
order by USER_ID


--
-- KEY           = roleTemp.get
-- ACCESS_GROUPS = ESP_ADMIN
--



select 
 USER_ID, 
 ROLE_NAME, 
TO_CHAR(VALID_FROM, 'YYYY-MM-DD') as VALID_FROM, 
TO_CHAR(VALID_TO, 'YYYY-MM-DD') as VALID_TO, 
 REASON  
from ESP.T_ROLE_TEMP
where 
ROLE_NAME = :roleName
and
USER_ID = :userId
