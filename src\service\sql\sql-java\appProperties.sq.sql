
--
-- KEY           = selectAppProperties
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

set-if-empty:nameFilter=%
;
set-if-empty:name=%
;
set-if-empty:value=%
;
select NAME, VALUE from ESP.T_APP_PROPERTIES
where
NAME like :nameFilter
and
NAME like :name
and
(VALUE like :value or VALUE is null)
order by NAME


--
-- KEY           = getAppProperties
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM
-- xxxCACHE_MINUTES = 5
--

select NAME, VALUE from ESP.T_APP_PROPERTIES where NAME = :name

;



--
-- KEY           = updateAppProperties
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

update ESP.T_APP_PROPERTIES set VALUE = :value where NAME = :name
;


--
-- KEY           = deleteAppProperties
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

delete from ESP.T_APP_PROPERTIES where NAME = :name
;


--
-- KEY           = saveAppProperties
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

delete from ESP.T_APP_PROPERTIES where NAME = :name
;
insert into ESP.T_APP_PROPERTIES (NAME, VALUE) VALUES (:name, :value)
;
