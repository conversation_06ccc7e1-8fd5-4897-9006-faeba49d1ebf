import * as express from 'express';
import { Request, Response } from 'express';
import { isSqExceptionResult, sqExceptionResult } from '../serviceQuery';
import ServiceCenter from '../service/ServiceCenter';
import { getDbFile, getDbFileContent } from '../service/lib/upload-file-utils';
import { winFoldername80 } from '../utils';
import { getUserProfile, toParameters } from './web-utils';
import { Controller } from '../types';
import LoggerCenter from '../logger/LoggerCenter';
import path from 'path';
import JSZip from 'jszip';
import { checkFileAccess } from '../service/lib/check-file-access';
import { track } from '../service/lib/esp/DBLogUtils';
import cors from 'cors';

const logger = LoggerCenter.getLogger(path.basename(__filename));
const CONTROLLER_NAME = 'multiFileDownload';

export default class MultiFileDownloadController implements Controller {
  public readonly name = CONTROLLER_NAME;
  public readonly paths = [`/api/${CONTROLLER_NAME}`];
  public readonly router = express.Router();

  constructor() {
    this.router.post('/*', cors(), processHttpRequest);
    this.router.get('/*', cors(), processHttpRequest);
  }
}

async function processHttpRequest(req: Request, res: Response) {
  try {
    const sq = ServiceCenter.getInstance().getSq();
    const userProfile = await getUserProfile(sq, req);
    if (!userProfile) {
      res.json(sqExceptionResult('Could not create user profile!'));
      return;
    }
    const { userId, roles } = userProfile;

    const parameters = toParameters(req, userId);
    const { fileTids, folders, downloadFileName } = await getFileTidsAndFolders(sq, parameters, userId, roles);

    const zipContent = await createZipContent(fileTids, folders, userProfile);

    await track('I', `Files: ${fileTids.join(',')}`, 'MultiFileDownloadController', userId);

    sendZipResponse(res, zipContent, downloadFileName);

    // CRI log pdf handling event
    logger.warn('>>>CRI event log: '+ userId + ' downloaded file '+ fileTids.join(', ') )
  } catch (e) {
    logger.error(`${CONTROLLER_NAME} ${e}`);
    res.sendStatus(500);
  }
}

async function getFileTidsAndFolders(sq: any, parameters: any, userId: string, roles: string[]) {
  const fileTidList = parameters['fileTidList'];
  const folders: string[] = [];
  let downloadFileName = '';
  const fileTids: string[] = [];

  if (fileTidList) {
    fileTidList.split(',').forEach((n: string) => fileTids.push(n));
  } else {
    const services = getServices(parameters);
    for (const serviceId of services) {
      const res = await sq.run({
        serviceId,
        userId,
        roles,
        parameters: { ...parameters, $USERID: userId }
      });
      if (res.table) {
        res.table.forEach((row: string[]) => {
          fileTids.push(row[0]);
          folders.push(winFoldername80(row[1]));
          downloadFileName = downloadFileName ? downloadFileName : winFoldername80(row[2]) + '.zip';
        });
      }
    }
    downloadFileName = downloadFileName || 'download.zip';
  }

  return { fileTids, folders, downloadFileName };
}

function getServices(parameters: any): string[] {
  const fileSelectServiceId = parameters['fileSelectServiceId'];
  const fileSelectServiceIds = parameters['fileSelectServiceIds'];
  const services: string[] = [];
  if (fileSelectServiceId) {
    services.push(fileSelectServiceId);
  } else {
    fileSelectServiceIds.split(',').forEach((s: string) => services.push(s));
  }
  return services;
}

async function createZipContent(fileTids: string[], folders: string[], userProfile: any) {
  const zip = new JSZip();
  for (const [index, fileTid] of fileTids.entries()) {
    const dbFile = await getDbFile(fileTid);
    if (!dbFile) {
      logger.error(`File does not exist. fileTid: ${fileTid}`);
      continue;
    }
    const accessOk = await checkFileAccess(dbFile, userProfile);
    if (!accessOk) {
      continue;
    }
    const fileContent = await getDbFileContent(fileTid);

    if (isSqExceptionResult(fileContent)) {
      logger.error(`Could not read file: ${fileTid}, ${dbFile.filename}`);
      continue;
    }
    const folder = folders[index] || '';
    if (folder) {
      zip.folder(folder);
    }
    zip.file(path.join(folder, dbFile.filename), fileContent);
  }

  return await zip.generateAsync({ type: 'nodebuffer' });
}

function sendZipResponse(res: Response, zipContent: Buffer, downloadFileName: string) {
  res.contentType('application/zip');
  res.attachment(downloadFileName);
  res.setHeader('Access-Control-Expose-Headers', 'X-Filename');
  res.setHeader('X-Filename', downloadFileName);
  res.send(zipContent);
}
