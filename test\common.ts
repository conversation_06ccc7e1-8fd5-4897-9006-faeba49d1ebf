import path from 'path';
import * as dotenv from 'dotenv';
dotenv.config();
export const testdataDir = path.join(__dirname, './test-data');
export const gridPdf = path.join(testdataDir, 'test-grid.pdf');
export const outdataDir = path.join(__dirname, '../outdata');

export const localUrlNode = process.env.TEST_LOCAL_URL_NODE;
export const prodUrlJava = process.env.TEST_PROD_URL_JAVA;
export const localUrlJava = process.env.TEST_LOCAL_URL_JAVA;

export const testLocalMpSrc = process.env.TEST_LOCAL_MP_SRC;
export const testLocalAdminSrc = process.env.TEST_LOCAL_ADMIN_SRC;
