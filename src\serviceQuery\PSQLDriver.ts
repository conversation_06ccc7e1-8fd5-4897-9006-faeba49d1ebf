/* eslint-disable @typescript-eslint/no-explicit-any,@typescript-eslint/explicit-module-boundary-types,space-before-function-paren */
/* tslint:disable:no-string-literal */
/* tslint:disable:one-variable-per-declaration */
/* tslint:disable:only-arrow-functions */
/* tslint:disable:no-explicit-any */
import camelCase from 'camelcase';
import { Pool, PoolClient, PoolConfig, QueryResult } from 'pg';
import { builtins } from 'pg-types';
import {
  isSqExceptionResult,
  SqContext,
  SqDriver,
  SqExceptionResult,
  sqExceptionResult,
  SqResult,
  SqServiceEntry,
  SqSimple,
  toArr,
  toFirst
} from './serviceQuery-common';
import LoggerCenter from '../logger/LoggerCenter';
import path from 'path';
import moment from 'moment';

export const defaultTimeout1Min = 1000 * 60;

export interface PSQLDriverExtension extends SqDriver {
  setServiceEntrySql: (sql: string) => void;
  returnConnection: (con: PoolClient) => void;
  getConnection: () => Promise<PoolClient | undefined>;
  processSql_con: (
    con: PoolClient,
    sql: string,
    parameters: Record<string, SqSimple>,
    maxRows?: number
  ) => Promise<SqResult>;
  end: () => void;
}

const logger = LoggerCenter.getLogger(path.basename(__filename));

export class PSQLDriver implements PSQLDriverExtension {
  private readonly pool: Pool | undefined;
  private readonly txConnections: Record<string, PoolClient> = {};
  private txConnectionCounter = 1;

  private serviceEntryCache: Map<string, SqServiceEntry> = new Map<string, SqServiceEntry>();
  private lastServiceEntryUpdate = Date.now();
  private readonly maxCacheTime = 1000 * 60;

  private sqlLogger = LoggerCenter.getLogger(path.basename(__filename) + '-sql');
  private serviceEntrySql = '';

  constructor(poolConfig: PoolConfig) {
    const pc = { ...poolConfig, statement_timeout: defaultTimeout1Min };
    this.pool = new Pool(pc);
  }

  public clearCache() {
    this.serviceEntryCache.clear();
  }

  public async startTransaction() {
    const txIdNumber = this.txConnectionCounter++;
    const txId = `PoolClient${txIdNumber}`;
    const client = await this.getConnection();
    await client.query('BEGIN');
    this.txConnections[txId] = client;
    return txId;
  }

  public async commitTransaction(txId: string) {
    const client = this.txConnections[txId];
    if (!client) {
      throw new Error(`Try to commit. No connection available for txId: ${txId}`);
    }
    await client.query('COMMIT');
    this.returnConnection(client);
  }

  public async rollbackTransaction(txId: string) {
    const client = this.txConnections[txId];
    if (!client) {
      throw new Error(`Try to rollback. No connection available for txId: ${txId}`);
    }
    await client.query('ROLLBACK');
    this.returnConnection(client);
  }

  public async getServiceEntry(serviceId: string): Promise<SqServiceEntry | SqExceptionResult> {
    if (await this.hasServiceEntry(serviceId)) {
      return this.serviceEntryCache.get(serviceId);
    } else {
      const msg = `No service entry found for ${serviceId}`;
      logger.warn(msg);
      return sqExceptionResult(msg);
    }
  }

  public async hasServiceEntry(serviceId: string): Promise<boolean> {
    // use service entry cache
    if (Date.now() - this.lastServiceEntryUpdate > this.maxCacheTime) {
      this.serviceEntryCache.clear();
      this.lastServiceEntryUpdate = Date.now();
    }
    if (this.serviceEntryCache.has(serviceId)) {
      return true;
    }

    const result = await this.processSql(this.serviceEntrySql, { serviceId });
    const raw = toFirst(result);

    const { exception, stack } = result;
    if (exception) {
      logger.error(`Error while selecting service data for ${serviceId}! exception:${exception} \nstack: ${stack}`);
    }
    if (!raw) {
      return false;
    }

    const se: SqServiceEntry = {
      serviceId: raw.serviceId || 'notfound',
      roles: toArr(raw.roles),
      statements: raw.statements || '',
      tags: new Set(toArr(raw.tags))
    };
    this.serviceEntryCache.set(serviceId, se);
    return true;
  }

  public async end() {
    if (this.pool) {
      this.pool.end(() => logger.info('PSQL Connection Pool ended.'));
    }
  }

  public setServiceEntrySql(sql: string) {
    this.serviceEntrySql = sql;
  }

  public async getConnection(): Promise<PoolClient | undefined> {
    if (this.pool) {
      return this.pool.connect();
    }
    return undefined;
  }

  public returnConnection(con: any): void {
    try {
      if (this.pool) {
        con.release();
      }
    } catch (e) {
      logger.error(`returnConnection -> ${e}`);
    }
    logger.debug('returnConnection DONE');
  }

  public async processSql(sql: string, parameters?: Record<string, SqSimple>, context?: SqContext): Promise<SqResult> {
    let con, result: SqResult;
    const { maxRows, txId } = context || {};
    try {
      con = txId ? this.txConnections[txId] : await this.getConnection();
      if (con) {
        await this.preProcessSql(con, sql, parameters);
        result = await this.processSql_con(con, sql, parameters, maxRows);
      } else {
        result = { exception: 'No connection received!' };
      }
    } catch (err: any) {
      logger.error(`processSql error message: ${err.message}\n err.stack: ${err.stack}`);
      result = { exception: err.message };
    } finally {
      if (con) {
        await this.postProcessSql(con, sql, parameters);
        this.returnConnection(con);
        // if (!txId) { if we use txId this has to be enabled again!
        //   this.returnConnection(con);
        // }
      }
    }
    return result;
  }

  public async processSqlDirect(sql: string, values: any = null, maxRows = 50000): Promise<SqResult> {
    let con: PoolClient | undefined, result;
    try {
      con = await this.getConnection();
      if (con) {
        result = await this.processSqlQuery(con, sql, values, maxRows);
      } else {
        const exception = 'No connection received!';
        logger.warn(exception);
        result = { exception };
      }
    } catch (err: any) {
      logger.error(`processSqlDirect: err.message = ${err.message}, err.stack = ${err.stack}`);
      result = { exception: err.message };
    } finally {
      if (con) {
        this.returnConnection(con);
      }
    }
    return result;
  }

  public async processSql_con(
    con: PoolClient,
    sql: string,
    parameters: Record<string, SqSimple> = {},
    maxRows = 50000
  ) {
    parameters = parameters || {};
    maxRows = maxRows || 50000;

    this.sqlLogger.debug('start sql **************************************');
    this.sqlLogger.debug(`sql: ${sql}`);
    const qap = new QueryAndParams(sql, parameters);
    qap.convertQuery();

    //
    // PREPARE SERVICE_STMT
    //

    const sql_params = [];

    for (const n of qap.param_list) {
      if (qap.req_params[n] === undefined) {
        this.sqlLogger.debug(`no value provided for parameter: ${n} will use NULL value`);
        sql_params.push(null);
      } else {
        const v = qap.req_params[n];
        sql_params.push(v);
        this.sqlLogger.debug(`sql-parameter: ${n}=${v}`);
      }
    }

    return await this.processSqlQuery(con, qap.qm_query, sql_params, maxRows);
  }

  public async processSqlQuery(con: PoolClient, sql: string, values: any, maxRows: number): Promise<SqResult> {
    let dbExecutionTime = 0;
    const dbFetchingTime = 0;
    const startTime = new Date().getTime();

    const pgResult = await processSqlPromise(con, sql, values);
    if (isSqExceptionResult(pgResult)) {
      return pgResult;
    }
    dbExecutionTime = new Date().getTime() - startTime;
    const r = this.convertResult(pgResult, maxRows);
    logger.debug(`rowsAffected: ${r.rowsAffected}`);
    return { ...r, dbExecutionTime, dbFetchingTime };
  }

  convertResult(pgResult: QueryResult, maxRows: number): SqResult {
    const result: SqResult = {};

    result.rowsAffected = -1;
    result.from = 0;
    result.hasMore = false;
    result.headerSql = [];
    result.header = [];
    result.table = [];
    result.rowsAffected = pgResult.rowCount || -1;
    if (pgResult.command === 'SELECT') {
      result.rowsAffected = -1;
      result.header =
        typeof pgResult.fields === 'object'
          ? pgResult.fields.map((f) => {
              return camelCase(f.name);
            })
          : undefined;
      result.headerSql = typeof pgResult.fields === 'object' ? pgResult.fields.map((f) => f.name) : undefined;
      for (const row of pgResult.rows) {
        const trow = result.headerSql?.map((h, index) => convertType(row[h], pgResult.fields[index].dataTypeID));
        if (trow) {
          result.table.push(trow);
          if (maxRows === result.table.length) {
            result.hasMore = true;
            break;
          }
        }
      }
    }
    return result;
  }

  async preProcessSql(con: any, _sql: string, parameters?: Record<string, SqSimple>) {
    const { $USERID = '' } = parameters || { $USERID: '' };
    await this.processSql_con(con, 'call esp.sp_connectinit(:$USERID)', { $USERID });
  }

  async postProcessSql(con: any, _sql: string, parameters?: Record<string, SqSimple>) {
    const { $USERID = '' } = parameters || { $USERID: '' };
    if ($USERID) {
      await this.processSql_con(con, 'call esp.sp_connectinit(:$USERID)', { $USERID });
    }
  }
}

function processSqlPromise(con: PoolClient, sql: string, values: any): Promise<QueryResult | SqExceptionResult> {
  return new Promise(function (resolve) {
    if (Array.isArray(values)) {
      values = values.map((e: any) => (e ? e : null));
    }
    con.query(sql, values, (err, res) => {
      if (err) {
        const valueStr = values.join('\n');
        logger.error(`processSqlPromise : ${sql}\n ${err.message}\n ${err.stack}\n values:\n ${valueStr}`);
        resolve({ exception: err.message });
      } else {
        resolve(res);
      }
    });
  });
}

export default PSQLDriver;

const isAlNum = (ch: string) => {
  return ch.match(/^[a-z0-9]+$/i) !== null;
};

class QueryAndParams {
  private readonly named_query: string;
  private in_param: boolean = false;
  public param_list: any;
  public qm_query: string = '';
  public req_params: any;
  private QMCounter: number;
  private current_param: string = '';

  constructor(named_query: string, req_params: any) {
    this.named_query = named_query;
    this.req_params = req_params;
    this.QMCounter = 0;
  }

  QM() {
    this.QMCounter++;
    return ' $' + this.QMCounter + ' ';
  }

  isParamCharacter(c: string): boolean {
    return isAlNum(c) || c === '_' || c === '$' || c === '[' || c === ']';
  }

  toggleProtection(c: string): boolean {
    return c === "'";
  }

  handleParamCharacter(c: string): boolean {
    if (this.in_param) {
      if (this.isParamCharacter(c)) {
        this.current_param += c;
        return true;
      } else {
        this.processParam();
      }
    }
    return false;
  }

  finalizeQueryConversion() {
    if (this.in_param) {
      this.processParam();
    }
  }

  convertQuery() {
    this.qm_query = '';
    this.in_param = false;

    this.param_list = [];
    this.current_param = '';

    let prot = false;

    for (const c of this.named_query) {
      if (!prot) {
        if (this.handleParamCharacter(c)) {
          continue;
        }
        if (!this.in_param && c === ':') {
          this.in_param = true;
          continue;
        }
      }
      if (this.toggleProtection(c)) {
        prot = !prot;
      }
      this.qm_query += c;
    }
    // end processing
    this.finalizeQueryConversion();
    //
  }

  processParam() {
    const param = this.current_param;
    if (param.endsWith('[]')) {
      this.processArrayParam(param);
    } else {
      this.param_list.push(param);
      this.qm_query += this.QM();
    }
    this.current_param = '';
    this.in_param = false;
  }

  processArrayParam(param: string) {
    const param_base = param.substring(0, param.length - 2);
    const a_value = this.req_params[param_base];
    if (a_value === undefined) {
      this.param_list.push(param);
      this.qm_query += this.QM();
    } else {
      const request_values = a_value.split(',');
      request_values.forEach((request_value: string, index: number) => {
        const param_indexed: string = `${param_base}[${index}]`;
        this.req_params[param_indexed] = request_value;
        this.param_list.push(param_indexed);
        this.qm_query += index === 0 ? this.QM() : `,${this.QM()}`;
      });
    }
  }
}

function convertType(value: any, sqlType: number) {
  if (value === null || value === undefined) {
    return '';
  }

  try {
    if ((sqlType === builtins.TIMESTAMPTZ || sqlType === builtins.TIMESTAMP) && value instanceof Date) {
      return moment(value).format('YYYY-MM-DD HH:mm');
    }
    if (sqlType === builtins.DATE && value instanceof Date) {
      return moment(value).format('YYYY-MM-DD');
    }
  } catch (e) {
    // no need to do anything
  }
  return '' + value;
}
