import { Pool, PoolConfig, PoolClient } from 'pg';
import LoggerCenter from '../../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export class ConnectionPool {
  private static instance: ConnectionPool;
  private pool: Pool;

  private constructor() {
    const poolConfig: PoolConfig = {
      user: process.env.DB_TRX_USER,
      password: process.env.DB_TRX_PASSWORD,
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT || '5432'),
      database: process.env.DB_DATABASE,

      // Pool configuration with enhanced defaults that can be overridden by environment variables
      max: parseInt(process.env.DB_POOL_MAX_CONNECTIONS || '20'), // Maximum number of clients in the pool
      idleTimeoutMillis: parseInt(process.env.DB_POOL_IDLE_TIMEOUT || '30000'), // How long a client is allowed to remain idle before being closed
      connectionTimeoutMillis: parseInt(process.env.DB_POOL_CONNECTION_TIMEOUT || '5000'), // How long to wait when connecting a new client

      // SSL configuration
      ssl: process.env.DB_SSL === 'true' || process.env.NODE_ENV === 'production',

      // Query timeout (in milliseconds)
      statement_timeout: parseInt(process.env.DB_STATEMENT_TIMEOUT || '60000'),

      // Additional pool settings
      allowExitOnIdle: process.env.DB_POOL_ALLOW_EXIT_ON_IDLE === 'true', // Allow the pool to close all connections and exit when idle
    };

    this.pool = new Pool(poolConfig);

    // Log pool creation with configuration (excluding sensitive data)
    const logConfig = { ...poolConfig };
    delete logConfig.password;
    logger.info(`Database connection pool created with config: ${JSON.stringify(logConfig)}`);

    // Set up event handlers
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.pool.on('error', (err, _client) => {
      logger.error(`Unexpected error on idle client: ${err.message}`, { error: err });
      // Optionally emit custom events for monitoring systems
    });

    this.pool.on('connect', (_client) => {
      logger.debug('New client connected to database');
      logger.info(`Pool stats after connect: ${JSON.stringify(this.getPoolStats())}`);
    });

    this.pool.on('acquire', (_client) => {
      logger.debug('Client acquired from pool');
      const stats = this.getPoolStats();
      if (stats.waitingCount > 0) {
        logger.warn(`Pool has ${stats.waitingCount} waiting connections - potential bottleneck`);
      }
    });

    this.pool.on('remove', (_client) => {
      logger.debug('Client removed from pool');
      logger.info(`Pool stats after remove: ${JSON.stringify(this.getPoolStats())}`);
    });
  }

  public static getInstance(): ConnectionPool {
    if (!ConnectionPool.instance) {
      ConnectionPool.instance = new ConnectionPool();
    }
    return ConnectionPool.instance;
  }

  public getPool(): Pool {
    return this.pool;
  }

  // Get pool statistics for monitoring
  public getPoolStats() {
    return {
      totalCount: this.pool.totalCount,
      idleCount: this.pool.idleCount,
      waitingCount: this.pool.waitingCount,
    };
  }

  // Health check method
  public async healthCheck(): Promise<boolean> {
    try {
      const client = await this.pool.connect();
      await client.query('SELECT 1');
      client.release();
      return true;
    } catch (error) {
      logger.error(`Database health check failed: ${error.message}`);
      return false;
    }
  }

  // Enhanced connection acquisition with better error handling
  public async getConnection(): Promise<PoolClient> {
    const startTime = Date.now();

    try {
      // Check pool stats before attempting connection
      const stats = this.getPoolStats();
      if (stats.waitingCount > 5) {
        logger.warn(`High connection wait queue: ${stats.waitingCount} waiting connections`);
      }

      const client = await this.pool.connect();
      const duration = Date.now() - startTime;

      if (duration > 1000) {
        logger.warn(`Slow connection acquisition: ${duration}ms`);
      }

      return client;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`Failed to acquire connection after ${duration}ms: ${error.message}`);

      // Provide specific error messages based on error type
      if (error.message.includes('timeout')) {
        throw new Error(`Database connection timeout after ${duration}ms. Pool may be exhausted.`);
      } else if (error.message.includes('ECONNREFUSED')) {
        throw new Error('Database connection refused. Check if database server is running.');
      } else if (error.message.includes('ENOTFOUND')) {
        throw new Error('Database host not found. Check database configuration.');
      } else {
        throw new Error(`Database connection failed: ${error.message}`);
      }
    }
  }

  // Method to gracefully close the pool
  public async end(): Promise<void> {
    if (this.pool) {
      logger.info('Closing database connection pool');
      await this.pool.end();
      logger.info('Database connection pool closed');
    }
  }
}
