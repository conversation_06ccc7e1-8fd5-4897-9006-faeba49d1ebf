
-- KEY 		= meeting.insertFolder

call ESP.SP_MP_INSERT_GENERALSECTION(:in_section_name,:in_parent,:in_preceding_section);include:meeting.getMaxFolder


-- KEY 		= meeting.deleteFolder

call esp.sp_mp_delete_generalsection(:in_generalsection_tid)

-- KEY 		= meeting.fileMove

call esp.sp_mp_file_move ( :in_file_tid, :in_file_order, :in_meeting_item)

-- KEY 		= meeting.moveItem

call esp.sp_mp_item_move ( :in_meeting_item_tid, :in_direction)

-- KEY 		= meeting.deleteItem

include:meeting.getParent;include:meeting.deleteItemSimple


-- KEY 		= meeting.getParent
select coalesce(t.meeting_item_tid, 0) as meeting_item_tid
from (select coalesce(
                     (select meeting_item_tid from esp.t_mp_file where file_tid = :in_meeting_item_tid),
                     (select meeting_tid as meeting_item_tid from esp.t_mp_agenda where agenda_tid = :in_meeting_item_tid),
                     (select type_tid as meeting_item_tid from esp.t_mp_meeting where meeting_tid = :in_meeting_item_tid),
                     (select type_tid as meeting_item_tid from esp.t_mp_meeting where type_tid = :in_meeting_item_tid and item_type = 'GS'),
                     0
             ) meeting_item_tid

      where (select count(*)
             from ESP.V_MP_ACCESS_FILE_CURR_USER
             where FILE_TID = :in_meeting_item_tid) > 0

         or (select count(*)
             from esp.v_mp_access_agenda_all i
             where i.user_id = esp.user_code()
               and i.agenda_tid = :in_meeting_item_tid
               and i.access_right_tid >= 4) > 0

         or (select count(*)
             from esp.v_mp_access_meeting_all i
             where i.user_id = esp.user_code()
               and i.meeting_tid = :in_meeting_item_tid
               and i.access_right_tid >= 4) > 0) t
;

-- KEY 		= meeting.deleteItemSimple

call ESP.SP_MP_DELETE_ITEM(:in_meeting_item_tid)
;



-- KEY 		= meeting.renameFileLabel

call esp.sp_mp_rename_filelabel ( :in_meeting_item_tid, :in_file_name, :in_new_label)


-- KEY 		= meeting.renameItem

call esp.sp_mp_rename_item ( :in_meeting_item_tid, :in_new_name)


-- KEY 		= meeting.getMaxAgenda

select max(agenda_tid) agenda_tid_new from esp.t_mp_agenda
;

-- KEY 		= meeting.deleteAgenda

call esp.sp_mp_delete_agenda ( :in_agenda_tid)


-- KEY 		= meeting_update_meeting_properties

call esp.sp_mp_meeting_update ( :in_meeting_name, :in_location_city, :in_location_address, :in_location_room, :in_time_start, :in_meeting_tid);


