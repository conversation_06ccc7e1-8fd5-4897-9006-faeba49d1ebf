import { test } from 'mocha';
import { localUrlJava, localUrlNode } from '../common';
import { compareJson } from '../test-utils';
import { runGetkeys2 } from './getkeys2-utils';

const localUrlNodeStart2 = `${localUrlNode}mobile/start2`;
const localUrlJavaStart2 = `${localUrlJava}mobile/start2`;

const testname = `(webtests) mstart-servlet-controller-test`;
test(testname, async () => {
  console.log(`Start Node ${testname} url: ${localUrlNodeStart2}!`);
  console.log(`Start Java ${testname} url: ${localUrlJavaStart2}!`);

  const resultJava = await runGetkeys2(localUrlJavaStart2, `${testname} Java`);
  const resultNode = await runGetkeys2(localUrlNodeStart2, `${testname} Node`);

  const javaKeys = Object.keys(resultJava).sort();
  const nodeKeys = Object.keys(resultNode).sort();
  compareJson(nodeKeys, javaKeys);

  compareJson(resultNode, resultJava, {
    ignore: ['sessionid', 'dbExecutionTime'],
    isJsonString: ['tabs', 'visibleTabs', 'cycleDefinition', 'cycleDefinitionSplit']
  });
});
