FROM node:20.15.0

ENV no_proxy=127.0.0.1,localhost,docker.internal,***************,artifact.swissre.com

# Download SwissRe CA certificates
ADD http://pki.swissre.com/aia/SwissReRootCA2.crt /usr/local/share/ca-certificates/
ADD http://pki.swissre.com/aia/SwissReSystemCA22.crt /usr/local/share/ca-certificates/
ADD http://pki.swissre.com/aia/SwissReSystemCA25.crt /usr/local/share/ca-certificates/

# INSTALL dependencies and completely remove Python to mitigate CVE-2024-9287
RUN apt-get update \
    && apt-get install -y --no-install-recommends ghostscript \
    && apt-get install -y --no-install-recommends graphicsmagick \
    && apt-get install -y --no-install-recommends build-essential \
    && apt-get install -y --no-install-recommends libcairo2-dev \
    && apt-get install -y --no-install-recommends libpango1.0-dev \
    && apt-get install -y --no-install-recommends libjpeg-dev \
    && apt-get install -y --no-install-recommends libgif-dev \
    && apt-get install -y --no-install-recommends librsvg2-dev \
    && apt-get install -y tini \
    # Thoroughly remove Python to address CVE-2024-9287
    && apt-get remove --purge -y python* python3* \
    && apt-get purge -y --auto-remove python* python3* \
    && dpkg --purge --force-all python3.11 python3.11-minimal python3-minimal python3 || true \
    && apt-get autoremove -y \
    && apt-get clean -y \
    && rm -rf /var/lib/apt/lists/* \
    # Verify Python is completely removed
    && dpkg -l | grep -i python || echo "No Python packages found - good!" \
    && which python || echo "Python not found - good!" \
    && which python3 || echo "Python3 not found - good!"

# Add SwissRe CA certificates to the system's certificate store
RUN mkdir /etc/ssl/certs -p && \
    cat /usr/local/share/ca-certificates/*.crt >> /etc/ssl/certs/ca-certificates.crt
# For npm/node
ENV NODE_EXTRA_CA_CERTS=/etc/ssl/certs/ca-certificates.crt
# Configure NPM to fetch from Artifactory by default
RUN npm config set strict-ssl false \
    && npm config set -g proxy http://***********:8080 \
    && npm config set -g https-proxy http://***********:8080 \
    && npm config set -g registry https://artifact.swissre.com/artifactory/api/npm/npm

RUN mkdir -p /home/<USER>/app && chown -R node:node /home/<USER>/app

WORKDIR /home/<USER>/app
COPY package.json ./
#RUN git config --global core.symlinks false
#RUN apt-get remove -y --auto-remove git

USER node
RUN export HUSKY=0 \
    && npm install --omit=dev \
    && rm -f .npmrc \
    && ls -al
COPY --chown=node:node . .

USER root
EXPOSE 8080

ENTRYPOINT ["/usr/bin/tini", "--"]

CMD ["sh", "-c", "node ./server.js"]

ENV NO_PROXY=""
ENV PACKAGE=true