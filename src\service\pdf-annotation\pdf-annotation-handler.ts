import { PDFDocument } from 'pdf-lib';
import { AnnotationType } from './types';
import { generateAnnotations } from './canvas/generate-annotations';

export async function pdfAnnotationHandler(pdfBytes: Buffer, annotationJsonBytes: <PERSON>uffer) {
  const json: AnnotationType = JSON.parse(annotationJsonBytes.toString('utf8'));
  return pdfAnnotationHandlerJson(pdfBytes, json);
}

export async function pdfAnnotationHandlerJson(pdfBytes: Buffer, json: AnnotationType) {
  const pdfDoc: PDFDocument = await PDFDocument.load(pdfBytes);
  await generateAnnotations(pdfDoc, json);
  return await pdfDoc.save();
}
