/* eslint-disable space-before-function-paren */
import express, { Application, Request, Response } from 'express';
import helmet from 'helmet';
import cors from 'cors';
import { Controller } from './types';
import path from 'path';
import fileUpload from 'express-fileupload';
import LoggerCenter, { WinstonLogLevel } from './logger/LoggerCenter'; // using this logger?
import { rateLimit } from 'express-rate-limit';
import ServiceCenter from './service/ServiceCenter';
import * as bodyParser from 'body-parser';
import cookieParser from 'cookie-parser';
// import './auth/passport/auth';

const loggerName = path.basename(__filename);
const logger = LoggerCenter.getLogger(loggerName);

LoggerCenter.setLevel(loggerName, WinstonLogLevel.Info);

export class App {
  private readonly app: Application;
  private readonly controllers: Controller[];
  private readonly serverPort: number;

  public static readonly ADMIN_ROLE = 'ESP_ADMIN';
  public static readonly APP_ID = 'ESP';

  constructor(controllers: Controller[], serverPort: number) {
    this.controllers = controllers;
    this.serverPort = serverPort;
    this.app = express();
  }

  public async init() {
    this.app.use(helmet({
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      },
      frameguard: {
        action: 'deny'
      },
      xssFilter: true,
      noSniff: true,
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", "'unsafe-inline'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          imgSrc: ["'self'", "data:"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"],
        }
      }
    }));

    this.app.use(bodyParser.json({ limit: '50mb' }));
    this.app.use(
      bodyParser.urlencoded({
        limit: '50mb',
        extended: false,
        parameterLimit: 50000
      })
    );
    const whitelist = process.env.CORS_WHITE_LIST?.split(',') || [];
    //     || [
    //   'http://localhost:3000',
    //   'http://localhost:3001',
    //   'http://localhost:8080',
    //   'https://dev.esp.swissre.com'
    // ]
    if (whitelist.length === 0) {
      logger.warn('CORS_WHITE_LIST in not defined (.env or command line!)!');
    }
    this.app.use(
      cors({
        origin: function (origin: string, callback: (arg0: null, arg1: boolean) => any) {
          // allow requests with no origin
          if (!origin) return callback(null, true);
          if (whitelist[0] === '*') return callback(null, true);
          if (whitelist.indexOf(origin) === -1) {
            const message = `The CORS policy for this origin doesn't
          allow access from the particular origin.`;
            logger.error(message);
            return callback(null, false);
          }
          return callback(null, true);
        },
        credentials: true
      })
    );

    this.app.use(function (req, res, next) {
      res.header('Content-Type', 'application/json;charset=UTF-8');
      res.header('Access-Control-Allow-Credentials', 'true');
      res.header('Access-Control-Allow-Headers', 'Authorization, Origin, X-Requested-With, Content-Type, Accept');
      next();
    });

    const limiter = rateLimit({
      windowMs: 60 * 1000, // 5 minutes
      limit: 1000, // Limit each IP to 1000 requests per `window` (here, per 5 minutes).
      standardHeaders: 'draft-7', // draft-6: `RateLimit-*` headers; draft-7: combined `RateLimit` header
      legacyHeaders: false, // Disable the `X-RateLimit-*` headers.
      message: 'Too many requests from this IP, please try again later.'
    });
    if (!ServiceCenter.getInstance().isLocal()) {
      this.app.use(limiter);
    }

    this.app.set('trust proxy', +(process.env.TRUST_PROXY_NUMBER || 1))

    this.app.use(cookieParser());

    this.app.use(
      fileUpload({
        createParentPath: true,
        preserveExtension: true,
        safeFileNames: true,
        defCharset: 'utf8'
      })
    );
    this.initializeControllers(this.controllers);
    this.app.use(
      '/favicon.ico',
      function (
        _req: any,
        res: {
          sendFile: (
            arg0: string,
            arg1: {
              headers: { 'Content-Disposition': string; 'Content-Type': string };
            }
          ) => void;
        }
      ) {
        res.sendFile(path.join(__dirname, 'favicon.jpg'), {
          headers: { 'Content-Disposition': 'inline', 'Content-Type': 'image/jpg' }
        });
      }
    );

    // Default url:
    this.app.use('/', (_req, res) => res.send('Hello from esp-be server!'));

    // error handling
    this.app.use(errorMiddleware);
  }

  private initializeControllers(controllers: Controller[]) {
    controllers.forEach((controller) => {
      for (const path of controller.paths) {
        this.app.use(path, controller.router);
        logger.info(`Initialize controller ${controller.name} with path: ${path}`);
      }
    });
  }

  public listen(): void {
    const port = this.serverPort;
    this.app?.listen(port, () => {
      logger.info(`Listening on port ${port}`);
    });
  }
}

function errorMiddleware(error: any, _request: Request, response: Response): void {
  let responseStatus: number | undefined = undefined;
  let errorMessage: string | undefined = undefined;
  if (error['response']) {
    responseStatus = error['response'].status;
    if (error['response']['headers']['message']) {
      errorMessage = error['response']['headers']['message'];
    }
  }
  if (!errorMessage) {
    errorMessage = error.message;
  }
  const requestPath: string | undefined =
    error['request'] && error['request']['path'] ? error['request']['path'] : undefined;
  const status = responseStatus || 500;

  const message = errorMessage || 'Internal Server Error';
  logger.error(
    `error-status=${status} , message=${message} ${(requestPath ? ', request.path=' + requestPath : '')}\n ${error.stack}`
  );
  response.status(status).header('message', message).contentType('text/plain').send();
}
