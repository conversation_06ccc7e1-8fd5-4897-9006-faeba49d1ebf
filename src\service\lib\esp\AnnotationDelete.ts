import { ProcessLine, SqNodeFunction, SqResult, warnLine } from '../../../serviceQuery';

import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';
import ServiceCenter from '../../ServiceCenter';
import { track } from './DBLogUtils';

const logger = LoggerCenter.getLogger(path.basename(__filename));
export const AnnotationDelete: SqNodeFunction = async ({ request }): Promise<SqResult> => {
  const sc = ServiceCenter.getInstance();

  const { userId, parameters } = request;
  logger.info('AnnotationDelete START : user: ');

  const documentFileTidList = (parameters['documentFileTidList'] || '').toString();
  const documentFileTidArray = documentFileTidList.split(',');

  const trackingMessage = 'documentFileTidList: ' + documentFileTidList;

  const lines: ProcessLine[] = [];

  if (documentFileTidArray.length === 0) {
    lines.push(warnLine(`documentFileTidList parameter list empty! (${userId})`));
  } else {
    for (const documentFileTid of documentFileTidArray) {
      await sc.runSystemSq({ serviceId: 'AnnotationDeleteSql', parameters: { documentFileTid, $USERID: userId } });
    }
  }
  await track('I', trackingMessage, 'AnnotationDelete', userId);

  return { name: 'AnnotationDelete', processInfo: { lines } };
};
