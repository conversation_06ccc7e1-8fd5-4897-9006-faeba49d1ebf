export enum InstrumentType {
  COMMENT = 1,
  ERASER = 2,
  MARKER = 3,
  PENCIL = 4,
  ARROW = 5,
  FREE_TEXT = 31
}

export type AnnotationType = {
  pages: AnnotationPage[];
};

export type AnnotationPage = {
  layers: AnnotationLayer[];
  pageMeta: AnnotationPageMeta;
};

export type AnnotationLayer = {
  objects: DrawOperation[];
};

export type AnnotationPageMeta = {
  pageNumber: number;
};

export type DrawOperation = {
  points: AnnotationPoint[];
  objectMeta: ObjectMeta;
};

export type AnnotationPoint = string[];
export type AnnotationPointN = number[];

export type ObjectMeta = {
  instrument: InstrumentType;
  color: string;
  style: number;
  timestamp: number;
  width: number;
  scale: number;
  text?: string;
};
