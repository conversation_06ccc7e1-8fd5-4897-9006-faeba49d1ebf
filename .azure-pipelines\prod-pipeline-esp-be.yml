trigger: none

pool: SR-Ubuntu-Default

variables:
  - template: ./prod-variables.yml

stages:
  - stage: PROD
    jobs:
      - deployment: Build_Deploy_env_PROD
        condition: succeeded()
        displayName: 'Build and Deploy to PROD environment'
        environment: 'PROD'
        strategy:
         runOnce:
           deploy:
              steps:
                - checkout: self
                - task: SwissReMultistageAuditorExp@0
                  displayName: Auditor
                  inputs:
                    enableChangeManagementIntegration: true
                    StandardTemplateService: "Standard Change Template Source"
                    standardchangeid: "Production: STDTEMPL1012561 (ESP Azure production CI/CD template)"
                - task: SwissReQueryRelease@0
                  displayName: 'Swiss Re Authenticator'
                  name: Authenticator
                  inputs:
                    ReleaseAuthenticatorConnection: 'Release Authenticator'
                - template: ./steps.yml
                  parameters:
                    apmId: $(apmId)
                    clusterEnv: $(clusterEnv)
                    deploymentEnv: $(deploymentEnv)
                    imageTag: $(imageTag)
                    kubeloginVersion: $(kubeloginVersion)
                    serviceConnection: $(serviceConnection)
                    stage: $(stage)
                    npmBuildScript: build
