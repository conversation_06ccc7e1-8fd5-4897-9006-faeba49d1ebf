import { SqRecord } from '../serviceQuery';
import { PATH_SEPARATOR } from './constants';
import moment from 'moment/moment';

export const canonicalPath = (directory2 = '') => {
  directory2 = directory2.replace('\\', PATH_SEPARATOR);
  let counter = 0;
  while (directory2.includes('//') && counter < 100) {
    directory2 = directory2.replace('//', PATH_SEPARATOR);
    counter++;
  }
  if (!directory2.endsWith(PATH_SEPARATOR)) {
    directory2 = directory2 + PATH_SEPARATOR;
  }
  if (!directory2.startsWith(PATH_SEPARATOR)) {
    directory2 = PATH_SEPARATOR + directory2;
  }
  return directory2;
};

export function nowIso() {
  return moment().format('YYYY-MM-DD hh:mm:ss');
}

export const canUploadFiles = (roles: string[], serviceId = '?') =>
  ['AnnotationUpload', 'Crashlog'].includes(serviceId) ||
  roles.reduce<boolean>(
    (a, role) => (role.includes('ADMIN') || role.includes('PROVIDER') || role.includes('SUPERVISOR') ? true : a),
    false
  );

export const stringValue = (parameters: SqRecord, name: string, defaultValue: string = '') =>
  (parameters[name] || defaultValue).toString();
