/* eslint-disable no-console,space-before-function-paren */
import { test } from 'mocha';
import { localUrlNode } from '../common';
import axios from 'axios';
import { expect } from 'chai';

const crashlogUrl = `${localUrlNode}mobile/restJson/Crashlog`;

const testname = '(webtests) crashlog-test';
test(testname, async () => {
  return Promise.all([doTest()]);
});

async function doTest() {
  const headers = {
    'Content-Type': 'multipart/form-data'
  };
  const crashLogContent = Buffer.from('This is just a test !', 'utf8');
  const blob = new Blob([crashLogContent]);
  const formData = new FormData();
  formData.append('crashlogDir', 'tonitest');
  formData.append('crashlog', `Just a Test from ${new Date()}`);
  formData.append('version', 'version 1.2.3.go');
  formData.append('test-file', blob, 'crashlog-test.txt'); // Replace with your file path

  const start = Date.now();
  const response = await axios.post(crashlogUrl, formData, { headers });
  console.log(`${testname} successful, time used: ${Date.now() - start}ms`);
  expect(!!response.data).true;
  console.debug(`response ${response.data}`);
}
