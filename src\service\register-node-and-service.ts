import { ServiceQuery } from '../serviceQuery';
import { SendData } from './lib/esp/SendData';
import { InsertAppProperties } from './lib/esp/InsertAppProperties';
import { UploadFile } from './lib/esp/UploadFile';
import { XlsxSqProcessing } from './lib/esp/XlsxSqProcessing';
import { UserInfo } from './lib/esp/UserInfo';
import { LoadSqSqlFiles } from './lib/esp/LoadSqSqlFiles';
import { RequestArrayProcessor } from './lib/esp/RequestArrayProcessor';
import { MyAccessProfile } from './lib/esp/MyAccessProfile';
import { AnnotationDelete } from './lib/esp/AnnotationDelete';
import { AnnotationDownload } from './lib/esp/AnnotationDownload';
import { Crashlog } from './lib/mobile/Crashlog';
import { MeetingDocumentUpload } from './lib/esp/MeetingDocumentUpload';
import { CreateMPMessages } from './lib/esp/CreateMPMessages';
import { MP_TYPE__insert } from './lib/esp/MP_Type__insert';
import { MailTask } from './lib/esp/MailUtils';
import { AnnotationUpload } from './lib/esp/AnnotationUpload';
import { Getkeys2 } from './lib/mobile/GetKeys2';
import { MUpdateService } from './lib/MUpdateService';
import { UploadZip } from './lib/UploadZip';
import { TempDocumentUpload } from './lib/esp/TempDocumentUpload';
import { MailServiceTester } from './lib/esp/MailServiceTester';
import { ReportMailMA } from './lib/esp/ReportMailMA';
import { SetReadFlag } from './lib/mobile/SetReadFlag';
import { DBFileUpdateRealLength } from './lib/esp/DBFileUpdateRealLength';
import { DBFileUpdateMissingPreviews } from './lib/esp/DBFileUpdateMissingPreviews';
import { LoggerCenterManager } from './lib/LoggerCenterManager';
import { LogMobileInsert } from './lib/mobile/LogMobileInsert';

export function registerNodeAndService(sq: ServiceQuery) {
  sq.registerNode('com.swissre.serviceQuery.JavaLogManager', LoggerCenterManager);

  sq.registerNode('com.swissre.serviceQuery.lib.LoadSqSqlFiles', LoadSqSqlFiles);
  sq.registerNode('com.swissre.serviceQuery.lib.MailTask', MailTask);
  sq.registerNode('com.swissre.esp.service.queryServices.MailServiceTester', MailServiceTester);
  sq.registerNode('com.swissre.serviceQuery.lib.MyAccessProfile', MyAccessProfile);
  sq.registerNode('com.swissre.serviceQuery.lib.RequestArrayProcessor', RequestArrayProcessor);
  sq.registerNode('com.swissre.serviceQuery.lib.SendData', SendData);
  sq.registerNode('com.swissre.serviceQuery.lib.UploadFile', UploadFile);
  sq.registerNode('com.swissre.esp.service.queryServices.TempDocumentUpload', TempDocumentUpload);
  sq.registerNode('com.swissre.esp.service.queryServices.AnnotationDelete', AnnotationDelete);
  sq.registerNode('com.swissre.esp.service.queryServices.AnnotationDownload', AnnotationDownload);
  sq.registerNode('com.swissre.esp.service.queryServices.AnnotationUpload', AnnotationUpload);
  sq.registerNode('com.swissre.esp.service.queryServices.MeetingDocumentUpload', MeetingDocumentUpload);
  sq.registerNode('com.swissre.esp.service.queryServices.CreateMPMessages', CreateMPMessages);
  sq.registerNode('com.swissre.esp.service.queryServices.SetReadFlag', SetReadFlag);
  sq.registerNode('com.swissre.esp.service.queryServices.ReportMailMA', ReportMailMA);
  sq.registerNode('com.swissre.serviceQuery.lib.DBFileUpdateRealLength', DBFileUpdateRealLength);

  // mobile
  sq.registerNode('com.swissre.serviceMobile.lib.Crashlog', Crashlog);
  sq.registerNode('com.swissre.serviceMobile.lib.Getkeys2', Getkeys2);
  sq.registerNode('com.swissre.serviceMobile.lib.MUpdateService', MUpdateService);

  sq.addService({
    serviceId: 'insertAppProperties',
    serviceFunction: InsertAppProperties,
    roles: ['ESP_ADMIN']
  });

  sq.addService({
    serviceId: 'UploadTmpFile',
    serviceFunction: UploadFile,
    roles: ['ESP_ADMIN']
  });

  sq.addService({
    serviceId: 'xlsxSqProcessing',
    serviceFunction: XlsxSqProcessing,
    roles: ['ESP_ADMIN']
  });

  sq.addService({
    serviceId: 'UserInfo',
    serviceFunction: UserInfo
  });
  sq.addService({
    serviceId: 'MP_TYPE.insert',
    serviceFunction: MP_TYPE__insert
  });

  sq.addService({
    serviceId: 'UploadZip',
    serviceFunction: UploadZip
  });

  sq.addService({
    serviceId: 'DBFileUpdateMissingPreviews',
    serviceFunction: DBFileUpdateMissingPreviews,
    roles: ['ESP_ADMIN']
  });

  sq.addService({
    serviceId: 'm.logMobile.insert',
    serviceFunction: LogMobileInsert,
    roles: []
  });

  sq.addService({
    serviceId: 'm.log.insert',
    serviceFunction: LogMobileInsert,
    roles: []
  });
}
