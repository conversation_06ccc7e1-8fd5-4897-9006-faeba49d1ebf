/* eslint-disable no-console,space-before-function-paren,quotes */
import { test } from 'mocha';
import * as dotenv from 'dotenv';
import { localUrlJava, localUrlNode, outdataDir } from '../common';
import axios from 'axios';
import { compareJson } from '../test-utils';
import { toList } from '../../src/serviceQuery';
import path from 'path';
import { writeFileSync } from 'fs';

dotenv.config();

// TREE SERVICES
const SERVICE_IDS = [
  'v_iesp_prereadings_tree_meetings'
  // 'v_iesp_prereadings_tree_meetings_current',
  // 'v_iesp_prereadings_tree_meetings_past',
  // 'v_iesp_prereadings_tree_documents',
  // 'ipad_client_properties',
  // ];
  //
  // // CR SERVICES
  // const SERVICE_IDS = [
  // 'meeting_detail_allcr',
  // 'cr_get_question_simple_all',
  // 'cr_agenda_list',
  // 'cr_documents_all',
  // 'cr_results_byagenda_agg_all',
  // 'cr_answer_report_byagenda_all'
];

const SAVE_RESULT = true;

for (const serviceId of SERVICE_IDS) {
  test(`(webtests) ${serviceId}`, async () => {
    const sqPart = `serviceQuery/${serviceId}`;

    const localUrlJavaStart2 = `${localUrlJava}${sqPart}`;
    const localUrlNodeStart2 = `${localUrlNode}${sqPart}`;

    console.log(`Start Java ${serviceId} url: ${localUrlJavaStart2}!`);
    const resultJava = toList((await axios.get(localUrlJavaStart2, {})).data);

    console.log(`Start Node ${serviceId} url: ${localUrlNodeStart2}!`);
    const resultNode = toList((await axios.get(localUrlNodeStart2, {})).data);

    if (SAVE_RESULT) {
      const outJava = path.join(outdataDir, 'sqdata', `SQ-COMP-${serviceId}-java.json`);
      const outNode = path.join(outdataDir, 'sqdata', `SQ-COMP-${serviceId}-node.json`);
      writeFileSync(outJava, JSON.stringify(resultJava));
      writeFileSync(outNode, JSON.stringify(resultNode));
    }

    compareJson(resultNode, resultJava, {
      ignore: ['dbExecutionTime', 'dbFetchingTime', 'created', 'timeStart'],
      isJsonString: []
    });
  });
}
