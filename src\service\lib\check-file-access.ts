import ServiceCenter from '../ServiceCenter';
import AppPropertiesService from './AppPropertiesService';
import { singleValue, toList } from '../../serviceQuery';
import { DbFile, DBFileSelect } from './esp/types';
import { _derived, SYSTEM } from '../constants';

export const publicDirectories: string[] = [
  '/var/definitions/uploads/',
  '/var/admin/documents/',
  '/var/misc/icons/',
  '/var/avatars/',
  '/home/<USER>/',
  '/web/images/'
];

export async function checkFileAccess(dbFile: DBFileSelect, { userId, roles }: { userId: string; roles: string[] }) {
  const $USERID = userId;
  const sq = ServiceCenter.getInstance().getSq();
  const { fileTid, directory, owner } = dbFile;

  if (isOwner(owner, userId) || isPublicDirectory(directory) || isHomeDirectory(directory, userId) || isDerivedFile(directory, userId) || isAdminDirectory(directory, roles)) {
    return true;
  }

  if (await hasUserFileAccess(sq, fileTid, $USERID)) {
    return true;
  }

  if (await hasServiceFileAccess(sq, fileTid, $USERID)) {
    return true;
  }

  return false;
}

const isOwner = (owner: string, userId: string): boolean => {
  return owner === userId;
};

const isPublicDirectory = (directory: string): boolean => {
  return publicDirectories.some(pubDir => directory.startsWith(pubDir));
};

const isHomeDirectory = (directory: string, userId: string): boolean => {
  return directory.startsWith('/home/' + userId + '/');
};

const isDerivedFile = (directory: string, userId: string): boolean => {
  const directoryParts = directory.split('/');
  return directoryParts.length > 2 && directory.startsWith(_derived) && userId === directoryParts[2];
};

const isAdminDirectory = (directory: string, roles: string[]): boolean => {
  return directory.startsWith('/var') && roles.includes('ESP_ADMIN');
};

const hasUserFileAccess = async (sq: any, fileTid: string, $USERID: string): Promise<boolean> => {
  const res = await sq.run({
    serviceId: 'userFileAccess',
    parameters: { fileTid, $USERID },
    userId: SYSTEM,
    roles: []
  });
  const right = +(singleValue(res) || 0);
  return right > 0;
};

const hasServiceFileAccess = async (sq: any, fileTid: string, $USERID: string): Promise<boolean> => {
  const t = await AppPropertiesService.getInstance().get('FileAccessServices');
  if (t) {
    const services0 = t.split(',');
    for (const serviceId of services0) {
      const res = await sq.run({ serviceId, parameters: { fileTid, $USERID } });
      const files = toList<DbFile>(res);
      if (files.some(f => f.fileTid === fileTid)) {
        return true;
      }
    }
  }
  return false;
};
