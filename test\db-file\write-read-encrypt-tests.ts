import { endTest, getTestSc, getTestSq } from '../init-local';
import { expect } from 'chai';
import LoggerCenter from '../../src/logger/LoggerCenter';
import path from 'path';
import { PSQLDriverExtension } from '../../src/serviceQuery/PSQLDriver';
import { getDbFileContent, saveDbFile } from '../../src/service/lib/upload-file-utils';
import { isSqExceptionResult } from '../../src/serviceQuery';

const logger = LoggerCenter.getLogger(path.basename(__filename));

const DESCR = 'write-read-and-encrypt-tests';

const filename = 'write-read-and-encrypt-tests.txt';
const directory = '/daily_temp/';
const owner = 'Nobody';
const data = Buffer.from('Hello World');
const data2 = Buffer.from('Hello World - Updated !');

describe(DESCR, async () => {
  logger.info(`Start ${DESCR}`);
  let fileTid = '';

  it(`save new file`, async () => {
    getTestSc();
    const newFile = { filename, directory, owner };

    const res = await saveDbFile({ ...newFile, data });
    if (isSqExceptionResult(res)) {
      expect.fail(`Saving new file failed: ${res.exception}`);
    } else {
      fileTid = res.fileTid;
      expect(fileTid).is.not.empty;
    }
  });

  it(`update content file`, async () => {
    const dbFile = {
      fileTid,
      filename,
      directory,
      owner,
      data: data2
    };
    const res = await saveDbFile(dbFile);
    if (isSqExceptionResult(res)) {
      expect.fail(`Updating file failed: ${res.exception}`);
    } else {
      fileTid = res.fileTid;
      expect(res.fileTid).is.not.empty;
      expect(res.fileTid).equals(fileTid);
    }
  });

  it(`check changed content`, async () => {
    const res = await getDbFileContent(fileTid);
    if (isSqExceptionResult(res)) {
      expect.fail(`Getting file content failed: ${res.exception}`);
    } else {
      expect(Buffer.compare(res, data2)).equals(0);
    }
  });

  it('cleanup', async () => {
    const pgDriver = getTestSq().driver as PSQLDriverExtension;
    if (fileTid) {
      await pgDriver.processSql('delete from esp.t_file where file_tid = :fileTid', { fileTid });
    }
    endTest();
  });
});
