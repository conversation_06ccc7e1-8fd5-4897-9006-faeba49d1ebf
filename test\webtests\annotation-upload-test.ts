/* eslint-disable no-console,space-before-function-paren */
import { test } from 'mocha';
import path from 'path';
import { localUrlNode, testdataDir } from '../common';
import axios from 'axios';
import fs, { readFileSync } from 'fs';
import { expect } from 'chai';
import { singleValue } from '../../src/serviceQuery';

const url = `${localUrlNode}serviceQuery/AnnotationUpload`;

// const documentFileTid = 142875589;
// const documentFileTid = 142875589;
const documentFileTid = 142875589;

const cabcomFile = path.join(testdataDir, 'cabcom-arrows.zip');
// const cabcomFile = path.join(testdataDir, 'freetext-test-annot.zip');
const testname = '(webtests) annotation-upload-test';
test(testname, async () => {
  return Promise.all([doTest(), doTest(), doTest(), doTest(), doTest(), doTest(), doTest(), doTest()]);
});

async function doTest() {
  expect(fs.existsSync(cabcomFile)).equals(true, `${cabcomFile} does not exist!`);
  const zipContent = readFileSync(cabcomFile);
  const blob = new Blob([zipContent]);

  const headers = {
    'Content-Type': 'multipart/form-data'
  };
  const formData = new FormData();
  formData.append('upload-file', blob, 'cabcom.zip'); // Replace with your file path
  formData.append('documentFileTid', documentFileTid.toString());

  try {
    const start = Date.now();
    const response = await axios.post(url, formData, { headers });
    console.log(`${testname} successful, time used: ${Date.now() - start}ms`);
    const exception = response.data.exception;
    if (exception) {
      expect.fail(`Exception in ${testname}; ${exception}`);
    }
    expect(!!response.data).true;
    expect(!!response.data.header).true;
    expect(response.data.header[0]).equals('annotationfiletid');
    expect(response.data.header[1]).equals('annotatedfiletid');
    expect(!!singleValue(response.data)).true;
    expect(!!response.data.table[0][1]).true;
    expect(+singleValue(response.data), 'annotationfiletid is wrong').gt(0);
    expect(+response.data.table[0][1], 'annotatedfiletid is wrong').gt(0);
    console.log(`header : ${JSON.stringify(response.data.header)}`);
    console.log(`table  : ${JSON.stringify(response.data.table)}`);
  } catch (error) {
    console.error(error);
    expect.fail(`Error received from annotation upload ${error}`);
  }
}
