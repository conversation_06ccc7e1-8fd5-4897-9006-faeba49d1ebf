/* eslint-disable no-console,space-before-function-paren */
import path from 'path';
import { expect } from 'chai';
import pako from 'pako';

describe('compression-test', () => {
  it('test-1', async () => {
    const zipData = pako.gzip('test test');
    const u1 = pako.ungzip(zipData);
    console.log(Buffer.from(u1).toString());
    console.log('...');
  });

  it('array test: in and of reminder', async () => {
    for (const e in ['Hello']) {
      expect(e).equals('0');
    }
    for (const e of ['Hello']) {
      expect(e).equals('Hello');
    }
    for (const e in { hello: 'world' }) {
      expect(e).equals('hello');
    }
  });

  it('array test: should fail if arrays have different values', () => {
    const arr1 = [1, 2, 3];
    const arr2 = [1, 2, 4];

    expect(arr1).to.not.deep.equal(arr2);
  });

  it('Json serial', () => {
    const joes = "joe's";
    expect(joes).to.equal("joe's");
  });
  it('check path functions', () => {
    expect(path.basename('a/b/c/test.pdf')).to.equal('test.pdf');
    expect(path.basename('//a/b/c/test.pdf')).to.equal('test.pdf');
    expect(path.basename('/a/b/c/test.pdf')).to.equal('test.pdf');
    expect(path.basename('test.pdf')).to.equal('test.pdf');
    expect(path.basename('./test.pdf')).to.equal('test.pdf');

    expect(path.dirname('a/b/c/test.pdf')).to.equal('a/b/c');
    expect(path.dirname('//a/b/c/test.pdf')).to.equal('//a/b/c');
    expect(path.dirname('/a/b/c/test.pdf')).to.equal('/a/b/c');
    expect(path.dirname('test.pdf')).to.equal('.');
    expect(path.dirname('./test.pdf')).to.equal('.');

    expect(path.normalize('a/b/c/test.pdf')).to.equal('a\\b\\c\\test.pdf');
    expect(path.normalize('//a/b/c/test.pdf')).to.equal('\\\\a\\b\\c\\test.pdf');
    expect(path.normalize('/a/b/c/test.pdf')).to.equal('\\a\\b\\c\\test.pdf');
    expect(path.normalize('test.pdf')).to.equal('test.pdf');
    expect(path.normalize('./test.pdf')).to.equal('test.pdf');
  });
});
