import ServiceCenter from '../src/service/ServiceCenter';
import { expect } from 'chai';
import * as dotenv from 'dotenv';

dotenv.config();

export const getTestSc = () => ServiceCenter.getInstance();
export const getTestSq = () => {
  const sq = ServiceCenter.getInstance().getSq();
  expect(!!sq).true;
  return sq;
};

export const endTest = () => {
  getTestSc().destroy();
};

export const getLocalUserId = () => process.env.LOCAL_USER_ID;
