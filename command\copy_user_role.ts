const { Client } = require('pg');
const fs = require('fs');

// Database connection configuration
interface DbConfig {
    user: string;
    host: string;
    database: string;
    password: string;
    port: number;
    ssl?: boolean | object;
}

// Command line argument type
interface CommandArgs {
    userIds: string[];
    sourceDb: DbConfig;
    targetDb: DbConfig;
}

/**
 * This function copies user roles from a source database to a target database.
 * It deletes existing user roles in the target database and inserts new ones from the source database.
 * @param inputUserIds 
 * @param sourceDbConfig 
 * @param targetDbConfig 
 */
async function copyUserRole(inputUserIds: string[] = [], sourceDbConfig: DbConfig, targetDbConfig: DbConfig): Promise<void> {
    console.log('Connecting to source database:', `${sourceDbConfig.user}@${sourceDbConfig.host}:${sourceDbConfig.port}/${sourceDbConfig.database}`);
    console.log('Connecting to target database:', `${targetDbConfig.user}@${targetDbConfig.host}:${targetDbConfig.port}/${targetDbConfig.database}`);

    const sourceClient = new Client(sourceDbConfig);
    const targetClient = new Client(targetDbConfig);

    try {
        await sourceClient.connect();
        console.log('Connected to source database');

        await targetClient.connect();
        console.log('Connected to target database');

        // Use provided user IDs or default values
        const userIds = inputUserIds.length > 0
            ? inputUserIds
            : ['S1BTJG'];

        console.log(`Migrating data for the following users: ${userIds.join(', ')}`);

        // Begin transaction in target database
        console.log('Starting transaction in target database');
        await targetClient.query('BEGIN');

        try {
            // Define tables in reverse order for deletion to handle foreign key constraints properly
            const deleteTables = [
                { name: 't_access_spec_input', join: 't_sr_role', on: 'role_name', condition: 'user_id' },
                { name: 't_sr_role', condition: 'user_id' },
                { name: 't_sr_user', condition: 'user_id' },
                { name: 't_role', condition: 'user_id' },
            ];

            // Define tables in the correct order for insertion
            const insertTables = [
                { name: 't_role', condition: 'user_id' },
                { name: 't_sr_user', condition: 'user_id' },
                { name: 't_sr_role', condition: 'user_id' },
                { name: 't_access_spec_input', join: 't_sr_role', on: 'role_name', condition: 'user_id' },
            ];

            // Step 1: Delete existing data for specified users in target database
            console.log('Step 1: Deleting existing data in target database...');
            for (const table of deleteTables) {
                try {
                    console.log(`Deleting data from table: ${table.name}`);

                    if (table.join) {
                        // For tables with joins, we need to first identify the records to delete
                        const findQuery = `SELECT r.${table.on} FROM esp.${table.join} r 
                                          WHERE r.${table.condition} = ANY($1)`;
                        const relatedRows = await targetClient.query(findQuery, [userIds]);

                        if (relatedRows.rows.length > 0) {
                            // For t_access_spec_input, we need to get the actual records with all their fields to properly delete them
                            if (table.name === 't_access_spec_input') {
                                const relatedValues = relatedRows.rows.map((row: Record<string, any>) => row[table.on]);

                                // Get a complete list of records to delete with all their primary key fields
                                const fullRecordsQuery = `SELECT role_name, filetype_nme, access_right FROM esp.${table.name} WHERE role_name = ANY($1)`;
                                const fullRecords = await targetClient.query(fullRecordsQuery, [relatedValues]);

                                // Delete records one by one with all primary key fields
                                console.log(`Found ${fullRecords.rows.length} detailed records to delete in ${table.name}`);
                                for (const record of fullRecords.rows) {
                                    const deleteDetailQuery = `DELETE FROM esp.${table.name} 
                                                             WHERE role_name = $1 
                                                             AND filetype_nme = $2 
                                                             AND access_right = $3`;
                                    await targetClient.query(deleteDetailQuery, [
                                        record.role_name,
                                        record.filetype_nme,
                                        record.access_right
                                    ]);
                                }

                                console.log(`Deleted ${fullRecords.rows.length} records from table ${table.name}`);
                            } else {
                                // For other tables, use the standard approach
                                const relatedValues = relatedRows.rows.map((row: Record<string, any>) => row[table.on]);
                                const deleteQuery = `DELETE FROM esp.${table.name} WHERE ${table.on} = ANY($1)`;
                                const result = await targetClient.query(deleteQuery, [relatedValues]);
                                console.log(`Deleted ${result.rowCount} records from table ${table.name}`);
                            }
                        } else {
                            console.log(`No related records found in ${table.name} to delete`);
                        }
                    } else {
                        // For tables with direct conditions
                        const deleteQuery = `DELETE FROM esp.${table.name} WHERE ${table.condition} = ANY($1)`;
                        const result = await targetClient.query(deleteQuery, [userIds]);
                        console.log(`Deleted ${result.rowCount} records from table ${table.name}`);

                    }
                } catch (error) {
                    console.error(`Error deleting from table ${table.name}:`, error);
                    throw error;
                }
            }

            // Step 2: Insert data from source database to target database
            console.log('Step 2: Inserting data from source database into target database...');
            for (const table of insertTables) {
                console.log(`Processing table: ${table.name}`);

                try {
                    if (table.join) {
                        const query = `SELECT ta.* FROM esp.${table.name} ta JOIN esp.${table.join} r ON ta.${table.on} = r.${table.on} WHERE r.${table.condition} = ANY($1)`;
                        console.log(`Executing query: ${query}`);
                        const result = await sourceClient.query(query, [userIds]);
                        console.log(`Retrieved ${result.rows.length} records from source table`);

                        for (const row of result.rows) {
                            const columns = Object.keys(row).join(', ');
                            const values = Object.values(row);
                            const placeholders = values.map((_, i) => `$${i + 1}`).join(', ');

                            // Special handling for t_access_spec_input to handle primary key constraint
                            if (table.name === 't_access_spec_input') {
                                // Check if record already exists
                                const checkQuery = `SELECT COUNT(*) FROM esp.${table.name} 
                                                  WHERE role_name = $1 
                                                  AND filetype_nme = $2 
                                                  AND access_right = $3`;
                                const checkResult = await targetClient.query(checkQuery, [
                                    row.role_name,
                                    row.filetype_nme,
                                    row.access_right
                                ]);

                                if (parseInt(checkResult.rows[0].count) === 0) {
                                    // Only insert if record doesn't exist
                                    const insertQuery = `INSERT INTO esp.${table.name} (${columns}) VALUES (${placeholders})`;
                                    await targetClient.query(insertQuery, values);
                                    console.log(`Inserted record into ${table.name} for role: ${row.role_name}, filetype: ${row.filetype_nme}`);
                                } else {
                                    console.log(`Skipping duplicate record in ${table.name} for role: ${row.role_name}, filetype: ${row.filetype_nme}`);
                                }
                            } else {
                                // For other tables, use standard insert
                                const insertQuery = `INSERT INTO esp.${table.name} (${columns}) VALUES (${placeholders})`;
                                await targetClient.query(insertQuery, values);
                            }
                        }
                    } else {
                        const query = `SELECT * FROM esp.${table.name} WHERE ${table.condition} = ANY($1)`;
                        console.log(`Executing query: ${query}`);
                        const result = await sourceClient.query(query, [userIds]);
                        console.log(`Retrieved ${result.rows.length} records from source table`);

                        for (const row of result.rows) {
                            const columns = Object.keys(row).join(', ');
                            const values = Object.values(row);
                            const placeholders = values.map((_, i) => `$${i + 1}`).join(', ');

                            // Insert into target database
                            const insertQuery = `INSERT INTO esp.${table.name} (${columns}) VALUES (${placeholders})`;
                            await targetClient.query(insertQuery, values);
                            console.log(`Executing insert query: ${insertQuery}`, values);
                        }
                    }
                    console.log(`Table ${table.name} processing completed`);
                } catch (error) {
                    console.error(`Error processing table ${table.name}:`, error);
                    throw error; // Re-throw error to ensure process termination
                }
            }

            // If everything succeeded, commit the transaction
            console.log('All operations successful, committing transaction');
            await targetClient.query('COMMIT');
            console.log('Transaction committed successfully');

        } catch (error) {
            // If any error occurs, rollback the transaction
            console.error('Error occurred during data migration, rolling back transaction:', error);
            await targetClient.query('ROLLBACK');
            console.log('Transaction rolled back successfully');
            throw error;
        }

        console.log('Data migration successfully completed.');
    } catch (error) {
        console.error('Error occurred during data migration:', error);
        throw error;
    } finally {
        await sourceClient.end();
        await targetClient.end();
        console.log('Database connections closed');
    }
}

function parseCommandLineArgs(): CommandArgs {
    const args = process.argv.slice(2);
    const userIds: string[] = [];
    const config: CommandArgs = {
        userIds: [],
        sourceDb: {
            user: process.env.SOURCE_DB_USER || 'esp_trx',
            host: process.env.SOURCE_DB_HOST || '*************',
            database: process.env.SOURCE_DB_DATABASE || 'pdp0006824',
            password: process.env.SOURCE_DB_PASSWORD || '',
            port: parseInt(process.env.SOURCE_DB_PORT || '5432'),
            ssl: process.env.SOURCE_DB_SSL === 'true' ? { rejectUnauthorized: false } : false
        },
        targetDb: {
            user: process.env.TARGET_DB_USER || 'esp_trx',
            host: process.env.TARGET_DB_HOST || 'localhost',
            database: process.env.TARGET_DB_DATABASE || 'pdp0006824',
            password: process.env.TARGET_DB_PASSWORD || '',
            port: parseInt(process.env.TARGET_DB_PORT || '5432'),
            ssl: process.env.TARGET_DB_SSL === 'true' ? { rejectUnauthorized: false } : false
        }
    };

    // Parse command line arguments
    for (const arg of args) {
        if (arg.startsWith('--')) {
            // Parse configuration parameters
            const [key, value] = arg.substring(2).split('=');

            if (key === 'config') {
                // Load configuration from file
                try {
                    const fileConfig = JSON.parse(fs.readFileSync(value, 'utf8'));
                    if (fileConfig.sourceDb) {
                        config.sourceDb = { ...config.sourceDb, ...fileConfig.sourceDb };
                    }
                    if (fileConfig.targetDb) {
                        config.targetDb = { ...config.targetDb, ...fileConfig.targetDb };
                    }
                } catch (error) {
                    console.error(`Unable to load configuration file ${value}:`, error);
                }
            }
        } else {
            // Non-option parameters are treated as user IDs
            userIds.push(arg);
        }
    }

    config.userIds = userIds;

    if (userIds.length === 0) {
        console.log('No user IDs specified, default values will be used.');
        console.log('Usage: ts-node copy_user_role.ts [USER_ID1 USER_ID2 ...] [--option=value]');
        console.log('Available options:');
        console.log('  --config=filepath              Load configuration from JSON file');
        console.log('');
        console.log('Examples:');
        console.log(' npx ts-node copy_user_role.ts S3NE8A SPCST4 --source-db-password=mypass --target-db-host=************');
        console.log(' npx ts-node copy_user_role.ts S3NE8A --config=db-config.json');
    }

    return config;
}

if (require.main === module) {
    const config = parseCommandLineArgs();
    copyUserRole(config.userIds, config.sourceDb, config.targetDb).catch((error: Error) => {
        console.error('Unhandled error:', error);
        process.exit(1);
    });
}