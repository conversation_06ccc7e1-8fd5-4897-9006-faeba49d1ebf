import { PDFPage } from 'pdf-lib';
import { AnnotationPointN, DrawOperation } from '../types';
import { rotateOrigin, translatePoly } from '../draw-utils';
import { Canvas } from 'canvas';
import { getColor } from './draw-utils-canvas';

// const MARKER_BRUSH2_WIDTH = 32;
const WIDTH_PENCIL = 1;

export function drawMarkerCanvas(page: PDFPage, operation: DrawOperation, canvas: Canvas) {
  const { height } = page.getSize();
  const points = operation.points;
  const ctx = canvas.getContext('2d');

  // const style = operation.objectMeta.style ?? 0;

  const start: AnnotationPointN = [+points[0][0], +points[0][1]];
  const end: AnnotationPointN = [+points[1][0], +points[1][1]];

  const path = createMarkerPath(start, end);

  const color = getColor(operation, 0.4);

  path.forEach(([x, y], i) => {
    if (i === 0) {
      ctx.beginPath();
      ctx.lineWidth = WIDTH_PENCIL;
      ctx.strokeStyle = color;
      ctx.fillStyle = color;
      ctx.moveTo(x, height - y);
    } else {
      ctx.lineTo(x, height - y);
    }
  });
  ctx.lineTo(path[0][0], height - +path[0][1]);
  ctx.fill();
  ctx.stroke();
}

export function createMarkerPath(start: AnnotationPointN, end: AnnotationPointN): AnnotationPointN[] {
  const direction: AnnotationPointN = [end[0] - start[0], end[1] - start[1]];

  const a = Math.atan2(direction[1], direction[0]);

  const markerLength = Math.sqrt((end[0] - start[0]) ** 2 + (end[1] - start[1]) ** 2);

  const markerHeight = 14;
  const markerTrapez = 6;

  const h2 = markerHeight / 2;

  const polygon0 = [
    [0, h2],
    [markerLength, h2],
    [markerLength - markerTrapez, -h2],
    [-markerTrapez, -h2],
    [0, h2]
  ];

  return translatePoly(
    polygon0.map((p) => rotateOrigin(a, p)),
    start
  );
}
