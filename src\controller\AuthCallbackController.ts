import * as express from 'express';
import { Request, Response } from 'express';
import passport from 'passport';

import { Controller } from '../types';
import LoggerCenter from '../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));

const CONTROLLER_NAME = 'auth/callback';

export default class AuthCallbackController implements Controller {
  public name = CONTROLLER_NAME;
  public paths = [`/api/${CONTROLLER_NAME}`];
  public router = express.Router();

  constructor() {
    this.router.get('/', passport.authenticate('azure_ad_oauth2', {
        failureRedirect: '/login',
        session: false,
      }), this.processHttpRequest);
  }

  private async processHttpRequest(req: Request, res: Response) {
    logger.info(`AuthCallbackController: ${req}`);
  }
}


