import { isSqExceptionResult, Sq<PERSON><PERSON><PERSON>unction, SqResult, toList } from '../../../serviceQuery';
import ServiceCenter from '../../ServiceCenter';
import { DbFile } from './types';
import { processPdfDerivedFiles } from './PdfUtilities';

export const DBFileUpdateMissingPreviews: SqNodeFunction = async (): Promise<SqResult> => {
  const sc = ServiceCenter.getInstance();

  const r = await sc.runSystemSq({ serviceId: 'dbFile.selectMissingPreview' });
  if (isSqExceptionResult(r)) {
    return r;
  }
  const files = toList<DbFile>(r);
  const header = ['message', 'content', 'origin'];
  const table: string[][] = [];
  table.push(['Nr of files to update', files.length.toString(), '']);
  for (const dbFile of files) {
    const res = await processPdfDerivedFiles(dbFile);
    if (isSqExceptionResult(res)) {
      table.push([`Could no update derived: ${dbFile.filename}`, `SQ Exception: ${res.exception}`, dbFile.directory]);
    } else if (res === -1) {
      table.push([
        `Could no update derived: ${dbFile.filename}`,
        `Could not open: ${dbFile.fileTid}`,
        dbFile.directory
      ]);
    } else {
      table.push([`Update successfully for: ${dbFile.filename}`, `Nr of pages ${res}`, dbFile.directory]);
    }
  }
  return { name: 'DBFileUpdateRealLength', header, table };
};
