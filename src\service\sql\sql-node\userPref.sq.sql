--
-- SERVICE_ID = userPref.annotationExpirationDays
-- ROLES      = SYSTEM
--

select pref_value as annotation_expiration_days
from esp.t_user_pref
where pref_name = 'ANNOTATION_EXPIRATION_DAYS'
  and user_id = :userId;

--
-- SERVICE_ID = userPref.select
-- ROLES      = ESP_ADMIN,SYSTEM
--

set-if-empty:userId=%
;
set-if-empty:userName=%
;
set-if-empty:prefName=%
;
select
p.USER_ID,
coalesce(u.LAST_NAME,  '-na-') || ', ' || coalesce(u.FIRST_NAME, '-na-') as USER_NAME,
p.PREF_NAME,
p.PREF_VALUE
from ESP.T_USER_PREF p
left join ESP.V_SR_USER u on (p.USER_ID = u.USER_ID  )
where upper(p.USER_ID) like upper(:userId)
and upper(p.PREF_NAME) like upper(:prefName)
and (
upper(u.FIRST_NAME) like upper(:userName) OR
upper(u.LAST_NAME) like upper(:userName) OR
(u.FIRST_NAME is null and :userName = '%%'))
order by u.LAST_NAME, u.FIRST_NAME
;

--
-- SERVICE_ID = userPrefMp.get
--

select p.USER_ID,
coalesce(u.LAST_NAME, '-na-') || ', ' || coalesce(u.FIRST_NAME, '-na-') as USER_NAME,
p.PREF_NAME, p.PREF_VALUE
from ESP.T_USER_PREF p
left join ESP.V_SR_USER_ALL u on (p.USER_ID = u.USER_ID)
where p.USER_ID = :userId;
