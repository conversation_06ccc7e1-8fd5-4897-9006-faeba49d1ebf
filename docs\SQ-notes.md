# Notes for ServiceQueries

In the past the SQ files  (sq.sql) files have been split between esp-stratum und esp-be repositories.

Now all SQ files are maintained separately in the esp-be repo.

The following directory contain the SQ files:

| Java dir        | Node dir                 | Notes                         |
|-----------------|--------------------------|-------------------------------|
| WEB-INF/sql     | service/sql/sql-java     | Common SQs                    |
| WEB-INF/sql-esp | service/sql/sql-esp-java | ESP SQs                       |
|                 | service/sql/sql-node     | SQs specific for Node version |

The directory order in the table above reflect the load order. Later SQ definitions overwrite earlier loaded SQs.

## Loading

This implies some minor changes e.g. KEY to SERVICE_ID.

I will enable the loading of these files at REMOTE server start and by the re-init command in the app-admin:

For loading at start up please set:

`LOAD_SQ_SQL_FILES_AT_STARTUP=true`

Usually you will have locally the following setup:

`LOAD_SQ_SQL_FILES_AT_STARTUP=false` 


