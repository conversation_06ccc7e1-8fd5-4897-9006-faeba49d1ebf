import jwt from 'jsonwebtoken';

test('should correctly resolve the token from the Authorization header', async() => {
  const idToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6ImltaTBZMnowZFlLeEJ0dEFxS19UdDVoWUJUayJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RFCmXuFLCST9IR71D9bARz44fXCGv8AXVwn1Sa1052csDYyyccOvebUkmzIB-6z2_u6vJVkCqn6aL1kmLX3JNUah4ur9oN8ZGmOhdXvGLDmAPSH7z6zkgiliBZgYwE3zz68AxWUruDiKfng6boQX5HZ3FPwmN66JUa7sGqZnGgzXn89DwzIK9619_TzCv7g_SQiKmZpNkVVKLnP_L6FXRRgCwrp2PWJU7-wSGL4T5z1Izzikc1m_ugSsFlOSbltSp69DZtwVopOdDYHcJVkdq1FWepeI1vENP4xlx0BfSbbLBmyIk-g53LCFlQANqLjWyxrovTVIA3HYHy220kx7VQ'; // Replace with a valid ID token

  // Decode the ID token to extract user information
  const decodedToken = jwt.decode(idToken) as { [key: string]: any };
  console.info('Decoded Token:', decodedToken);

  // Extract user information from the decoded token
  const userId = decodedToken.oid;
  const userEmail = decodedToken.preferred_username;
  console.info('User ID:', userId);
  console.info('User Email:', userEmail);

  expect(userId).toBe('28f55c6a-d04f-4e9a-b227-ebe12846e209');
  expect(userEmail).toBe('<EMAIL>');
});