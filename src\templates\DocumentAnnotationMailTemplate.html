<!DOCTYPE html>
<html lang="en" xml:lang="en">
<head>
	<title>ESP - Enterprise Steering Platform</title>
	<style>
        body {
            font-family: Arial, sans-serif;
        }
        .title {
            font-size: 24px;
            color: gray;
            font-weight: bold;
        }
        .greeting {
            font-size: 14px;
            font-weight: bold;
        }
        .content {
            font-size: 14px;
        }
    </style>
</head>
<body>


	<div class="title">ESP - Enterprise Steering Platform</div>
    <br>
	<br>

	<div class="greeting">Dear $userName</div>


	<br>
	<br>
	<div class="content">
	
		#if( $mutlipleFiles )
		
		  The attached documents have been sent from iESP. Please don't reply to this email; this address is not monitored.
		  
		#else 
		
		The attached document has been sent from iESP. Please don't reply to this email; this address is not monitored.
		
		#end 
          <br> <br>
          
          
<!--           meeting type  -->
<!--           meeting -->

#if( $meetingTypes )

#foreach( $key in $meetingTypes.keySet() )
<b>$esc.html($key)</b><br>
	#foreach( $meetingKey in $meetingTypes.get($key).keySet() )
  <u>$esc.html($meetingKey)</u><br>
		#foreach( $document in $meetingTypes.get($key).get($meetingKey) )
            $esc.html($document.getFilename())<br>
	    #end
    <br>
	#end
	<br>
#end
    <br>
#end



		#if( $comment ) <cite> $comment </cite> <br> <br> #end  
	</div>
</body>
</html>