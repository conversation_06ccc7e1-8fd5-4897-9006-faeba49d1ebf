import { test } from 'mocha';
import { expect } from 'chai';
import { createSecureSalt, getUserSalt } from '../../src/service/lib/mobile/mobile-utils';
import { isSqExceptionResult } from '../../src/serviceQuery';

const userSaltJava = {
  version: '1000',
  userId: 'SRZXLM',
  salt: 'gm,<1P^vwNu\\(/b}%L#l"w%"QGP_/g69{f#aP"3(DA\\+4>4]l6?d<XvZ>k< B4J/'
};

test(`User salt test`, async () => {
  const sizes = [23, 21, 32, 32, 32, 32, 534, 23, 12];
  sizes.forEach((size) => {
    let salt = createSecureSalt(size);
    expect(salt.length).equals(size * 2);
  });
});

test(`User salt test for SRZXLM`, async () => {
  const userSalt = await getUserSalt(userSaltJava.userId);
  if (isSqExceptionResult(userSalt)) {
    expect.fail(`Could not get salt for ${userSaltJava.userId}`);
    return;
  }
  expect(userSalt.salt).equals(userSaltJava.salt);
});
