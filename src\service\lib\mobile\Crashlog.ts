/* eslint-disable space-before-function-paren */
import { isSqExceptionResult, ProcessLine, SqNodeFunction, SqResult } from '../../../serviceQuery';
import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';
import ServiceCenter from '../../ServiceCenter';
import { getDocumentAsString, moveFile, rename } from '../DBFileService';
import { IDType } from '../esp/types';
import moment from 'moment';
import { ANONYMOUS, SYSTEM } from '../../constants';
import { saveDbFile } from '../upload-file-utils';
import { getFiles } from '../esp/DBFileHelper';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export const Crashlog: SqNodeFunction = async function ({ request }): Promise<SqResult> {
  const sq = ServiceCenter.getInstance().getSq();

  const { userId = ANONYMOUS, parameters } = request;
  const lines: ProcessLine[] = [];
  const files: string[] = getFiles(request);

  const crashlogDir = (parameters['crashlogDir'] || '').toString();
  let crashlog = (parameters['crashlog'] || '').toString();
  const version = parameters['version'];

  let fileTid: IDType = files[0];

  const directory = crashlogDir + '/' + userId;

  try {
    if (fileTid) {
      crashlog = await getDocumentAsString(fileTid);
    } else if (!crashlog) {
      logger.error('No clashlog provided! user=' + userId);
    } else {
      const filename = 'noname';
      const directory = 'tmp';

      const file0 = await saveDbFile({ filename, directory, owner: userId, data: crashlog });
      if (isSqExceptionResult(file0)) {
        fileTid = '';
      } else {
        fileTid = file0.fileTid;
      }
    }
    let errMessage = '';
    if (fileTid) {
      await moveFile(fileTid, directory);
      await rename(fileTid, 'Crashlog-' + userId + '.txt');
      errMessage =
        'fileTid:' + fileTid + ';Crashlog::by:' + userId + '::' + moment().format('YYYY-MM-DD hh:mm') + '::' + crashlog;
    }

    errMessage = errMessage.substring(0, 2000);
    sq.run({
      serviceId: 'm.crashlog.insert',
      userId: SYSTEM,
      roles: [SYSTEM],
      parameters: { version, userId, errMessage }
    });
  } catch (e) {
    logger.error(e);
  }
  return { name: 'Crashlog', processInfo: { lines } };
};
