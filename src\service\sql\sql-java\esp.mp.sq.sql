--
-- KEY    = MeetingDocumentUpload
-- 

java:com.swissre.esp.service.queryServices.MeetingDocumentUpload


-- 
-- KEY          = esp.mp.processUpload
-- ACCESS_ROLES = SYSTEM
--

set-if-empty:ord=0
;
call ESP.SP_MP_PROCESS_UPLOAD(:fileTid, :filename, :ord, :meetingItemTid)
;


--
-- KEY          = esp.mp.documents.missingRel
-- ACCESS_ROLES = SYSTEM
--

select f.FILE_TID, f.FILENAME, LENGTH(f.DOCUMENT) as DOCUMENT_LENGTH, mf.MEETING_ITEM_TID , fr1.TARGET_VALUE, fr_preview.TARGET_TID
from ESP.T_FILE f 
left join ESP.T_MP_FILE mf on mf.FILE_TID = f.FILE_TID 
left join ESP.T_FILE_REL fr1 on fr1.FILE_TID = f.FILE_TID and fr1.REL_TID = -100
left join ESP.T_FILE_REL fr_preview on fr_preview.FILE_TID = f.FILE_TID and fr_preview.REL_TID = -103
where
mf.FILE_TID is not null
and
fr1.TARGET_VALUE is null
and
(mf.MEETING_ITEM_TID = :meetingItemTid or :meetingItemTid = '%')
order by DOCUMENT_LENGTH
    ;


--
-- KEY          = MeetingDocumentRelationFix
-- ACCESS_ROLES = ESP_ADMIN
-- 

java:com.swissre.esp.service.queryServices.MeetingDocumentRelationFix



--
-- KEY          = AnnotationDeleteSql
-- ACCESS_ROLES = SYSTEM
-- 

update ESP.T_FILE 
set 
	directory = '/daily_temp/'
where 
	directory = '/.derived/' || :documentFileTid || '/' || :$USERID || '/'
;
delete from ESP.T_FILE_REL
where
	FILE_TID = :documentFileTid
and 
	REL_TID in (-101,-102) and TARGET_VALUE = :$USERID
;


--
-- KEY          = AnnotationDelete
-- 

java:com.swissre.esp.service.queryServices.AnnotationDelete
	



