import { createServiceQueryFromEnv } from '../utils';
import { ServiceQuery, ServiceQueryUtils, singleValue, SqFunctionArg, SqSimple } from '../serviceQuery';

import { registerNodeAndService } from './register-node-and-service';
import LoggerCenter from '../logger/LoggerCenter';
import { startAllSchedulers } from './scheduler-utils';
import { nowIso } from './service-utils';
import { PSQLDriverExtension } from '../serviceQuery/PSQLDriver';
import { SYSTEM } from './constants';

interface InitArg {
  sqlDirectories?: string[];
}

const logger = LoggerCenter.getLogger('ServiceCenter');

export default class ServiceCenter {
  private static inst: ServiceCenter;

  static getInstance() {
    return !ServiceCenter.inst ? (ServiceCenter.inst = new ServiceCenter()) : ServiceCenter.inst;
  }

  private readonly sq: ServiceQuery;
  private sqlDirectories: string[] = [];

  private readonly startupTime = new Date();

  constructor() {
    this.sq = createServiceQueryFromEnv();
  }

  public clearServiceCache() {
    this.sq.driver.clearCache();
  }

  async init(initArg?: InitArg) {
    if (process.env.LOG_STARTTIME_IN_APP_PROPERTIES === 'true') {
      const name = this.isLocal() ? 'startup-at-local' : 'startup-at-remote';
      const value = `${nowIso()}`;
      const r0 = await this.sq.driver.processSql('delete from esp.t_app_properties where name =:name', { name });
      logger.debug(`delete ${name}`, r0.rowsAffected);
      const r = await this.sq.driver.processSql(
        'INSERT INTO ESP.T_APP_PROPERTIES (NAME, VALUE) VALUES (:name, :value) ON CONFLICT (NAME) DO UPDATE SET VALUE = EXCLUDED.VALUE',
        { name, value }
      );
      if (r.rowsAffected !== 1) {
        logger.error('DB Connection not ok ' + r.exception);
      }
    }
    this.sqlDirectories = initArg?.sqlDirectories || [];
    // Load SQL and ServiceQueries files
    try {
      
      if (process.env.LOAD_SQ_SQL_FILES_AT_STARTUP === 'true') {
        logger.warn('LoadSqSqlFiles started...');
        const initRepositoryResult = await this.loadSqSqlFiles();
        // check duplicate services
        const set = new Set<string>();
        initRepositoryResult.directories.forEach((dir) =>
          dir.serviceIds.forEach((serviceId) => {
            logger.info(`Service loaded: ${serviceId} (${dir}).`);
            if (set.has(serviceId)) {
              logger.warn(`Duplicate service entry in directory ${dir.name}: ${serviceId}`);
            }
            set.add(serviceId);
          })
        );
        logger.warn('LoadSqSqlFiles ended!');
      } else {
        logger.warn('LoadSqSqlFiles skipped!');
      }
    } catch (e: any) {
      logger.error('Error in loadSq', e.message);
    } finally {
      logger.warn('LoadSqSqlFiles finally!');
    }

    //
    // Register commands
    //

    this.sq.registerCommand('create-tid', createTid);
    this.sq.registerCommand('create-tid-if-empty', createTid);
    //
    // register migrated Java Services
    //
    registerNodeAndService(this.sq);
    //
    // Cleanup Scheduler Locks
    //
    if (this.isLocal()) {
      logger.warn('Scheduler is skipped. System runs locally!');
    } else {
      startAllSchedulers();
      await this.runSystemSq({ serviceId: 'sync.deleteAll' });
    }
  }

  async loadSqSqlFiles() {
    logger.warn('== loadSqSqlFiles ==')
    const sqUtils = new ServiceQueryUtils(this.sq, 'SaveServicequery');
    return await sqUtils.initRepository(this.sqlDirectories);
  }

  getSq() {
    return this.sq;
  }

  runSystemSq({ serviceId, parameters = {} }: { serviceId: string; parameters?: Record<string, SqSimple> }) {
    return this.sq.run({ serviceId, parameters, userId: SYSTEM, roles: [SYSTEM] });
  }

  isLocal(): boolean {
    logger.info('IS_LOCAL', process.env.IS_LOCAL);
    return process.env.IS_LOCAL === 'true' || false;
  }

  getStartupTime(): Date {
    return this.startupTime;
  }

  async destroy() {
    const driver = this.getSq().driver as PSQLDriverExtension;
    driver.end();
  }
}

//
// Commands Registration
//
async function createTid({ request, currentResult, statementNode }: SqFunctionArg) {
  const overwrite = statementNode.cmd === 'create-tid';
  request.parameters = request.parameters || {};
  const name = statementNode.parameter;
  if (overwrite || !request.parameters[name]) {
    request.parameters[statementNode.parameter] = await newTid();
  }
  return currentResult;
}

export async function newTid(): Promise<string> {
  const sq = ServiceCenter.getInstance().getSq();
  const res0 = await sq.run({
    serviceId: 'newTid.create',
    userId: SYSTEM,
    roles: [SYSTEM],
    parameters: {}
  });
  return singleValue(res0);
}
