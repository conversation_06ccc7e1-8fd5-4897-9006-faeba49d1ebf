import { endTest, getTestSc, getTestSq } from '../init-local';
import { deleteSync, insertSync, selectSync } from '../../src/service/lib/SyncService';
import { expect } from 'chai';
import LoggerCenter from '../../src/logger/LoggerCenter';
import path from 'path';
import { PSQLDriverExtension } from '../../src/serviceQuery/PSQLDriver';
import { PoolClient } from 'pg';

const logger = LoggerCenter.getLogger(path.basename(__filename));

const DESCR = 'transaction 01';

describe(DESCR, async () => {
  logger.info(`Start ${DESCR}`);
  let lockName = `-}`;
  let details = `Inserted by ${DESCR}`;
  before(() => {
    getTestSc();
    lockName = `test-tx-${Date.now()}`;
  });

  after(async () => {
    const pgDriver = getTestSq().driver as PSQLDriverExtension;
    await pgDriver.processSql(deleteSync, { lockName });
    endTest();
  });

  it(`rollback test`, async () => {
    const pgDriver = getTestSq().driver as PSQLDriverExtension;
    let con: PoolClient = await pgDriver.getConnection();
    try {
      await con.query('BEGIN');
      await pgDriver.processSql_con(con, insertSync, { lockName, details });
      await con.query('ROLLBACK');
    } catch (e) {
      expect.fail(`Transaction test failed ${e.message}`);
    } finally {
      if (con) {
        pgDriver.returnConnection(con);
      }
    }
    const r = await pgDriver.processSql(selectSync, { lockName });
    expect(r.table?.length === 0).true;
  });

  it(`commit test`, async () => {
    const pgDriver = getTestSq().driver as PSQLDriverExtension;
    let con: PoolClient = await pgDriver.getConnection();
    try {
      await con.query('BEGIN');
      await pgDriver.processSql_con(con, insertSync, { lockName });
      await con.query('COMMIT');
    } catch (e) {
      expect.fail(`Transaction commit test failed ${e.message}`);
    } finally {
      if (con) {
        pgDriver.returnConnection(con);
      }
    }
    const r = await pgDriver.processSql(selectSync, { lockName });
    expect(r.table?.length === 1).true;

    const resDel = await pgDriver.processSql(deleteSync, { lockName });
    expect(resDel.rowsAffected === 1).true;
  });

  it(`auto commit test`, async () => {
    const pgDriver = getTestSq().driver as PSQLDriverExtension;
    let con: PoolClient = await pgDriver.getConnection();
    try {
      await pgDriver.processSql_con(con, insertSync, { lockName });
    } catch (e) {
      expect.fail(`Transaction auto commit test failed ${e.message}`);
    } finally {
      if (con) {
        pgDriver.returnConnection(con);
      }
    }
    const r = await pgDriver.processSql(selectSync, { lockName });
    expect(r.table?.length === 1).true;

    const resDel = await pgDriver.processSql(deleteSync, { lockName });
    expect(resDel.rowsAffected === 1).true;
  });
});
