/* eslint-disable no-console */
// called from admin
// serviceId: 'm.Getkeys2',
//     parameters: {
//   name: 'sources',
//       // sources only!
//       sourcesOnly: true,
//       uriVersion: '2'
// },
// ==> java:com.swissre.serviceMobile.lib.Getkeys2

import { test } from 'mocha';
import { localUrlJava, localUrlNode } from '../common';
import { compareResult } from '../test-utils';
import { runGetkeys2 } from './getkeys2-utils';

const sqPart = 'mobile/getkeys2';
const localUrlNodeStart2 = `${localUrlNode}${sqPart}`;
const localUrlJavaStart2 = `${localUrlJava}${sqPart}`;
const getSources = 'm.Getkeys2 sources';
test(`(webtests) ${getSources}`, async () => {
  const name = 'sources';

  console.log(`Start Java ${getSources} url: ${localUrlJavaStart2}!`);
  console.log(`Start Node ${getSources} url: ${localUrlNodeStart2}!`);

  const resultJava = await runGetkeys2(`${localUrlJavaStart2}?sourcesOnly=true&name=${name}`, 'Java');
  console.log(`java: header        : ${JSON.stringify(resultJava.header)}`);
  console.log(`java: nr of records : ${resultJava.table?.length}`);
  console.log(`java: nr of keys    : ${Object.keys(resultJava).length}`);
  const resultNode = await runGetkeys2(`${localUrlNodeStart2}?sourcesOnly=true&name=${name}`, 'Node');
  console.log(`node: header        : ${JSON.stringify(resultNode.header)}`);
  console.log(`node: nr of records : ${resultNode.table?.length}`);
  console.log(`node: nr of keys    : ${Object.keys(resultNode).length}`);
  compareResult(resultNode, resultJava, {
    ignoreHeader: ['updateTime'],
    ignoredFields: []
  });
});
