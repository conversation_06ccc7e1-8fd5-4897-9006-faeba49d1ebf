import { PDFPage } from 'pdf-lib';
import { AnnotationPoint, AnnotationPointN, DrawOperation } from '../types';
import { rotateOrigin, translatePoly } from '../draw-utils';
import { Canvas } from 'canvas';
import { getColor, PINK } from './draw-utils-canvas';

enum ArrowStyle {
  forward,
  both,
  none
}

export function drawArrowCanvas(page: PDFPage, operation: DrawOperation, canvas: Canvas) {
  const { height } = page.getSize();
  const style: ArrowStyle = operation.objectMeta.style ?? 0;

  const points = operation.points;
  const ctx = canvas.getContext('2d');

  const width = +operation.objectMeta.width || 0.2;
  const path = createArrowPath(points, width, style);

  // let color = getColor(operation);
  let color = operation.objectMeta.color || PINK;

  color = getColor(operation, 0.4);
  path.forEach(([x, y], i) => {
    if (i === 0) {
      ctx.beginPath();
      ctx.lineWidth = 1;
      ctx.strokeStyle = color;
      ctx.fillStyle = color;
      ctx.moveTo(x, height - y);
    } else {
      ctx.lineTo(x, height - y);
    }
  });
  ctx.lineTo(path[0][0], height - +path[0][1]);
  ctx.fill();
  ctx.stroke();
}

export function createArrowPath(points: AnnotationPoint[], width: number, style: ArrowStyle): AnnotationPointN[] {
  const start: AnnotationPointN = [+points[0][0], +points[0][1]];
  const end: AnnotationPointN = [+points[1][0], +points[1][1]];
  const direction: AnnotationPointN = [end[0] - start[0], end[1] - start[1]];
  const a = Math.atan2(direction[1], direction[0]) - Math.PI / 2;

  const arrowLength = Math.sqrt((end[0] - start[0]) ** 2 + (end[1] - start[1]) ** 2);

  const arrowWidth = 30 * width;
  const triangleBaseWidth = 100 * width;
  const triangleBaseHeight = 100 * width;

  const hw = arrowWidth / 2;
  const tb = triangleBaseWidth / 2;
  const th = triangleBaseHeight;

  let polygon0;

  switch (style) {
    case ArrowStyle.forward: {
      polygon0 = [
        [-hw, 0],
        [-hw, arrowLength - th],
        // triangle
        [-tb, arrowLength - th],
        [0, arrowLength],
        [tb, arrowLength - th],
        [hw, arrowLength - th],
        //
        [hw, 0],
        [-hw, 0]
      ];
      break;
    }
    case ArrowStyle.both: {
      polygon0 = [
        [0, 0],
        [-tb, th],
        [-hw, th],
        [-hw, arrowLength - th],
        // triangle end
        [-tb, arrowLength - th],
        [0, arrowLength],
        [tb, arrowLength - th],
        [hw, arrowLength - th],
        //
        [hw, th],
        [tb, th],
        [0, 0]
      ];
      break;
    }
    default:
      polygon0 = [
        [-hw, 0],
        [-hw, arrowLength],
        [hw, arrowLength],
        [hw, 0],
        [-hw, 0]
      ];
  }

  return translatePoly(
    polygon0.map((p) => rotateOrigin(a, p)),
    start
  );
}
