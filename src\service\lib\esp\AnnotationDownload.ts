import { <PERSON>Line, Sq<PERSON><PERSON>Function, SqRequest, SqResult } from '../../../serviceQuery';

import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';
import ServiceCenter from '../../ServiceCenter';
import { getUserPref } from './UserService';
import { UserPreferences } from './UserPreferences';
import { toBoolean } from './utils';
import { KeyProcessor2 } from '../mobile/KeyProcessor2';
import { SYSTEM } from '../../constants';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export const AnnotationDownload: SqNodeFunction = async({ request }): Promise<SqResult> => {
  const sq = ServiceCenter.getInstance().getSq();

  const { userId = 'ANONYMOUS', parameters } = request;
  logger.info(`AnnotationDownload START : user: ${userId}`);
  const lines: ProcessLine[] = [];

  const userPrefMap = await getUserPref(userId);
  const noano = userPrefMap[UserPreferences.ANNOTATION_DOWNLOAD_OFF];
  if (toBoolean(noano)) {
    return { processInfo: { lines } };
  }

  //
  // request parameters
  //
  const documentFileTidList = (parameters['documentFileTidList'] || '').toString().split(',');
  if (documentFileTidList.length === 0) {
    logger.error(`documentFileTidList parameter list empty! (${userId})`);
    return { processInfo: { lines } };
  }
  const fileTids = new Set<string>();
  if ('ALL' === documentFileTidList[0]) {
    // process all, need a clever query ...
    const boardPackReports = await KeyProcessor2.getBPReports(userId);
    boardPackReports.forEach((f) => fileTids.add(f.fileTid.toString()));

    //
    // iesp report files
    //
    const espReports = await KeyProcessor2.getESPReports(userId);
    espReports.forEach((f) => fileTids.add(f.fileTid.toString()));
  } else {
    fileTids.add('-1');
    documentFileTidList.forEach((s) => {
      if (+s) {
        fileTids.add(s);
      }
    });
  }

  const request1: SqRequest = {
    serviceId: 'esp.annotationDownload.select',
    userId: SYSTEM,
    roles: [SYSTEM],
    parameters: { userId, fileTids: Array.from(fileTids).join(',') }
  };

  return sq.run(request1);
};
