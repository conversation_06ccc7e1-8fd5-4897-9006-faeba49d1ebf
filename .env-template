IS_LOCAL=true

# When in local mode, the LOCAL_USER_ID is taken for running all processes.
LOCAL_USER_ID=SRZ...

PORT=8080

# DB on AZURE

DB_TRX_USER=esp_trx
DB_TRX_PASSWORD=...
DB_HOST=dsp0000575.postgres.database.azure.com
DB_PORT=5432
DB_DATABASE=ddp0006621

NODE_TLS_REJECT_UNAUTHORIZED=0

LOG_STARTTIME_IN_APP_PROPERTIES=true


# All sql and sq files are in the sql service directory are read into the service table.
LOAD_SQ_SQL_FILES_AT_STARTUP=false

OKTA_ISSUER=https://identity-np.swissre.com/oauth2/aus231pr469i7v1PD0h8

OKTA_AUDIENCE=xxxx

AUTH_AUDIENCE=XXXX

# Setting white list for cors
# CORS_WHITE_LIST=http://localhost:3000,http://localhost:3001,http://localhost:8080,https://dev.esp.swissre.com

# This allows CORS for all host
# CORS_WHITE_LIST=*

# Set some logger names to INFO level. Most often the name of the logger is the filename.
LOG_LEVEL_INFO=serviceQuery.ts,PSQLDriver.ts,scheduler-utils.ts,server.ts

# Same for debug would be like:
#LOG_LEVEL_DEBUG=serviceQuery.ts,PSQLDriver.ts
TEST_LOCAL_URL_NODE=http://localhost:8081/api/
TEST_LOCAL_URL_JAVA=http://localhost:8080/
TEST_LOCAL_SQ_URL=http://localhost:8080/serviceQuery/

# Directory with the MP sources for iPad. Make sure that it ends with / or \
TEST_LOCAL_MP_SRC=C:\srdev\gitdata\esp\sr-esp-web\src\app-mp\build\

# SENDGRID API KEY
SENDGRID_API_KEY=SENDGRID_API_KEY
FROM_EMAIL_ADDRESS=<EMAIL>

# VERIFY_SIGNATURE=false does skip verification of signature in JWT, otherwise the check is done
# VERIFY_SIGNATURE=false

# TEAM_DOMAIN is the url for cloudflare, default is: https://swissre.cloudflareaccess.com
# e.g. TEAM_DOMAIN=
