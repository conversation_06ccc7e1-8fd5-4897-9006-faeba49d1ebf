trigger:
- none

# variables:
#   - template: ./dev-variables.yml
 
pool: SR-Ubuntu-Default #Default-Linux

stages:
  - stage: Build
    jobs:
    - job:
      steps:

      - task: NodeTool@0
        inputs:
          versionSpec: '20.x'
          displayName: 'Install Node.js'
      - task: Npm@1
        displayName: Install dependencies
        inputs:
          command: install
      - task: Npm@1
        displayName: Build
        inputs:
          command: 'custom'
          customCommand: 'run build'
        env:
          NODE_OPTIONS: --max_old_space_size=8192
  
  - stage: Scan
    jobs:
    - job:
      steps:
      # - task: Npm@1
      #   displayName: Install dependencies
      #   inputs:
      #     command: 'custom'
      #     customCommand: 'install typescript'
          # workingDir: 'workspace/1/s'
          # workingDir: 'RMOSS/TEST_CASES/js-project'
      # - task: Npm@1
      #   displayName: Build
      #   inputs:
      #     command: 'custom'
      #     customCommand: 'run build'
      #   env:
      #     NODE_OPTIONS: --max_old_space_size=8192

      - task: NexusIqPipelineTask@1
        displayName: Run scan nexus IQ
        inputs:
          nexusIqService: 'Nexus Iq'
          applicationId: 'ESP-BE'
          stage: 'Build'
          scanTargets: '**/package.json, **/package-lock.json, !**/node_modules/**'

