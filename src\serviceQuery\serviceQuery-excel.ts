import { read, readFile, utils } from 'xlsx';
import moment from 'moment';
import { SqRequest } from './serviceQuery-common';
import LoggerCenter from '../logger/LoggerCenter';
import path from 'path';

const logger = LoggerCenter.getLogger(path.basename(__filename));

interface MPoint {
  x: number;
  y: number;
}

interface VPoint extends MPoint {
  value: any;
  isEmpty: () => boolean;
}

// type MSheet = { name: string; length: number };

interface OO {
  requestTables: RequestTable[];
}

interface RequestTable {
  topCellMap?: any;
  name: string;
  list: any[];
  type?: string;
}

export function spreadsheet2Requests(arg0: string | any, option?: any): Record<string, SqRequest[]> {
  const workbook = typeof arg0 === 'string' ? readFile(arg0, option) : read(arg0, option);
  const result: Record<string, SqRequest[]> = {};
  for (const sheetName of workbook.SheetNames) {
    const sheet = workbook.Sheets[sheetName];
    if (!sheet['!ref']) {
      continue;
    }
    const range = 'A1:' + sheet['!ref'].split(':')[1];
    const xlData = utils.sheet_to_json<any>(fixDateFields(workbook.Sheets[sheetName]), {
      header: 1,
      raw: false,
      range: range
    });
    result[sheetName] = spreadsheetProcessor(xlData);
  }
  return result;
}

function spreadsheetProcessor(sheet: any) {
  let pointer: VPoint;
  const startRow = 0;
  let topCellMap: any = {};
  let yNameCells: any[] = [];
  let xNameCells: any[] = [];

  const o: OO = { requestTables: [] };

  return doProcess();

  function doProcess() {
    o.requestTables = [];
    let lastPointer;

    try {
      pointer = getCell(0, startRow);
      //
      // Main Loop
      //
      const ok = true;
      while (ok) {
        lastPointer = pointer;
        //
        const y = findNonEmptyRow(pointer.y);
        if (y === -1) {
          break;
        }
        pointer = getCell(0, y);

        const m1 = getCell(0, y);
        const m2 = getCell(1, y);
        if (m1 && m1.value && '#' === m1.value[0]) {
          pointer = getCell(0, pointer.y + 1);
          continue;
        }
        if (!m1.isEmpty() && !m2.isEmpty()) {
          processTopCellMap();
        } else if (m1.isEmpty() && !m2.isEmpty()) {
          processTable();
        } else {
          pointer = getCell(0, pointer.y + 1);
        }
      }
    } catch (e) {
      logger.warn(
        'Unexpected error while table processing tab ' +
          sheet.name +
          '! Stop processing tab ' +
          sheet.name +
          '.' +
          (lastPointer != null ? ' Last pointer at (x,y):  (0,' + lastPointer.x + ',' + lastPointer.y + ').' : '')
      );
    } finally {
      topCellMap = null;
    }
    return toRequests();
  }

  function processTopCellMap() {
    topCellMap = {};
    while (pointer.y < sheet.length) {
      const n = getCell(0, pointer.y);
      const v = getCell(1, pointer.y);

      if (!n.isEmpty() && !v.isEmpty()) {
        topCellMap[n.value] = v.value;
        pointer = getCell(0, pointer.y + 1);
      } else {
        return;
      }
    }
  }

  function processTable() {
    const requestTable = newRequestTable();
    xNameCells = [];
    yNameCells = [];

    requestTable.topCellMap = topCellMap;

    const endOfTableY = findEmptyRow(pointer.y);
    const endOfTableX = findEmptyColumn2(1, pointer.y, endOfTableY);
    let yStart = getCell(1, pointer.y);
    while (!yStart.isEmpty()) {
      yNameCells.push(yStart);
      yStart = getCell(yStart.x + 1, yStart.y);
    }
    let xStart = getCell(0, pointer.y + 1);
    // Refactor to avoid 'while (true)'
    while (!xStart.isEmpty()) {
      xNameCells.push(xStart);
      xStart = getCell(xStart.x, xStart.y + 1); // Assuming this is the intended way to move to the next cell
    }
    //
    const dataStart = getCell(yStart.x, xStart.y);
    const dataEnd = getCell(endOfTableX, endOfTableY);
    //
    //

    for (let x = dataStart.x; x < dataEnd.x; x++) {
      for (let y = dataStart.y; y < dataEnd.y; y++) {
        const vCell = getCell(x, y);
        if (!vCell.isEmpty()) {
          const request = createRequest(vCell);
          requestTable.list.push(request);
        }
      }
    }
    topCellMap = {};
    pointer = getCell(0, dataEnd.y + 1);
  }

  function newRequestTable(): RequestTable {
    const requestTable: RequestTable = { name: 'table-' + o.requestTables.length, list: [] };
    o.requestTables.push(requestTable);
    return requestTable;
  }

  function createRequest(vCell: any) {
    const kCellMap: Record<string, any> = {};
    for (const xNameCell of xNameCells) {
      const kCell = getCell(vCell.x, xNameCell.y);
      if (!kCell.isEmpty()) {
        kCellMap[xNameCell.value] = kCell.value;
      }
    }
    for (const yNameCell of yNameCells) {
      const kCell = getCell(yNameCell.x, vCell.y);
      if (!kCell.isEmpty()) {
        kCellMap[yNameCell.value] = kCell.value;
      }
    }
    return { kCellMap: kCellMap, vCell: vCell }; // RequestData(kCellMap, vCell);
  }

  function getCell(x: number, y: number): VPoint {
    const row: any = sheet[y];
    return _cell(row ? row[x] : null, x, y);

    function _cell(value: any, x: number, y: number) {
      value = typeof value === 'string' ? value.trim() : value;
      return {
        x,
        y,
        value,
        isEmpty: function() {
          return value === undefined || value == null || value === '';
        }
      };
    }
  }

  function findNonEmptyRow(y: number) {
    for (let i = y; sheet[i]; i++) {
      if (sheet[i] && sheet[i].length > 0) {
        return i;
      }
    }
    return -1;
  }

  function findEmptyRow(y: number) {
    for (let iy = y + 1; iy < sheet.length; iy++) {
      if (sheet[iy].length === 0) {
        return iy;
      }
    }
    return sheet.length;
  }

  // function findEmptyColumn(xStart: number, yStart: number, yEnd: number) {
  //   for (let yi: number = yStart; yi < yEnd; yi) {
  //     xStart = Math.max(xStart, ((sheet[yi] as any) || []).length);
  //   }
  //   return xStart;
  // }

  function findEmptyColumn2(xStart: number, yStart: number, yEnd: number) {
    let xi = xStart;
    const maxColumns = 10000; // Adjust this value based on the expected size of your sheet
    while (xi < maxColumns) {
      let empty = true;
      for (let yi = yStart; yi < yEnd; yi++) {
        const row = sheet[yi];
        if (row) {
          const c = getCell(xi, yi);
          if (!c.isEmpty()) {
            empty = false;
            break;
          }
        }
      }
      if (empty) {
        break; // Found an empty column, exit the loop
      }
      xi++; // Move to the next column
    }
    return xi;
  }

  function toRequests(): SqRequest[] {
    const requests: SqRequest[] = [];
    for (const requestTable of o.requestTables) {
      if (requestTable.type === 'simple') {
        continue;
      }
      for (const e of requestTable.list) {
        const parameters = {
          ...requestTable.topCellMap,
          ...e.kCellMap,
          VALUE: e.vCell.value,
          X: e.vCell.x,
          Y: e.vCell.y
        };
        requests.push({
          serviceId: parameters['SERVICEID'] || parameters['SQCOMMANDS'] || parameters['VALUE'],
          parameters: parameters
        });
      }
    }
    return requests;
  }
}

export function refToPoint(ref: any): MPoint {
  let idx,
    cc,
    i = 0;
  const point: MPoint = { x: -1, y: -1 };

  for (idx = 0; i < ref.length; ++i) {
    if ((cc = ref.charCodeAt(i) - 64) < 1 || cc > 26) break;
    idx = 26 * idx + cc;
  }
  point.x = --idx;

  for (idx = 0; i < ref.length; ++i) {
    if ((cc = ref.charCodeAt(i) - 48) < 0 || cc > 9) break;
    idx = 10 * idx + cc;
  }
  point.y = --idx;
  return point;
}

export function fixDateFields(sheet: any) {
  const keys = Object.keys(sheet);
  for (const key of keys) {
    const value = sheet[key];
    if (typeof value === 'object') {
      if (value.t === 'd') {
        value.w = moment(value.v).format('YYYY-MM-DD');
      }
    }
  }
  return sheet;
}
