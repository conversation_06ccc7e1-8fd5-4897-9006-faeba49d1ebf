--
--  KEY           = SQ.insert
--  ACCESS_GROUPS = ESP_ADMIN_MGMT
--                 

insert into ESP.T_SERVICEQUERIES
(KEY, QUERY, TAGS, ACCESS_GROUPS, CACHE_MINUTES)
VALUES
    (:key, :query, :tags, :accessGroups, :cacheMinutes)
;

--
--  KEY           = SQ.insertProtected
--  ACCESS_GROUPS = ESP_ADMIN_MGMT
--

insert into ESP.T_SERVICEQUERIES
(KEY, QUERY, TAGS,
 ACCESS_GROUPS, CACHE_MINUTES)
VALUES (:key,
        CONVERT_FROM(DECODE(:query, 'BASE64'), 'UTF-8'),
        :tags,
        :accessGroups, :cacheMinutes);

--
--  KEY           = SQ.select
--  ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--  

set-if-empty:key=%
;
set-if-empty:query=%
;
set-if-empty:accessGroups=%
;
set-if-empty:tags=%
;
set-if-empty:cacheMinutes=-10000
;
select
*
from ESP.T_SERVICEQUERIES
where
(upper(KEY) like upper(:key) or KEY is null)
and
(upper(QUERY) like upper(:query) or QUERY is null)
and
(
	(upper(ACCESS_GROUPS) like upper(:accessGroups) and ACCESS_GROUPS is not null)
	or
	((:accessGroups = '%' or :accessGroups = '%%') and ACCESS_GROUPS is null)
)
and
(upper(TAGS) like upper(:tags) or (:tags = '%' and TAGS is null))
and
(CACHE_MINUTES >= :cacheMinutes or (CACHE_MINUTES is null and :cacheMinutes = -10000))
order by KEY
;

--
--  KEY           = SQ.selectProtected
--  ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

select key,
       ENCODE(CONVERT_TO(query, 'UTF-8'), 'base64') as query,
       ACCESS_GROUPS,
       tags,
       cache_minutes
from ESP.T_SERVICEQUERIES
order by KEY
;

--
--  KEY           = SQ.get
--  ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

select * from ESP.T_SERVICEQUERIES where key = :key
;

--
--  KEY           = SQ.getProtected
--  ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

select key, ENCODE(CONVERT_TO(query, 'UTF-8'), 'base64') as query, access_groups, tags, cache_minutes
from ESP.T_SERVICEQUERIES
where key = :key
;

--
--  KEY           = SQ.update
--  ACCESS_GROUPS = ESP_ADMIN_MGMT
--


set:sqOverwriteDirectory=...
;
set-if-empty:cacheMinutes=-1
;
update ESP.T_SERVICEQUERIES
set
QUERY = :query,
TAGS = :tags,
ACCESS_GROUPS = :accessGroups,
CACHE_MINUTES = :cacheMinutes
where
KEY = :key
;

--
--  KEY           = SQ.updateProtected
--  ACCESS_GROUPS = ESP_ADMIN_MGMT
--

update ESP.T_SERVICEQUERIES
set
    QUERY = CONVERT_FROM(DECODE(:query, 'BASE64'), 'UTF-8'),
    TAGS = :tags,
    ACCESS_GROUPS = :accessGroups,
    CACHE_MINUTES = :cacheMinutes
where
    KEY = :key
;


--
--  KEY           = SQ.delete
--  ACCESS_GROUPS = ESP_ADMIN,ESP_ADMIN_MGMT
--

delete from ESP.T_SERVICEQUERIES
where KEY = :key  
