{"name": "esp-azure", "license": "UNLICENSED", "version": "1.0.0", "description": "Backend module for ESP written in node and providing serviceQuery", "main": "dist/src/server.js", "scripts": {"base-test": "ts-mocha --timeout 60000 ./test/base/*test.ts", "crypto-node-enc-test": "ts-mocha --timeout 60000 ./test/crypto/crypto-node-enc-test.ts", "crypto-sha1-test": "ts-mocha --timeout 60000 ./test/crypto/crypto-sha1-test.ts", "user-salt-test": "ts-mocha --timeout 60000 ./test/crypto/user-salt-test.ts", "save-file-and-modify-content": "ts-mocha --timeout 6000000 ./test/db-file/save-file-and-modify-content.ts", "upload-file-utils": "ts-mocha --timeout 6000000 ./test/db-file/upload-file-utils.ts", "write-read-encrypt-tests": "ts-mocha --timeout 6000000 ./test/db-file/write-read-encrypt-tests.ts", "decompress-test": "ts-mocha --timeout 6000000 ./test/decompress/decompress-test.ts", "testing-test": "ts-mocha --timeout 60000 ./test/testing-test.ts", "create-pdf-previews": "ts-mocha --timeout 60000 ./test/pdf-to-png/create-pdf-previews.ts", "test-annot-01": "ts-mocha --timeout 60000 ./test/pdf-annotation/annot-01-canvas-test.ts", "test-annot-gametheory": "ts-mocha --timeout 60000 ./test/pdf-annotation/gametheory-test.ts", "test-canvas-2pages": "ts-mocha --timeout 60000 ./test/pdf-annotation/canvas-2pages-test.ts", "meeting-detail-allcr-test": "ts-mocha --timeout 60000 ./test/webtests/meeting-detail-allcr-test.ts", "cr_results_byagenda_agg_all-test": "ts-mocha --timeout 60000 ./test/webtests/cr_results_byagenda_agg_all-test.ts", "annotation-upload-test": "ts-mocha --timeout 600000 ./test/webtests/annotation-upload-test.ts", "annotation-delete-test": "ts-mocha --timeout 60000 test/webtests/annotation-delete-test.ts", "remove-read-flag-test": "ts-mocha --timeout 6000000 ./test/webtests/remove-read-flag-test.ts", "app-properties-test": "ts-mocha --timeout 60000 ./test/webtests/app-properties-test.ts", "crashlog-test": "ts-mocha --timeout 60000 ./test/webtests/crashlog-test.ts", "health-check-test": "ts-mocha --timeout 60000 ./test/webtests/health-check-test.ts", "meeting-document-upload-test": "ts-mocha --timeout 600000 ./test/webtests/meeting-document-upload-test.ts", "mstart-servlet-controller-test": "ts-mocha --timeout 600000 ./test/webtests/mstart-servlet-controller-test.ts", "getkeys2-data-comparison-node-java": "ts-mocha --timeout 600000 test/webtests/getkeys2-data-comparison-node-java.ts", "getkeys2-ipad-sources-test": "ts-mocha --timeout 600000 ./test/webtests/getkeys2-ipad-sources-test.ts", "getkeys2-ipad-compression-test": "ts-mocha --timeout 600000 ./test/webtests/getkeys2-ipad-compression-test.ts", "getkeys2-tree": "ts-mocha --timeout 6000000 ./test/webtests/getkeys2-tree.ts", "getkeys2-sources": "ts-mocha --timeout 6000000 ./test/webtests/getkeys2-sources.ts", "m-docu2-test": "ts-mocha --timeout 600000 ./test/webtests/m-docu2-test.ts", "m-docu2-len-test": "ts-mocha --timeout 600000 ./test/webtests/m-docu2-len-test.ts", "m-docu2-single-test": "ts-mocha --timeout 6000000 ./test/webtests/m-docu2-single-test.ts", "m-docu2-appproperties-test": "ts-mocha --timeout 6000000 ./test/webtests/m-docu2-appproperties-test.ts", "m-docu2-oracle-test": "ts-mocha --timeout 6000000 ./test/webtests/m-docu2-oracle-test.ts", "preview-icon-test": "ts-mocha --timeout 6000000 ./test/webtests/preview-icon-test.ts", "ipad-resolution-test": "ts-mocha --timeout 6000000 ./test/webtests/ipad-resolution-test.ts", "sq-comparison-node-java-test": "ts-mocha --timeout 6000000 ./test/webtests/sq-comparison-node-java-test.ts", "cr_get_question_simple_all-test": "ts-mocha --timeout 60000 test/webtests/sq-comparison-cr_get_question_simple_all-test.ts", "test-get-salt": "ts-mocha --timeout 60000 ./test/mobile/test-get-salt.ts", "start": "ts-node ./src/server.ts", "dev": "ts-node ./src/server.ts", "build": "tsc", "build-local": "tsc", "postbuild": "copyfiles ./src/service/sql/**/* ./dist && copyfiles ./src/templates/**/* ./dist && copyfiles ./package.json ./dist/src/ && cd ./dist/src && npm install --omit=dev", "watch": "nodemon ./src/server.js", "format:all": "prettier --write .", "cpp": "copyfiles ./src/service/sql/**/* ./dist2", "server:lint:js": "eslint --ext .js,.ts,.tsx ./test", "server:fix:js": "npm run server:lint:js -- --fix", "test": "echo \"No tests specified\" && exit 0", "prepare": "husky"}, "resolutions": {"graceful-fs": "4.2.11"}, "dependencies": {"@napi-rs/canvas": "^0.1.53", "@okta/jwt-verifier": "3.2.2", "@sendgrid/mail": "8.1.1", "@types/passport": "1.0.17", "async": "3.2.6", "axios": "^1.7.8", "body-parser": "^1.20.3", "camelcase": "^5.3.1", "canvas": "2.11.2", "ci": "^2.3.0", "clamscan": "2.4.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cron": "^3.1.6", "decompress": "^4.2.1", "dompurify": "^3.2.4", "dotenv": "^16.0.3", "esp-azure": "file:", "express": "^4.21.1", "express-fileupload": "^1.5.0", "express-rate-limit": "7.4.1", "express-session": "1.18.1", "helmet": "7.1.0", "http-status-codes": "2.2.0", "husky": "9.0.11", "iconv-lite": "^0.6.3", "json5": "^2.2.1", "jsonwebtoken": "9.0.2", "jszip": "^3.10.1", "jwt-decode": "^3.1.2", "lodash": "^4.17.20", "mime": "^3.0.0", "mime-types": "2.1.24", "moment": "2.29.4", "morgan": "^1.10.0", "mustache": "^4.2.0", "node-cache": "5.1.2", "node-device-detector": "2.1.0", "node-jose": "^2.1.1", "numeral": "^2.0.6", "pako": "^2.1.0", "passport": "0.7.0", "passport-azure-ad": "4.3.5", "pdf-lib": "^1.17.1", "pdf-to-image-generator": "1.0.1", "pdfjs-dist": "4.8.69", "pg": "8.8.0", "pg-copy-streams": "^5.1.1", "pg-hstore": "2.3.4", "pg-types": "4.0.2", "properties-reader": "^2.1.1", "querystring": "^0.2.1", "semver": "^7.6.2", "socket.io-client": "^4.5.1", "swagger-ui-express": "4.6.3", "tweetnacl-util": "^0.15.1", "underscore": "^1.13.4", "velocityjs": "2.0.6", "winston": "^3.11.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.2/xlsx-0.20.2.tgz", "xss": "^1.0.14"}, "devDependencies": {"@types/async": "3.2.24", "@types/chai": "^4.3.4", "@types/clamscan": "2.4.0", "@types/cookie-parser": "1.4.7", "@types/cors": "^2.8.12", "@types/decompress": "^4.2.7", "@types/dompurify": "^3.0.5", "@types/express": "4.17.14", "@types/express-fileupload": "^1.2.2", "@types/express-session": "1.18.1", "@types/jest": "^29.5.12", "@types/js-yaml": "^4.0.5", "@types/lodash": "4.14.188", "@types/mime": "^3.0.1", "@types/mocha": "^10.0.1", "@types/morgan": "~1.9.1", "@types/mustache": "^4.2.5", "@types/mysql": "^2.15.21", "@types/node": "^16.18.10", "@types/node-jose": "^1.1.10", "@types/pako": "^2.0.3", "@types/passport-azure-ad": "4.3.6", "@types/pg": "^8.6.5", "@types/ua-parser-js": "0.7.37", "@types/xml2js": "^0.4.9", "@typescript-eslint/eslint-plugin": "7.13.1", "@typescript-eslint/parser": "7.13.1", "babel-eslint": "10.1.0", "chai": "^4.2.0", "copyfiles": "^2.4.1", "eslint": "8.57.0", "eslint-config-prettier": "^6.10.1", "eslint-plugin-prettier": "^3.1.4", "jest": "29.7.0", "mocha": "^10.4.0", "mocha-typescript": "^1.1.17", "nodemon": "^3.1.2", "prettier": "2.7.1", "ts-jest": "29.1.4", "ts-mocha": "^10.0.0", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^4.9.4"}, "overrides": {"rimraf": "5.0.7", "string-width-cjs": "5.1.1", "strip-ansi-cjs": "8.0.0", "wrap-ansi-cjs": "8.0.0", "ws": "8.17.1"}}