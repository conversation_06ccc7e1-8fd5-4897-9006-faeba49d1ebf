import { test } from 'mocha';
import { localUrlJava, localUrlNode } from '../common';
import { runGetBlob } from './getkeys2-utils';
import { expect } from 'chai';
import { expectBufferEquals } from '../test-utils';

const docu2UrlNode = `${localUrlNode}mobile/docu2/`;
const docu2UrlJava = `${localUrlJava}mobile/docu2/`;
const fileTid = 141298982;

const testname = `(webtests) m-docu2-single-test`;
test(testname, async () => {
  console.log(`Start ${testname} get: ${fileTid}!`);

  const docu2UrlFileTidListNode = `${docu2UrlNode}?fileTidList=${fileTid}`;
  const docu2ResultNode = await runGetBlob(docu2UrlFileTidListNode, '');
  const lenNode = docu2ResultNode.length;
  console.debug(`len node: ${lenNode}`);
  expect(lenNode).gt(10, `<PERSON><PERSON> failed for ${fileTid}!`);

  const docu2UrlFileTidListJava = `${docu2UrlJava}?fileTidList=${fileTid}`;
  const docu2ResultJava = await runGetBlob(docu2UrlFileTidListJava, '');
  const lenJava = docu2ResultJava.length;
  console.debug(`len java: ${lenJava}`);
  expect(lenJava).gt(10, `Java failed for ${fileTid}!`);

  console.debug(`len node: ${lenNode}`);
  expect(lenNode).gt(10, `Node failed for ${fileTid}!`);

  expect(lenNode).equals(lenJava, `Length of ${fileTid} result is not the same!`);

  expectBufferEquals(docu2ResultNode, docu2ResultJava);
});
