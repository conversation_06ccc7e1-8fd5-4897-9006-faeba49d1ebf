--
-- KEY           = JavaLogManager.setLevel
-- ACCESS_GROUPS = ESP_ADMIN
-- parameters: filter, action (setLevel, info, ...), level (WARNING, FINE, SEVERE, INFO, ...)
--
set:action=setLevel
;
java:com.swissre.serviceQuery.JavaLogManager
;


--
-- KEY           = JavaLogManager.info
--

set:action=info
;
java:com.swissre.serviceQuery.JavaLogManager
;

--
-- KEY           = JavaLogManager.updateByAppProperties
-- ACCESS_GROUPS = SYSTEM
--
set:action=updateByAppProperties
;
java:com.swissre.serviceQuery.JavaLogManager
;
