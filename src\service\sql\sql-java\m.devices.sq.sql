
--
--
--  KEY           = m.devices.select
--  ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--  
--

set-if-empty:userId=%
;
set-if-empty:deviceId=%
;
set-if-empty:deviceIdExclude=%
;
set-if-empty:version=%
;
set-if-empty:deviceType=%
;
set-if-empty:userName=%
;
select d.USER_ID,
       d.DEVICE_ID,
       d.VERSION,
       coalesce(u.LAST_NAME, '-na-') || ', ' || coalesce(u.FIRST_NAME, '-na-') as USER_NAME,
       d.DEVICE_TYPE,
       d.UPDATED,
       d.IOS_VERSION
from ESP.T_DEVICES d
         left join ESP.V_SR_USER_ALL u on d.USER_ID = u.USER_ID
where  d.USER_ID like :userId
  and d.DEVICE_ID like :deviceId
  and (d.VERSION like :version or d.VERSION is null)
  and (d.DEVICE_TYPE like :deviceType or d.DEVICE_TYPE is null)
  and (u.FIRST_NAME like :userName or u.LAST_NAME like :userName)
order by d.USER_ID
;
  
  
--
-- KEY           = m.start.saveDeviceInfo
-- ACCESS_GROUPS = SYSTEM
--

set-if-empty:deviceType=NA
;
INSERT INTO ESP.T_DEVICES (USER_ID, DEVICE_ID, VERSION, DEVICE_TYPE, IOS_VERSION, UPDATED)
VALUES (:userId, :deviceId, :version, :deviceType, :iosVersion, :$CURRENTMILLIS)
ON CONFLICT (USER_ID, DEVICE_ID)
DO UPDATE SET VERSION = EXCLUDED.VERSION, DEVICE_TYPE = EXCLUDED.DEVICE_TYPE, IOS_VERSION = EXCLUDED.IOS_VERSION, UPDATED = EXCLUDED.UPDATED;
select * from ESP.T_DEVICES 
where USER_ID = :userId 
and DEVICE_ID = :deviceId     
;