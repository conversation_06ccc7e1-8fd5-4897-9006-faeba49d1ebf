import { sqExceptionResult, SqNodeFunction, SqResult } from '../../../serviceQuery';
import LoggerCenter from '../../../logger/LoggerCenter';
import path from 'path';
import { ANONYMOUS } from '../../constants';
import { KeyProcessor2 } from './KeyProcessor2';
import { StringRecord } from '../../../types';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export const Getkeys2: SqNodeFunction = async function({ request }): Promise<SqResult> {
  logger.debug('process Getkeys2');
  const { parameters, userId = ANONYMOUS, roles } = request;
  const strParams = Object.entries(parameters).reduce<StringRecord>((a, [name, value]) => {
    a[name] = (value ?? '').toString();
    return a;
  }, {});

  const name = strParams.name;
  const sourcesOnly = strParams.sourcesOnly === 'true';

  const kp = new KeyProcessor2({ userId, roles });

  kp.setName(name);
  kp.setSourcesOnly(sourcesOnly);

  const inParameters: StringRecord = {};
  const outParameters: StringRecord = {};

  inParameters.mode = strParams.mode;
  let pr: SqResult | undefined = {};
  try {
    pr = await kp.processKeydocumentAsPagingResult(inParameters, outParameters);
  } catch (e) {
    pr = sqExceptionResult(e);
  }
  return pr;
};
