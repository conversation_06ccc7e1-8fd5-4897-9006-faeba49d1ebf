import LoggerCenter from '../../logger/LoggerCenter';
import path from 'path';
import ServiceCenter from '../ServiceCenter';
import { stopAllJobs } from '../scheduler-utils';

const logger = LoggerCenter.getLogger(path.basename(__filename));

export interface RestartConfig {
  gracefulShutdownTimeout: number; // milliseconds
  restartDelay: number; // milliseconds
  maxRestartAttempts: number;
  restartCooldown: number; // milliseconds between restart attempts
}

export class AutoRestartManager {
  private static instance: AutoRestartManager;
  private config: RestartConfig;
  private restartAttempts = 0;
  private lastRestartTime: Date | null = null;
  private isRestarting = false;

  private constructor() {
    this.config = this.loadConfig();
    this.setupProcessHandlers();
    logger.info('AutoRestartManager initialized with config:', this.config);
  }

  public static getInstance(): AutoRestartManager {
    if (!AutoRestartManager.instance) {
      AutoRestartManager.instance = new AutoRestartManager();
    }
    return AutoRestartManager.instance;
  }

  private loadConfig(): RestartConfig {
    return {
      gracefulShutdownTimeout: parseInt(process.env.GRACEFUL_SHUTDOWN_TIMEOUT || '30000'), // 30 seconds
      restartDelay: parseInt(process.env.RESTART_DELAY || '5000'), // 5 seconds
      maxRestartAttempts: parseInt(process.env.MAX_RESTART_ATTEMPTS || '3'),
      restartCooldown: parseInt(process.env.RESTART_COOLDOWN || '300000') // 5 minutes
    };
  }

  private setupProcessHandlers(): void {
    // Handle graceful shutdown signals
    process.on('SIGTERM', () => {
      logger.info('Received SIGTERM signal, initiating graceful shutdown...');
      this.gracefulShutdown('SIGTERM');
    });

    process.on('SIGINT', () => {
      logger.info('Received SIGINT signal, initiating graceful shutdown...');
      this.gracefulShutdown('SIGINT');
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception:', error);
      this.gracefulShutdown('uncaughtException');
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled promise rejection at:', promise, 'reason:', reason);
      this.gracefulShutdown('unhandledRejection');
    });
  }

  public async initiateRestart(reason: string): Promise<void> {
    if (this.isRestarting) {
      logger.warn('Restart already in progress, ignoring restart request');
      return;
    }

    // Check restart cooldown
    if (this.lastRestartTime) {
      const timeSinceLastRestart = Date.now() - this.lastRestartTime.getTime();
      if (timeSinceLastRestart < this.config.restartCooldown) {
        logger.warn(`Restart cooldown active. ${Math.ceil((this.config.restartCooldown - timeSinceLastRestart) / 1000)}s remaining`);
        return;
      }
    }

    // Check max restart attempts
    if (this.restartAttempts >= this.config.maxRestartAttempts) {
      logger.error(`Maximum restart attempts (${this.config.maxRestartAttempts}) reached. Manual intervention required.`);
      return;
    }

    this.isRestarting = true;
    this.restartAttempts++;
    this.lastRestartTime = new Date();

    logger.error(`Initiating restart attempt ${this.restartAttempts}/${this.config.maxRestartAttempts}. Reason: ${reason}`);

    try {
      await this.performRestart();
    } catch (error) {
      logger.error('Restart failed:', error);
      this.isRestarting = false;
    }
  }

  private async performRestart(): Promise<void> {
    logger.info('Starting graceful shutdown for restart...');

    // Perform graceful shutdown
    await this.gracefulShutdown('restart', false);

    // Wait before restarting
    logger.info(`Waiting ${this.config.restartDelay}ms before restart...`);
    await this.sleep(this.config.restartDelay);

    // Restart the process
    this.restartProcess();
  }

  private async gracefulShutdown(reason: string, exitProcess = true): Promise<void> {
    logger.info(`Graceful shutdown initiated. Reason: ${reason}`);

    const shutdownPromise = this.performShutdownTasks();
    const timeoutPromise = this.sleep(this.config.gracefulShutdownTimeout);

    try {
      // Race between shutdown tasks and timeout
      await Promise.race([shutdownPromise, timeoutPromise]);
      logger.info('Graceful shutdown completed');
    } catch (error) {
      logger.error('Error during graceful shutdown:', error);
    }

    if (exitProcess) {
      logger.info('Exiting process...');
      process.exit(0);
    }
  }

  private async performShutdownTasks(): Promise<void> {
    const tasks: Promise<void>[] = [];

    // Stop all scheduled jobs
    try {
      logger.info('Stopping all scheduled jobs...');
      stopAllJobs();
    } catch (error) {
      logger.error('Error stopping scheduled jobs:', error);
    }

    // Close database connections
    try {
      logger.info('Closing database connections...');
      const sc = ServiceCenter.getInstance();
      tasks.push(sc.destroy());
    } catch (error) {
      logger.error('Error closing database connections:', error);
    }

    // Wait for all shutdown tasks to complete
    await Promise.allSettled(tasks);
    logger.info('All shutdown tasks completed');
  }

  private restartProcess(): void {
    logger.info('Restarting process...');
    
    // In a containerized environment, we exit and let the container orchestrator restart us
    // In a PM2 environment, PM2 will handle the restart
    // For development, we can use process.exit() and rely on nodemon or similar tools
    
    if (process.env.NODE_ENV === 'production') {
      // In production, exit and let the container orchestrator restart
      process.exit(1);
    } else {
      // In development, try to restart using spawn if possible
      const { spawn } = require('child_process');
      const args = process.argv.slice(1);
      
      logger.info('Spawning new process:', process.execPath, args);
      
      const child = spawn(process.execPath, args, {
        detached: true,
        stdio: 'inherit'
      });
      
      child.unref();
      process.exit(0);
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  public getStatus() {
    return {
      isRestarting: this.isRestarting,
      restartAttempts: this.restartAttempts,
      lastRestartTime: this.lastRestartTime,
      config: this.config
    };
  }

  public resetRestartAttempts(): void {
    this.restartAttempts = 0;
    this.lastRestartTime = null;
    logger.info('Restart attempts counter reset');
  }
}
