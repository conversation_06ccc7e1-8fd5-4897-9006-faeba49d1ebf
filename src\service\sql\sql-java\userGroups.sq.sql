--
-- KEY           = userGroups.select
-- ACCESS_GROUPS = ESP_ADMIN,SYSTEM
--

set-if-empty:userId=%
;
set-if-empty:userGroup=%
;
set-if-empty:firstName=%
;
set-if-empty:lastName=%
;
select 
USER_ID, 
USER_GROUP,
first_name,
last_name
FROM 
ESP.V_USAGE_GROUPS
where upper(USER_ID) like upper(:userId)
and upper(USER_GROUP) like upper(:userGroup) 
and upper(first_name) like upper(:firstName) 
and upper(last_name) like upper(:lastName) 

--
-- KEY           = userGroups.get
-- ACCESS_GROUPS = ESP_ADMIN
--

select USER_ID,USER_GROUP,FIRST_NAME,LAST_NAME
from ESP.V_USAGE_GROUPS  
where UPPER(USER_ID) like UPPER(:userId)


--
-- KEY           = userGroups.update
-- ACCESS_GROUPS = ESP_ADMIN
--
delete from ESP.T_USER_GROUP where USER_ID = :userId
;
insert into ESP.T_USER_GROUP (USER_ID,USER_GROUP) values (:userId, :userGroup)
;



--
-- KEY           = userGroups.delete
-- ACCESS_GROUPS = ESP_ADMIN
--

delete from ESP.T_USER_GROUP where USER_ID = :userId



--
-- KEY           = userGroups.insert
-- ACCESS_GROUPS = ESP_ADMIN
--

insert into ESP.T_USER_GROUP (USER_ID, USER_GROUP) VALUES (:userId,:userGroup)

