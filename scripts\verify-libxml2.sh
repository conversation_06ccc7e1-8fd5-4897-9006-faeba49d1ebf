#!/bin/bash

# <PERSON>ript to verify libxml2 version and security fix
echo "=== libxml2 Security Verification ==="

# Check if libxml2 is installed
if command -v xml2-config &> /dev/null; then
    echo "✓ libxml2 is installed"
    
    # Get version
    VERSION=$(xml2-config --version)
    echo "Current version: $VERSION"
    
    # Check package version (Debian/Ubuntu)
    if command -v dpkg &> /dev/null; then
        PACKAGE_VERSION=$(dpkg -l | grep libxml2 | awk '{print $3}' | head -1)
        echo "Package version: $PACKAGE_VERSION"
        
        # Check if version meets security requirements
        if [[ "$PACKAGE_VERSION" == *"2.9.14+dfsg-1.3~deb12u2"* ]] || [[ "$PACKAGE_VERSION" > "2.9.14+dfsg-1.3~deb12u2" ]]; then
            echo "✓ libxml2 version meets security requirements"
        else
            echo "⚠ libxml2 version may be vulnerable. Please upgrade to 2.9.14+dfsg-1.3~deb12u2 or higher"
        fi
    fi
    
    # Check package version (CentOS/RHEL)
    if command -v rpm &> /dev/null; then
        PACKAGE_VERSION=$(rpm -q libxml2)
        echo "Package version: $PACKAGE_VERSION"
    fi
    
else
    echo "✗ libxml2 is not installed or xml2-config not found"
fi

echo "=== End Verification ==="
