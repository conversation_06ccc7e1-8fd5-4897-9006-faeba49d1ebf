import { PDFPage } from 'pdf-lib';
import { DrawOperation } from '../types';
import { getPoints } from '../draw-utils';
import { Canvas } from 'canvas';

const WIDTH_ERASER = 12;

export function drawEraserCanvas(page: PDFPage, object: DrawOperation, canvas: Canvas) {
  const scale = object.objectMeta.scale ?? 1;
  const ctx = canvas.getContext('2d');
  const { height } = page.getSize();
  const points = getPoints(object.points);

  points.forEach(([x, y], i) => {
    if (i === 0) {
      ctx.beginPath();
      ctx.lineWidth = WIDTH_ERASER * scale;
      ctx.strokeStyle = '#ffffff';
      ctx.moveTo(x, height - y);
    } else {
      ctx.lineTo(x, height - y);
    }
  });
  ctx.stroke();
}
